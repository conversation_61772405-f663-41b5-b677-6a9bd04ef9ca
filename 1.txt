获取签到id:
GET /designcenter/activity/216652/wechat/1146457634812837889.json?t=29230793&appid=wx3423ef0c7b7f19af HTTP/2
host: images.qmai.cn
qm-user-token: iIJXUQt0YY0BRu-TulJR5Odyzt21XHbLd049Y3D4ZTolwvTsu7RjDHRAaTMdtxwQb_Gs6zZX5q_YN-SLjeRnwg
store-id: 216652
accept-language: zh-CN
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
qm-from: wechat
content-type: application/json
accept: v=1.0
xweb_xhr: 1
qm-from-type: catering
sec-fetch-site: cross-site
sec-fetch-mode: cors
sec-fetch-dest: empty
referer: https://servicewechat.com/wx3423ef0c7b7f19af/77/page-frame.html
accept-encoding: gzip, deflate, br
priority: u=1, i

HTTP/2 200
server: Tengine
content-type: application/json
content-length: 335
vary: Origin
vary: Access-Control-Request-Method
vary: Access-Control-Request-Headers
date: Wed, 30 Jul 2025 03:53:50 GMT
x-oss-request-id: 6889974EC6DD403135D32B29
x-oss-cdn-auth: fail
accept-ranges: bytes
etag: "DBB89D2EF5D212FB4733E441979BAADB"
last-modified: Tue, 22 Jul 2025 03:55:55 GMT
x-oss-object-type: Normal
x-oss-hash-crc64ecma: 9432539004115250703
x-oss-storage-class: Standard
content-md5: 27idLvXSEvtHM+RBl5uq2w==
x-oss-server-time: 17
via: cache32.l2cn3008[45,45,200-0,M], cache36.l2cn3008[47,0], vcache3.cn5158[68,67,200-0,M], vcache6.cn5158[70,0]
ali-swift-global-savetime: 1753847630
x-cache: MISS TCP_MISS dirn:-2:-2
x-swift-savetime: Wed, 30 Jul 2025 03:53:50 GMT
x-swift-cachetime: 60
cache-control: public, max-age=5184000
access-control-allow-origin: *
timing-allow-origin: *
eagleid: 77274a2417538476308977716e

{"activityData":{"id":"1146457634812837889","name":"签到有礼(7月)"},"style":1,"bgImg":"https://images.qmai.cn/s216652/2025/07/22/e5416803b9fdc30975.jpg","bgColor":"rgba(253, 186, 197, 1)","btnBgColor":"rgba(253, 186, 197, 1)","fontColor":"rgba(243, 241, 241, 1)","calendarIcon":"","awardIcon":"","showPoints":true,"components":[]}

签到：
POST /web/cmk-center/sign/takePartInSign HTTP/2
host: webapi.qmai.cn
content-length: 423
qm-user-token: iIJXUQt0YY0BRu-TulJR5Odyzt21XHbLd049Y3D4ZTolwvTsu7RjDHRAaTMdtxwQb_Gs6zZX5q_YN-SLjeRnwg
store-id: 216652
accept-language: zh-CN
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
qm-from: wechat
content-type: application/json
accept: v=1.0
xweb_xhr: 1
qm-from-type: catering
sec-fetch-site: cross-site
sec-fetch-mode: cors
sec-fetch-dest: empty
referer: https://servicewechat.com/wx3423ef0c7b7f19af/77/page-frame.html
accept-encoding: gzip, deflate, br
priority: u=1, i

{"activityId":"1146457634812837889","storeId":"216652","appid":"wx3423ef0c7b7f19af","timestamp":"1753847631850","signature":"50B59044AA7561A0CD79C109567E072E","v":1,"data":"RbvsFVlYyI+JVj8uHHzZL895S4OEcpoFXW0UiaMpncg1sa7inrpFKDA9sJH7WT8impsHWzvGAlHiWUF3Khv4usTlpMJPVJ1B8/GilrwxYAo20FS4teRaLXCeJSAEvEoWqp5QEe69yeNMLsw+S0Ihkmm5rIWuQ+HabmhC64XXkonqBq12PogkjV5Fec/InbM+KoOWX6N3rFRFFUND7IGyv0dehtzNcYGo77pGTbb9DqQ=","version":1}
HTTP/2 200
date: Wed, 30 Jul 2025 03:53:52 GMT
content-type: application/json;charset=UTF-8
content-length: 201
set-cookie: acw_tc=ac11000117538476324723153e00550af3c70c5d7963c3a7066615ced640b4;path=/;HttpOnly;Max-Age=1800
vary: Origin
vary: Access-Control-Request-Method
vary: Access-Control-Request-Headers
vary: Origin
vary: Access-Control-Request-Method
vary: Access-Control-Request-Headers
strict-transport-security: max-age=********

{"code":0,"data":{"activityId":"1146457634812837889","activityType":"YX_4","rewardDetailList":[],"userId":"1157283994981785601"},"message":"ok","status":true,"trace_id":"pdW07302zW8sK8083c3805de0486e"}

登录,body里面的code应该是phonecode:
POST /web/account-center/oauth/bind-mobile HTTP/2
host: webapi.qmai.cn
content-length: 201
qm-user-token: AE4xSe1ViDngjcdR6bK8zoisE15u7m-kDKiyBFWm8b8fh9p_305tSa8uInKgu4cqereEtN1G4BipB31QbpYLYA
store-id: 216652
accept-language: zh-CN
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
qm-from: wechat
content-type: application/json
accept: v=1.0
xweb_xhr: 1
qm-from-type: catering
sec-fetch-site: cross-site
sec-fetch-mode: cors
sec-fetch-dest: empty
referer: https://servicewechat.com/wx3423ef0c7b7f19af/77/page-frame.html
accept-encoding: gzip, deflate, br
priority: u=1, i

{"code":"4b80d5f1220dbcbda33e2dc2823c57336ece99179e29bca7ec07f0fa88b6935e","reg_activity_source":0,"is_update_mobile":0,"channel_code":"","flowScene":1145,"eVersion":"1.0","appid":"wx3423ef0c7b7f19af"}
HTTP/2 200
date: Wed, 30 Jul 2025 03:53:36 GMT
content-type: application/json;charset=UTF-8
content-length: 927
set-cookie: acw_tc=ac11000117538476164018549e0079a56920a4b26282d214ef3890520af4b6;path=/;HttpOnly;Max-Age=1800
vary: Origin
vary: Access-Control-Request-Method
vary: Access-Control-Request-Headers
strict-transport-security: max-age=********

{"code":0,"data":{"countryCode":"86","data":null,"eMobile":null,"gateway_action":"auth_cache_update","loginToken":{"data":null,"store":{"contact_number":"","contact_person":"","id":"216652","logo":"https://images.qmai.cn/s216652/2025/07/16/a3a0b57ab49887bee8.png","name":"爷爷不泡茶","service_tel":"","store_type":66},"token":"iIJXUQt0YY0BRu-TulJR5Odyzt21XHbLd049Y3D4ZTolwvTsu7RjDHRAaTMdtxwQb_Gs6zZX5q_YN-SLjeRnwg","user":{"app_id":null,"avatar":"","eMobile":"OSadgndezxpDgTFT5VRiWA==","entity_card_qty":0,"id":"1157283994981785601","is_open_webank":0,"mobile":"181****5627","nickname":"","open_webank_time":0,"openid":"of5Ly4u525yXCGdT6emzRFOm-1iQ","personal_recommend_setting":null,"unionid":"o9_b46YGzTYEgphNp_kMgv7CAkOc","username":""}},"mobile":"181****5627","newGroupRegister":false,"phoneNumber":"181****5627","purePhoneNumber":"181****5627"},"message":"ok","status":true,"trace_id":"pdW07302zRoUq64b5548d8c7343c8"}
