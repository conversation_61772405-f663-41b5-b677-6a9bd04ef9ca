{"subPackages": [{"root": "pluginMall"}, {"root": "pluginPoint"}, {"root": "pluginPages"}, {"root": "pluginDine"}, {"root": "pluginMedal"}, {"root": "pluginUser"}, {"root": "pluginQueue"}, {"root": "pluginFeedback"}, {"root": "subpackages/tabbar-pages/takefood"}, {"root": "subpackages/tabbar-pages/order-list"}, {"root": "subpackages/tabbar-pages/pay"}, {"root": "async-components"}, {"root": "async-components2"}, {"root": "async-libs"}, {"root": "open"}, {"root": "subpackages/tabbar-pages/user"}, {"root": "subpackages/tabbar-pages/index"}, {"root": "subpackages/tabbar-pages/page"}, {"root": "pluginMarketing"}, {"root": "subpackages/bargain"}, {"root": "subpackages/old-bring-new"}, {"root": "subpackages/invite-for-gift"}, {"root": "subpackages/integralDraw"}, {"root": "subpackages/treasure"}, {"root": "subpackages/spellGroup"}, {"root": "subpackages/questionAnswer"}, {"root": "subpackages/questionAnswerNew"}, {"root": "subpackages/blindBoxLottery"}, {"root": "subpackages/spell-coupon"}, {"root": "subpackages/password-envelope"}, {"root": "subpackages/outbreak-card"}, {"root": "subpackages/split-coupons"}, {"root": "subpackages/queneUp"}, {"root": "pages/multi"}, {"root": "subpackages/protocol"}, {"root": "subpackages/address"}, {"root": "subpackages/user-equity"}, {"root": "subpackages/asset-merge"}, {"root": "subpackages/bgImage"}, {"root": "subpackages/gift-card"}, {"root": "subpackages/premium-membership"}, {"root": "subpackages/sign-in"}, {"root": "subpackages/value-card"}, {"root": "subpackages/refund"}, {"root": "subpackages/wishMatch"}, {"root": "subpackages/confirm"}, {"root": "subpackages/detail"}, {"root": "subpackages/evaluate"}, {"root": "subpackages/select-coupon"}, {"root": "subpackages/coupon"}, {"root": "subpackages/transform"}, {"root": "subpackages/exchange-coupon"}, {"root": "pages/pay/success"}, {"root": "pages/user/coupon"}, {"root": "pages/user/info"}, {"root": "subpackages/user"}, {"root": "pages/coupon"}, {"root": "subpackages/invoice"}, {"root": "subpackages/pinDetails"}, {"root": "subpackages/pinDetailsShare"}, {"root": "subpackages/suggestionFeedback"}, {"root": "subpackages/marketing-detail"}, {"root": "subpackages/familyMemberCard"}, {"root": "subpackages/third-equity"}, {"root": "subpackages/category-goods"}, {"root": "subpackages/user-task-center"}, {"root": "subpackages/exchange-goods"}, {"root": "subpackages/group-restaurant"}, {"root": "subpackages/group-purchase-coupon"}, {"root": "subpackages/enterprise-wechat"}, {"root": "subpackages/lottery-reward"}, {"root": "subpackages/middlePage"}, {"root": "subpackages/login-auth"}, {"root": "subpackages/coupon-goods-list"}, {"root": "subpackages/site-pages"}, {"root": "subpackages/strongestDecibel"}, {"root": "subpackages/live-player"}, {"root": "plugins", "plugins": {"materialPlugin": {"version": "1.0.5", "provider": "wx4d2de<PERSON>b3aed6e5a", "subpackage": "/plugins/"}, "sendCoupon": {"version": "latest", "provider": "wxf3f436ba9bd4be7b", "subpackage": "/plugins/"}}}, {"root": "plugins2", "plugins": {"studentVerify": {"version": "latest", "provider": "wxa16657d57059e0f0", "subpackage": "/plugins2/"}}}, {"root": "subpackages/ar-cheers"}, {"root": "/__wx__/", "independent": true}], "entryPagePath": "pages/index/index.html", "pages": ["pages/index/index", "subpackages/payment-code/index", "subpackages/license/index", "subpackages/webView/index", "subpackages/privacyNumPage/index", "subpackages/errorPage/index", "subpackages/pay-gift-other-rights/index", "subpackages/delivery-detail/index", "pages/user/index", "pages/takefood/index", "pages/takeout/index", "pages/page/page", "pages/duibaPay/duibaPay", "pages/duibaRedirect/duibaRedirect", "pages/page/p1/index", "pages/page/p2/index", "pages/page/p3/index", "pages/page/p4/index", "pages/page/p5/index", "pages/pluginMall/index", "pages/webView/index", "pages/user/qualification/index", "pages/pay/index/index", "pages/pay/record/index", "pages/pay/detail/index", "pages/order/list/index", "pages/order/remark/index", "pluginMall/error/index", "pluginMall/webview/index", "pluginMall/index/index", "pluginMall/detail/index", "pluginMall/classify/index", "pluginMall/cart/index", "pluginMall/order-confirm/index", "pluginMall/order-list/index", "pluginMall/order-detail/index", "pluginMall/logistics-detail/index", "pluginMall/logistics-form/index", "pluginMall/after-sale-select/index", "pluginMall/after-sale-detail/index", "pluginMall/after-sale-form/index", "pluginMall/address-list/index", "pluginMall/address-edit/index", "pluginMall/invoicing-detail/index", "pluginMall/distribution-index/index", "pluginMall/distribution-apply/index", "pluginMall/distribution-invite/index", "pluginMall/distribution-junior/index", "pluginMall/distribution-withdraw/index", "pluginMall/distribution-withdraw-record/index", "pluginMall/distribution-order/index", "pluginMall/distribution-goods/index", "pluginMall/pay-result/index", "pluginMall/order-change/index", "pluginMall/store-select/index", "pluginMall/city-select/index", "pluginPoint/index/index", "pluginPoint/record/index", "pluginPoint/rule/index", "pluginPoint/exchange-record/index", "pluginPoint/exchange-coupon/index", "pluginPoint/exchange-evaluate/index", "pluginPoint/exchange-success/index", "pluginPoint/exchange-order-confirm/index", "pluginPoint/exchange-confirm/index", "pluginPoint/entity-exchange-success/index", "pluginPoint/logistics/index", "pluginPoint/refund-apply/index", "pluginPoint/refund-list/index", "pluginPoint/refund-detail/index", "pluginPoint/pay-result/index", "pluginPoint/webview/index", "pluginPages/login-guide/index", "pluginPages/money-saving-calc/index", "pluginPages/store-account/index", "pluginPages/transaction-records/index", "pluginPages/applicable-stores/index", "pluginPages/mall-invoice-apply/index", "pluginPages/mall-invoice-detail/index", "pluginPages/goods-energy-calculation/index", "pluginPages/apply-stores/index", "pluginPages/apply-goods/index", "pluginPages/apply-rights-cards/index", "pluginPages/apply-gift-cards/index", "pluginPages/custom-web-view/index", "pluginPages/miniapp-bridge/index", "pluginDine/address-add/index", "pluginDine/address-edit/index", "pluginDine/address-select/index", "pluginDine/location-select/index", "pluginDine/goods-list/index", "pluginDine/goods-package-detail/index", "pluginDine/goods-search/index", "pluginDine/goods-packing-fee-detail/index", "pluginDine/order-confirm/index", "pluginDine/order-detail/index", "pluginDine/order-list/index", "pluginDine/order-remark/index", "pluginDine/refund-apply/index", "pluginDine/refund-detail/index", "pluginDine/refund-list/index", "pluginDine/shop-select/index", "pluginDine/shop-cert/service-qualification", "pluginDine/shop-cert/food-safety-file", "pluginDine/shop-activity/detail", "pluginDine/invoice-detail/index", "pluginDine/invoice-select/index", "pluginDine/invoice-add/index", "pluginDine/invoice-apply/index", "pluginMedal/index/index", "pluginMedal/medal-detail/index", "pluginMedal/task/index", "pluginMedal/task-rewards/index", "pluginUser/index", "pluginUser/account-mgmt/index", "pluginUser/account-switch/index", "pluginUser/account-log-off/index", "pluginUser/log-off-agreement/index", "pluginUser/assets-move/index", "pluginUser/assets-move-records/index", "pluginQueue/index", "pluginFeedback/index/index", "pluginFeedback/add/index", "subpackages/tabbar-pages/takefood/index", "subpackages/tabbar-pages/order-list/index", "subpackages/tabbar-pages/pay/index", "async-components/index", "async-components2/index", "async-libs/index", "open/index", "subpackages/tabbar-pages/user/index", "subpackages/tabbar-pages/index/index", "subpackages/tabbar-pages/page/index", "pluginMarketing/common/reward-template-detail", "pluginMarketing/common/web-view/index", "pluginMarketing/common/my-reward/index", "pluginMarketing/common/my-reward/coupon-package-detail", "pluginMarketing/common/goods/index", "pluginMarketing/common/goods/common-goods", "pluginMarketing/common/store/index", "pluginMarketing/common/source/index", "pluginMarketing/overlord-meal/index", "pluginMarketing/overlord-meal/share", "pluginMarketing/identify-customer-code/index", "pluginMarketing/decibel/index", "pluginMarketing/puzzle/index", "pluginMarketing/puzzle/give-record", "pluginMarketing/puzzle/get-pieces", "pluginMarketing/ladder-coupons/index", "pluginMarketing/lottery/index/index", "pluginMarketing/lottery/share/index", "pluginMarketing/lottery/join/index", "pluginMarketing/lottery/rule/index", "pluginMarketing/lottery/equity/index", "pluginMarketing/checkin/index/index", "pluginMarketing/checkin/my-reward/index", "pluginMarketing/receive/index/index", "pluginMarketing/receive/list/index", "pluginMarketing/receive/receive-result/index", "pluginMarketing/receive/password-rule/index", "pluginMarketing/partition-coupon/index/index", "pluginMarketing/partition-coupon/team-record/index", "pluginMarketing/point-exchange/index/index", "pluginMarketing/point-exchange/detail/index", "pluginMarketing/point-exchange/my-reward/index", "pluginMarketing/point-exchange/goods/index", "pluginMarketing/point-exchange/store/index", "pluginMarketing/bridge/subscribe/index", "pluginMarketing/bridge/navigate/index", "pluginMarketing/bridge/crowd/index", "pluginMarketing/bridge/authorization/index", "pluginMarketing/collect-card/index/index", "pluginMarketing/collect-card/get-card/index", "pluginMarketing/collect-card/my-card/index", "pluginMarketing/collect-card/give-record/index", "pluginMarketing/nurture/index/index", "pluginMarketing/invite/index/index", "pluginMarketing/invite/share/index", "pluginMarketing/invite/my-invite/index", "pluginMarketing/guess/index/index", "pluginMarketing/guess/record/index", "pluginMarketing/guess/reward/index", "pluginMarketing/exchange/index/index", "pluginMarketing/activity-center/index/index", "pluginMarketing/activity-center/detail/index", "pluginMarketing/collect-img/index/index", "pluginMarketing/collect-img/give-record/index", "pluginMarketing/scratchcard/share/index", "pluginMarketing/scratchcard/index/index", "pluginMarketing/funny-synthesis/index/index", "pluginMarketing/funny-synthesis/give-record/index", "subpackages/bargain/list/index", "subpackages/bargain/equity-card/index", "subpackages/bargain/equity-order/index", "subpackages/bargain/activity/index", "subpackages/bargain/record/index", "subpackages/old-bring-new/index/index", "subpackages/old-bring-new/help/index", "subpackages/old-bring-new/rule/index", "subpackages/old-bring-new/reward/index", "subpackages/old-bring-new/share/index", "subpackages/invite-for-gift/index/index", "subpackages/invite-for-gift/help/index", "subpackages/invite-for-gift/rule/index", "subpackages/invite-for-gift/reward/index", "subpackages/invite-for-gift/share/index", "subpackages/integralDraw/index/index", "subpackages/integralDraw/rule/index", "subpackages/treasure/index/index", "subpackages/treasure/my-treasure/index", "subpackages/treasure/coupon-list/index", "subpackages/treasure/get-coupon/index", "subpackages/treasure/actList/index", "subpackages/spellGroup/list/index", "subpackages/spellGroup/detail/index", "subpackages/spellGroup/groupTeamDetail/index", "subpackages/questionAnswer/index", "subpackages/questionAnswerNew/index", "subpackages/blindBoxLottery/index", "subpackages/spell-coupon/list/index", "subpackages/spell-coupon/applicable/index", "subpackages/spell-coupon/detail/index", "subpackages/spell-coupon/group-detail/index", "subpackages/spell-coupon/group-list/index", "subpackages/spell-coupon/members/index", "subpackages/spell-coupon/order/confirm/index", "subpackages/spell-coupon/order/success/index", "subpackages/spell-coupon/order/list/index", "subpackages/spell-coupon/order/detail/index", "subpackages/password-envelope/index/index", "subpackages/password-envelope/reward/index", "subpackages/outbreak-card/index", "subpackages/split-coupons/list/index", "subpackages/split-coupons/detail/index", "subpackages/split-coupons/records/index", "subpackages/queneUp/index", "subpackages/queneUp/list", "subpackages/queneUp/detail", "pages/multi/list-instore", "pages/multi/choice-multi", "pages/multi/list", "pages/multi/select-city", "pages/multi/list-search", "pages/multi/list-component", "pages/multi/youdian/search", "subpackages/protocol/index", "subpackages/protocol/list/index", "subpackages/protocol/detail/index", "subpackages/protocol/privacy/recommend", "subpackages/address/add/index", "subpackages/address/list/index", "subpackages/user-equity/index", "subpackages/user-equity/level/index", "subpackages/user-equity/level-record/index", "subpackages/user-equity/privilege-diff/index", "subpackages/asset-merge/index", "subpackages/asset-merge/merge/index", "subpackages/asset-merge/unionid/index", "subpackages/bgImage/change-theme/index", "subpackages/bgImage/choose-way/index", "subpackages/bgImage/my-theme/index", "subpackages/gift-card/index/index", "subpackages/gift-card/card/index", "subpackages/gift-card/confirm/index", "subpackages/gift-card/exchange/index", "subpackages/gift-card/details/index", "subpackages/gift-card/share-card/index", "subpackages/gift-card/give/index", "subpackages/gift-card/record/index", "subpackages/gift-card/receive/index", "subpackages/gift-card/history/index", "subpackages/gift-card/notice/index", "subpackages/gift-card/orderDetail/index", "subpackages/gift-card/order-list/index", "subpackages/gift-card/rightsInterestsCard/index", "subpackages/gift-card/paySuccess/index", "subpackages/gift-card/batchGiftsCard/index", "subpackages/gift-card/balance-transfer/index", "subpackages/gift-card/confirm-transfer/index", "subpackages/gift-card/physical-card/list", "subpackages/gift-card/physical-card/record", "subpackages/gift-card/apply-gift-cards/index", "subpackages/premium-membership/index/index", "subpackages/premium-membership/rich-text/index", "subpackages/premium-membership/buy-record/index", "subpackages/premium-membership/select-cards/index", "subpackages/premium-membership/cover/index", "subpackages/premium-membership/give/index", "subpackages/premium-membership/receive/index", "subpackages/premium-membership/exchange/index", "subpackages/premium-membership/collect/index", "subpackages/premium-membership/my-cards/index", "subpackages/premium-membership/student-certification/index", "subpackages/premium-membership/save-money-preview", "subpackages/sign-in/index/index", "subpackages/sign-in/rule/index", "subpackages/value-card/agree-info/index", "subpackages/value-card/balance-donate/index", "subpackages/value-card/balance-receive/index", "subpackages/value-card/member-value/index", "subpackages/value-card/notice/index", "subpackages/value-card/pay-success/index", "subpackages/value-card/record/index", "subpackages/value-card/exchange/index", "subpackages/value-card/physical-card-recharge/index", "subpackages/value-card/manage/index", "subpackages/refund/refund-apply/index", "subpackages/refund/refund-detail/index", "subpackages/refund/refund-list/index", "subpackages/refund/refund-content/index", "subpackages/wishMatch/wishMatch", "subpackages/confirm/confirm", "subpackages/detail/index", "subpackages/evaluate/index/index", "subpackages/evaluate/list/index", "subpackages/evaluate/detail/index", "subpackages/evaluate/add/index", "subpackages/select-coupon/index", "subpackages/coupon/promotion/index", "subpackages/coupon/voucher-coupon/index", "subpackages/coupon/get-coupon/index", "subpackages/coupon/exchange/index", "subpackages/coupon/get-activity/index", "subpackages/coupon/tourist-coupon/index", "subpackages/coupon/package/list/index", "subpackages/coupon/package/detail/index", "subpackages/coupon/package/record/index", "subpackages/coupon/package/paySuccess/index", "subpackages/coupon/package/agreeInfo/index", "subpackages/transform/index", "subpackages/exchange-coupon/index", "pages/pay/success/index", "pages/user/coupon/index", "pages/user/info/info", "subpackages/user/cancellation-cause/index", "subpackages/user/cancellation-event/index", "subpackages/user/account-security/index", "subpackages/user/cancellation/index", "subpackages/user/agreement/index", "subpackages/user/certification/index", "subpackages/user/change-mobile/index", "subpackages/user/update-mobile/index", "subpackages/user/change-language/index", "subpackages/user/areaCodeMobile/index", "pages/coupon/coupon-receive/index", "pages/coupon/user-giving-records/index", "pages/coupon/user-coupon-stale/index", "pages/coupon/batch-coupons/index", "pages/coupon/customized-coupons/index", "subpackages/invoice/index/index", "subpackages/invoice/apply/index", "subpackages/invoice/order-list/index", "subpackages/invoice/instructions/index", "subpackages/pinDetails/pin-poster-edit", "subpackages/pinDetails/index", "subpackages/pinDetailsShare/index", "subpackages/suggestionFeedback/list/index", "subpackages/suggestionFeedback/add/index", "subpackages/marketing-detail/index", "subpackages/familyMemberCard/index/index", "subpackages/familyMemberCard/cartManager/index", "subpackages/familyMemberCard/addCard/index", "subpackages/familyMemberCard/cardSet/index", "subpackages/third-equity/index/index", "subpackages/third-equity/detail/index", "subpackages/category-goods/index", "subpackages/user-task-center/index/index", "subpackages/user-task-center/record/index", "subpackages/user-task-center/rule/index", "subpackages/user-task-center/limitGoods/index", "subpackages/exchange-goods/index", "subpackages/exchange-goods/new-index", "subpackages/group-restaurant/takefood/index", "subpackages/group-restaurant/confirm/index", "subpackages/group-restaurant/index/index", "subpackages/group-restaurant/multi/choice-multi", "subpackages/group-restaurant/multi/list", "subpackages/group-restaurant/multi/list-instore", "subpackages/group-restaurant/multi/list-search", "subpackages/group-restaurant/address/list/index", "subpackages/group-purchase-coupon/list/index", "subpackages/group-purchase-coupon/ali-list/index", "subpackages/group-purchase-coupon/detail/index", "subpackages/group-purchase-coupon/cancel-coupon-helper/index", "subpackages/enterprise-wechat/customer/index", "subpackages/enterprise-wechat/group-chat/index", "subpackages/lottery-reward/index", "subpackages/lottery-reward/redirect", "subpackages/lottery-reward/draw-down", "subpackages/middlePage/multiple-person-ordering", "subpackages/login-auth/index", "subpackages/coupon-goods-list/index", "subpackages/coupon-goods-list/multi-list", "subpackages/coupon-goods-list/list", "subpackages/site-pages/apply-goods/index", "subpackages/site-pages/apply-stores/index", "subpackages/site-pages/apply-rights-cards/index", "subpackages/site-pages/search-goods/index", "subpackages/site-pages/password-setting/index", "subpackages/site-pages/shopQqualification/index", "subpackages/site-pages/pay-success/index", "subpackages/site-pages/member-card-activation/index", "subpackages/site-pages/packaging-fee-detail/index", "subpackages/site-pages/no-queuing-privilege/index", "subpackages/site-pages/personalized-label/index", "subpackages/site-pages/personalized-label/edit", "subpackages/site-pages/shop-home/index", "subpackages/site-pages/custom-web-view/index", "subpackages/site-pages/pay/index", "subpackages/site-pages/shop/other-sell-shop/index", "subpackages/strongestDecibel/index/index", "subpackages/live-player/list/index", "plugins/index", "plugins2/index", "subpackages/ar-cheers/index", "__wx__/functional-page", "__wx__/open-api-redirecting-page", "__wx__/choose-wifi-credential-page"], "page": {"pages/index/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "componentPlaceholder": {"tab-index": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "disableScroll": true}}, "subpackages/payment-code/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "disableScroll": true, "componentPlaceholder": {"tab-member-code": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/license/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "食品安全档案", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/webView/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/privacyNumPage/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "号码保护", "enablePullDownRefresh": false, "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/errorPage/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/pay-gift-other-rights/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/delivery-detail/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "订单详情", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pages/user/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "disableScroll": true, "componentPlaceholder": {"std-user": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pages/takefood/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "disableScroll": true, "componentPlaceholder": {"tab-takefood": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pages/takeout/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pages/page/page.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "enablePullDownRefresh": true, "componentPlaceholder": {"async-page": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pages/duibaPay/duibaPay.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "微信支付", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pages/duibaRedirect/duibaRedirect.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "积分商城", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pages/page/p1/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "componentPlaceholder": {"tab-p1": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pages/page/p2/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "componentPlaceholder": {"tab-p2": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pages/page/p3/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "disableScroll": true, "componentPlaceholder": {"tab-p3": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pages/page/p4/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "enablePullDownRefresh": true, "componentPlaceholder": {"tab-p4": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pages/page/p5/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "componentPlaceholder": {"tab-p5": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pages/pluginMall/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "disableScroll": true, "componentPlaceholder": {"tab-mall": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pages/webView/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pages/user/qualification/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pages/pay/index/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "disableScroll": true, "componentPlaceholder": {"tab-pay": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pages/pay/record/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "买单记录", "enablePullDownRefresh": false, "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pages/pay/detail/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "支付详情", "enablePullDownRefresh": false, "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pages/order/list/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "disableScroll": true, "componentPlaceholder": {"tab-order-list": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pages/order/remark/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "备注", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMall/error/index.html": {"window": {"navigationStyle": "custom", "backgroundColor": "#fff", "componentPlaceholder": {"std-navigation-bar": "view", "config-provider": "view", "std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMall/webview/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMall/index/index.html": {"window": {"navigationStyle": "custom", "backgroundColor": "#f2f2f2", "navigationBarBackgroundColor": "#f2f2f2", "componentPlaceholder": {"config-provider": "view", "std-crowd-dialog": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTextStyle": "black"}}, "pluginMall/detail/index.html": {"window": {"navigationStyle": "default", "navigationBarTextStyle": "black", "navigationBarBackgroundColor": "#FFF", "componentPlaceholder": {"std-navigation-bar": "view", "config-provider": "view", "std-crowd-dialog": "view", "std-rich-text": "view", "std-count-down": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "商品详情页"}}, "pluginMall/classify/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "商品分类", "navigationBarBackgroundColor": "#ffffff", "backgroundColor": "#fff", "navigationBarTextStyle": "black", "componentPlaceholder": {"config-provider": "view", "std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMall/cart/index.html": {"window": {"navigationStyle": "default", "navigationBarTextStyle": "black", "backgroundColor": "#f2f2f2", "navigationBarBackgroundColor": "#f2f2f2", "componentPlaceholder": {"config-provider": "view", "std-empty": "view", "std-crowd-dialog": "view", "std-checkbox": "view", "std-brand-info": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "购物车"}}, "pluginMall/order-confirm/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "订单结算", "navigationBarTextStyle": "black", "backgroundColor": "#f2f2f2", "navigationBarBackgroundColor": "#f2f2f2", "componentPlaceholder": {"config-provider": "view", "std-goods-order-item": "view", "std-goods-section": "view", "std-crowd-dialog": "view", "std-delivery-address": "view", "std-checkbox": "view", "std-cell": "view", "std-popup-radio": "view", "std-stepper": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMall/order-list/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "订单列表", "backgroundColor": "#f2f2f2", "navigationBarBackgroundColor": "#f2f2f2", "navigationBarTextStyle": "black", "onReachBottomDistance": 400, "componentPlaceholder": {"config-provider": "view", "std-empty": "view", "std-goods-order-item": "view", "std-goods-section": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMall/order-detail/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "backgroundColor": "#f2f2f2", "navigationBarBackgroundColor": "#f2f2f2", "enablePullDownRefresh": true, "componentPlaceholder": {"std-navigation-bar": "view", "config-provider": "view", "std-goods-order-item": "view", "std-goods-section": "view", "std-after-sale-select": "view", "std-count-down": "view", "std-delivery-address": "view", "std-mall-header": "view", "std-cell": "view", "std-steps": "view", "std-fixed-bar": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTextStyle": "black"}}, "pluginMall/logistics-detail/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "物流信息", "navigationBarTextStyle": "black", "backgroundColor": "#f2f2f2", "navigationBarBackgroundColor": "#f2f2f2", "componentPlaceholder": {"config-provider": "view", "std-empty": "view", "std-steps": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMall/logistics-form/index.html": {"window": {"navigationStyle": "custom", "backgroundColor": "#f2f2f2", "navigationBarBackgroundColor": "#f2f2f2", "componentPlaceholder": {"std-navigation-bar": "view", "config-provider": "view", "std-mall-header": "view", "std-popup-radio": "view", "std-fixed-bar": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTextStyle": "black"}}, "pluginMall/after-sale-select/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "", "backgroundColor": "#f2f2f2", "navigationBarBackgroundColor": "#f2f2f2", "navigationBarTextStyle": "black", "componentPlaceholder": {"std-navigation-bar": "view", "config-provider": "view", "std-goods-order-item": "view", "std-goods-section": "view", "std-delivery-address": "view", "std-cell": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMall/after-sale-detail/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "backgroundColor": "#f2f2f2", "navigationBarBackgroundColor": "#f2f2f2", "enablePullDownRefresh": true, "componentPlaceholder": {"config-provider": "view", "mall-after-sale-detail": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTextStyle": "black"}}, "pluginMall/after-sale-form/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "退款申请", "backgroundColor": "#f2f2f2", "navigationBarBackgroundColor": "#f2f2f2", "navigationBarTextStyle": "black", "componentPlaceholder": {"config-provider": "view", "mall-after-sale-form": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMall/address-list/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "选择收货地址", "backgroundColor": "#f5f5f5", "navigationBarBackgroundColor": "#fff", "navigationBarTextStyle": "black", "componentPlaceholder": {"config-provider": "view", "std-fixed-bar": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMall/address-edit/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "收货地址", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black", "enablePullDownRefresh": false, "componentPlaceholder": {"config-provider": "view", "std-checkbox": "view", "std-modal": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMall/invoicing-detail/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "开票详情", "backgroundColor": "#f2f2f2", "navigationBarBackgroundColor": "#f2f2f2", "componentPlaceholder": {"config-provider": "view", "std-goods-order-item": "view", "std-fixed-bar": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMall/distribution-index/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "componentPlaceholder": {"std-navigation-bar": "view", "config-provider": "view", "std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMall/distribution-apply/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "", "componentPlaceholder": {"config-provider": "view", "std-empty": "view", "std-rich-text": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMall/distribution-invite/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "componentPlaceholder": {"std-navigation-bar": "view", "config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMall/distribution-junior/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "下级分销员", "backgroundColor": "#f5f5f5", "componentPlaceholder": {"config-provider": "view", "std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMall/distribution-withdraw/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "提现", "componentPlaceholder": {"config-provider": "view", "std-checkbox": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMall/distribution-withdraw-record/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "提现记录", "backgroundColor": "#f5f5f5", "componentPlaceholder": {"config-provider": "view", "std-empty": "view", "std-cell": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMall/distribution-order/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "分销订单", "componentPlaceholder": {"config-provider": "view", "std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMall/distribution-goods/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "商品列表", "componentPlaceholder": {"config-provider": "view", "std-empty": "view", "std-fixed-bar": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMall/pay-result/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "支付成功", "navigationBarBackgroundColor": "#ffffff", "componentPlaceholder": {"config-provider": "view", "std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMall/order-change/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "开票详情", "backgroundColor": "#f2f2f2", "navigationBarBackgroundColor": "#f2f2f2", "componentPlaceholder": {"config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMall/store-select/index.html": {"window": {"navigationStyle": "custom", "navigationBarTextStyle": "black", "navigationBarBackgroundColor": "#ffffff", "componentPlaceholder": {"config-provider": "view", "std-navigation-bar": "view", "std-store-action-group": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "onReachBottomDistance": 300}}, "pluginMall/city-select/index.html": {"window": {"navigationStyle": "default", "navigationBarTextStyle": "black", "navigationBarBackgroundColor": "#ffffff", "navigationBarTitleText": "选择门店", "enablePullDownRefresh": false, "componentPlaceholder": {"config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginPoint/index/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"config-provider": "view", "std-navigation-bar": "view", "std-error-page": "view", "std-mall-switch": "view", "v-custom-page": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "disableScroll": true}}, "pluginPoint/record/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "积分明细", "enablePullDownRefresh": true, "componentPlaceholder": {"std-error-page": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginPoint/rule/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"std-error-page": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "积分规则", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black", "enablePullDownRefresh": false}}, "pluginPoint/exchange-record/index.html": {"window": {"navigationStyle": "default", "enablePullDownRefresh": true, "navigationBarTitleText": "兑换记录", "componentPlaceholder": {"std-order-card": "view", "config-provider": "view", "std-error-page": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginPoint/exchange-coupon/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"v-pay-choose": "view", "config-provider": "view", "std-empty": "view", "std-rich-text": "view", "std-mall-tying-goods": "view", "std-error-page": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "兑换详情", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black", "enablePullDownRefresh": false}}, "pluginPoint/exchange-evaluate/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "商品评价", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginPoint/exchange-success/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "兑换信息", "enablePullDownRefresh": false, "componentPlaceholder": {"config-provider": "view", "std-error-page": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginPoint/exchange-order-confirm/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "订单确认", "componentPlaceholder": {"config-provider": "view", "std-error-page": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginPoint/exchange-confirm/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "确认兑换", "componentPlaceholder": {"v-pay-choose": "view", "config-provider": "view", "std-fixed-bottom-bar": "view", "std-error-page": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginPoint/entity-exchange-success/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "兑换信息", "componentPlaceholder": {"std-after-sale-select": "view", "config-provider": "view", "std-navigation-bar": "view", "std-mall-tying-goods": "view", "std-error-page": "view", "std-store-action-group": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTextStyle": "black"}}, "pluginPoint/logistics/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "物流信息", "componentPlaceholder": {"std-empty": "view", "std-error-page": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginPoint/refund-apply/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "申请退款", "backgroundColor": "#F5F5F5", "componentPlaceholder": {"config-provider": "view", "std-error-page": "view", "mall-after-sale-form": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginPoint/refund-list/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "退款记录", "backgroundColor": "#F5F5F5", "componentPlaceholder": {"std-error-page": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginPoint/refund-detail/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "backgroundColor": "#f2f2f2", "navigationBarBackgroundColor": "#f2f2f2", "componentPlaceholder": {"config-provider": "view", "std-error-page": "view", "mall-after-sale-detail": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTextStyle": "black"}}, "pluginPoint/pay-result/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "支付成功", "navigationBarBackgroundColor": "#ffffff", "componentPlaceholder": {"std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginPoint/webview/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginPages/login-guide/index.html": {"window": {"navigationStyle": "default", "backgroundColor": "#f5f5f5", "componentPlaceholder": {"config-provider": "view", "std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "账户登录", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black", "enablePullDownRefresh": false}}, "pluginPages/money-saving-calc/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "disableScroll": true}}, "pluginPages/store-account/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "账户信息", "enablePullDownRefresh": true}}, "pluginPages/transaction-records/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "交易记录", "enablePullDownRefresh": true}}, "pluginPages/applicable-stores/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"config-provider": "view", "std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "适用门店", "enablePullDownRefresh": true}}, "pluginPages/mall-invoice-apply/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "提交申请"}}, "pluginPages/mall-invoice-detail/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "发票详情", "backgroundColor": "#F5F5F5", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginPages/goods-energy-calculation/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"std-empty": "view", "std-navigation-bar": "view", "std-scroll-number-to": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginPages/apply-stores/index.html": {"window": {"navigationStyle": "default", "backgroundColor": "#f5f5f5", "componentPlaceholder": {"config-provider": "view", "std-empty": "view", "std-navigation-bar": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "适用门店", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black", "enablePullDownRefresh": false}}, "pluginPages/apply-goods/index.html": {"window": {"navigationStyle": "default", "backgroundColor": "#f5f5f5", "componentPlaceholder": {"config-provider": "view", "std-empty": "view", "std-navigation-bar": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "适用商品", "navigationBarBackgroundColor": "#FFF", "navigationBarTextStyle": "black", "enablePullDownRefresh": false}}, "pluginPages/apply-rights-cards/index.html": {"window": {"navigationStyle": "default", "backgroundColor": "#f5f5f5", "componentPlaceholder": {"config-provider": "view", "std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "适用权益卡", "navigationBarBackgroundColor": "#FFF", "navigationBarTextStyle": "black", "enablePullDownRefresh": false}}, "pluginPages/apply-gift-cards/index.html": {"window": {"navigationStyle": "default", "backgroundColor": "#f5f5f5", "componentPlaceholder": {"config-provider": "view", "std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "适用礼品卡", "navigationBarBackgroundColor": "#FFF", "navigationBarTextStyle": "black", "enablePullDownRefresh": false}}, "pluginPages/custom-web-view/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginPages/miniapp-bridge/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginDine/address-add/index.html": {"window": {"navigationStyle": "custom", "disableScroll": true, "componentPlaceholder": {"config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginDine/address-edit/index.html": {"window": {"navigationStyle": "custom", "disableScroll": true, "componentPlaceholder": {"config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginDine/address-select/index.html": {"window": {"navigationStyle": "custom", "disableScroll": true, "componentPlaceholder": {"config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginDine/location-select/index.html": {"window": {"navigationStyle": "custom", "disableScroll": true, "componentPlaceholder": {"config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginDine/goods-list/index.html": {"window": {"navigationStyle": "custom", "disableScroll": true, "componentPlaceholder": {"config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginDine/goods-package-detail/index.html": {"window": {"navigationStyle": "custom", "disableScroll": true, "componentPlaceholder": {"config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginDine/goods-search/index.html": {"window": {"navigationStyle": "custom", "disableScroll": true, "componentPlaceholder": {"config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginDine/goods-packing-fee-detail/index.html": {"window": {"navigationStyle": "custom", "disableScroll": true, "componentPlaceholder": {"config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginDine/order-confirm/index.html": {"window": {"navigationStyle": "custom", "disableScroll": true, "componentPlaceholder": {"config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginDine/order-detail/index.html": {"window": {"navigationStyle": "custom", "disableScroll": true, "componentPlaceholder": {"config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginDine/order-list/index.html": {"window": {"navigationStyle": "custom", "disableScroll": true, "componentPlaceholder": {"config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginDine/order-remark/index.html": {"window": {"navigationStyle": "custom", "disableScroll": true, "componentPlaceholder": {"config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginDine/refund-apply/index.html": {"window": {"navigationStyle": "custom", "disableScroll": true, "componentPlaceholder": {"config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginDine/refund-detail/index.html": {"window": {"navigationStyle": "custom", "disableScroll": true, "componentPlaceholder": {"config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginDine/refund-list/index.html": {"window": {"navigationStyle": "custom", "disableScroll": true, "componentPlaceholder": {"config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginDine/shop-select/index.html": {"window": {"navigationStyle": "custom", "disableScroll": true, "componentPlaceholder": {"config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginDine/shop-cert/service-qualification.html": {"window": {"navigationStyle": "custom", "disableScroll": true, "componentPlaceholder": {"config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginDine/shop-cert/food-safety-file.html": {"window": {"navigationStyle": "custom", "disableScroll": true, "componentPlaceholder": {"config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginDine/shop-activity/detail.html": {"window": {"navigationStyle": "custom", "disableScroll": true, "componentPlaceholder": {"config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginDine/invoice-detail/index.html": {"window": {"navigationStyle": "custom", "disableScroll": true, "componentPlaceholder": {"config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginDine/invoice-select/index.html": {"window": {"navigationStyle": "custom", "disableScroll": true, "componentPlaceholder": {"config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginDine/invoice-add/index.html": {"window": {"navigationStyle": "custom", "disableScroll": true, "componentPlaceholder": {"config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginDine/invoice-apply/index.html": {"window": {"navigationStyle": "custom", "disableScroll": true, "componentPlaceholder": {"config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMedal/index/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"std-crowd-dialog": "view", "std-navigation-bar": "view", "std-rich-text": "view", "std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMedal/medal-detail/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"std-crowd-dialog": "view", "std-navigation-bar": "view", "config-provider": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "disableScroll": true}}, "pluginMedal/task/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"std-crowd-dialog": "view", "std-navigation-bar": "view", "config-provider": "view", "std-count-down": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "disableScroll": true}}, "pluginMedal/task-rewards/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "我的奖励"}}, "pluginUser/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginUser/account-mgmt/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"config-provider": "view", "std-navigation-bar": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "disableScroll": true, "enablePullDownRefresh": false}}, "pluginUser/account-switch/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"config-provider": "view", "std-navigation-bar": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "disableScroll": true, "enablePullDownRefresh": false}}, "pluginUser/account-log-off/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"config-provider": "view", "std-navigation-bar": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "disableScroll": true, "enablePullDownRefresh": false}}, "pluginUser/log-off-agreement/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"config-provider": "view", "std-navigation-bar": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "disableScroll": true, "enablePullDownRefresh": false}}, "pluginUser/assets-move/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"config-provider": "view", "std-navigation-bar": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "disableScroll": true, "enablePullDownRefresh": false}}, "pluginUser/assets-move-records/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"config-provider": "view", "std-navigation-bar": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "disableScroll": true, "enablePullDownRefresh": false}}, "pluginQueue/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginFeedback/index/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "建议反馈", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black", "enablePullDownRefresh": true}}, "pluginFeedback/add/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "disableScroll": true, "navigationBarTitleText": "建议反馈", "navigationBarBackgroundColor": "#F5F5F5", "navigationBarTextStyle": "black"}}, "subpackages/tabbar-pages/takefood/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/tabbar-pages/order-list/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/tabbar-pages/pay/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "async-components/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "async-components2/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "async-libs/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "open/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/tabbar-pages/user/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/tabbar-pages/index/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/tabbar-pages/page/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMarketing/common/reward-template-detail.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"std-navigation-bar": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMarketing/common/web-view/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMarketing/common/my-reward/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "我的奖品"}}, "pluginMarketing/common/my-reward/coupon-package-detail.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "我的奖品"}}, "pluginMarketing/common/goods/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "商品列表", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMarketing/common/goods/common-goods.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMarketing/common/store/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "门店列表"}}, "pluginMarketing/common/source/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "订单来源渠道", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMarketing/overlord-meal/index.html": {"window": {"navigationStyle": "custom", "navigationBarBackgroundColor": "#ffffff", "enablePullDownRefresh": true, "onReachBottomDistance": 20, "componentPlaceholder": {"std-navigation-bar": "view", "std-empty": "view", "std-count-down": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMarketing/overlord-meal/share.html": {"window": {"navigationStyle": "custom", "navigationBarBackgroundColor": "#ffffff", "componentPlaceholder": {"std-navigation-bar": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMarketing/identify-customer-code/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "", "navigationBarBackgroundColor": "#ffffff", "componentPlaceholder": {"std-empty": "view", "std-design-text": "view", "std-design-image": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMarketing/decibel/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"std-navigation-bar": "view", "std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "最强分贝", "navigationBarBackgroundColor": "#000000", "navigationBarTextStyle": "black"}}, "pluginMarketing/puzzle/index.html": {"window": {"navigationStyle": "custom", "navigationBarBackgroundColor": "#ffffff", "enablePullDownRefresh": true, "onReachBottomDistance": 20, "componentPlaceholder": {"std-navigation-bar": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMarketing/puzzle/give-record.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "赠送记录"}}, "pluginMarketing/puzzle/get-pieces.html": {"window": {"navigationStyle": "custom", "navigationBarBackgroundColor": "#ffffff", "enablePullDownRefresh": true, "onReachBottomDistance": 20, "componentPlaceholder": {"std-navigation-bar": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMarketing/ladder-coupons/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"std-navigation-bar": "view", "std-empty": "view", "std-authorization": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "按序核券", "navigationBarBackgroundColor": "#000000", "navigationBarTextStyle": "black"}}, "pluginMarketing/lottery/index/index.html": {"window": {"navigationStyle": "custom", "enablePullDownRefresh": true, "componentPlaceholder": {"std-empty": "view", "std-navigation-bar": "view", "std-error-page": "view", "std-crowd-dialog": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMarketing/lottery/share/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"std-empty": "view", "std-navigation-bar": "view", "std-crowd-dialog": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMarketing/lottery/join/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "参与记录"}}, "pluginMarketing/lottery/rule/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "活动规则", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMarketing/lottery/equity/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"std-empty": "view", "std-rich-text": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "三方权益"}}, "pluginMarketing/checkin/index/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"std-empty": "view", "std-navigation-bar": "view", "std-error-page": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMarketing/checkin/my-reward/index.html": {"window": {"navigationStyle": "default", "onReachBottomDistance": 20, "componentPlaceholder": {"std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "我的奖品"}}, "pluginMarketing/receive/index/index.html": {"window": {"navigationStyle": "custom", "disableScroll": true, "componentPlaceholder": {"std-empty": "view", "std-navigation-bar": "view", "std-error-page": "view", "std-crowd-dialog": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMarketing/receive/list/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"std-empty": "view", "std-navigation-bar": "view", "std-crowd-dialog": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMarketing/receive/receive-result/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "领取结果"}}, "pluginMarketing/receive/password-rule/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"std-rich-text": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "如何获取口令"}}, "pluginMarketing/partition-coupon/index/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"std-empty": "view", "std-navigation-bar": "view", "std-error-page": "view", "std-crowd-dialog": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMarketing/partition-coupon/team-record/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "我的记录"}}, "pluginMarketing/point-exchange/index/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"std-navigation-bar": "view", "std-error-page": "view", "std-crowd-dialog": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMarketing/point-exchange/detail/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"std-empty": "view", "std-navigation-bar": "view", "std-crowd-dialog": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMarketing/point-exchange/my-reward/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "兑换记录"}}, "pluginMarketing/point-exchange/goods/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMarketing/point-exchange/store/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "", "componentPlaceholder": {"std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMarketing/bridge/subscribe/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMarketing/bridge/navigate/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMarketing/bridge/crowd/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"std-crowd-dialog": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMarketing/bridge/authorization/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMarketing/collect-card/index/index.html": {"window": {"navigationStyle": "custom", "enablePullDownRefresh": true, "componentPlaceholder": {"std-navigation-bar": "view", "std-error-page": "view", "std-crowd-dialog": "view", "std-design-jump-help": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMarketing/collect-card/get-card/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"std-navigation-bar": "view", "std-crowd-dialog": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMarketing/collect-card/my-card/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "我的卡片"}}, "pluginMarketing/collect-card/give-record/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "赠送记录"}}, "pluginMarketing/nurture/index/index.html": {"window": {"navigationStyle": "custom", "enablePullDownRefresh": true, "componentPlaceholder": {"std-navigation-bar": "view", "std-error-page": "view", "std-crowd-dialog": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMarketing/invite/index/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"std-empty": "view", "std-count-down": "view", "std-navigation-bar": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMarketing/invite/share/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"std-empty": "view", "std-navigation-bar": "view", "std-crowd-dialog": "view", "std-design-jump-help": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMarketing/invite/my-invite/index.html": {"window": {"navigationStyle": "default", "onReachBottomDistance": 20, "componentPlaceholder": {"std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "我的邀请记录"}}, "pluginMarketing/guess/index/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"std-empty": "view", "std-navigation-bar": "view", "std-error-page": "view", "std-crowd-dialog": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMarketing/guess/record/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "竞猜记录"}}, "pluginMarketing/guess/reward/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "我的奖品"}}, "pluginMarketing/exchange/index/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"std-empty": "view", "std-navigation-bar": "view", "std-error-page": "view", "std-crowd-dialog": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMarketing/activity-center/index/index.html": {"window": {"navigationStyle": "default", "onReachBottomDistance": 20, "componentPlaceholder": {"std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "活动中心"}}, "pluginMarketing/activity-center/detail/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"std-navigation-bar": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMarketing/collect-img/index/index.html": {"window": {"navigationStyle": "custom", "enablePullDownRefresh": true, "navigationBarTitleText": "", "navigationBarBackgroundColor": "#ffffff", "componentPlaceholder": {"std-card-item": "view", "std-navigation-bar": "view", "std-fixed-bottom-bar": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pluginMarketing/collect-img/give-record/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "赠送记录"}}, "pluginMarketing/scratchcard/share/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"std-navigation-bar": "view", "std-crowd-dialog": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMarketing/scratchcard/index/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"std-empty": "view", "std-navigation-bar": "view", "std-crowd-dialog": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMarketing/funny-synthesis/index/index.html": {"window": {"navigationStyle": "custom", "enablePullDownRefresh": true, "componentPlaceholder": {"std-navigation-bar": "view", "std-error-page": "view", "std-crowd-dialog": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "pluginMarketing/funny-synthesis/give-record/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"std-empty": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "赠送记录"}}, "subpackages/bargain/list/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "componentPlaceholder": {"count-down": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/bargain/equity-card/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "砍价详情", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/bargain/equity-order/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "确认订单", "componentPlaceholder": {"subscribe-mask": "view", "show-toast": "view", "v-select-coupon": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/bargain/activity/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "enablePullDownRefresh": true, "componentPlaceholder": {"qm-popup": "empty-placeholder", "count-down": "view", "subscribe-mask": "view", "custom-rich-text": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/bargain/record/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "砍价记录", "componentPlaceholder": {"count-down": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/old-bring-new/index/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"subscribe-mask": "view", "v-error": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "subpackages/old-bring-new/help/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"v-error": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "subpackages/old-bring-new/rule/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "活动规则", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/old-bring-new/reward/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "我的奖励", "componentPlaceholder": {"v-error": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/old-bring-new/share/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "邀请有礼", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/invite-for-gift/index/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"v-error": "view", "subscribe-mask": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "subpackages/invite-for-gift/help/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"v-error": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "subpackages/invite-for-gift/rule/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "活动规则", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/invite-for-gift/reward/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "我的奖励", "componentPlaceholder": {"empty-image": "view", "v-error": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/invite-for-gift/share/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "邀请有礼", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/integralDraw/index/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"error": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "积分抽奖", "enablePullDownRefresh": false}}, "subpackages/integralDraw/rule/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "积分抽奖", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/treasure/index/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "componentPlaceholder": {"qm-text": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/treasure/my-treasure/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"pop-up": "empty-placeholder", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/treasure/coupon-list/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/treasure/get-coupon/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/treasure/actList/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "集点卡", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/spellGroup/list/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"empty-image": "view", "count-down": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/spellGroup/detail/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "componentPlaceholder": {"count-down": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/spellGroup/groupTeamDetail/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "拼团详情", "componentPlaceholder": {"count-down": "view", "empty-image": "view", "zm-button": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/questionAnswer/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/questionAnswerNew/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/blindBoxLottery/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "enablePullDownRefresh": false, "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/spell-coupon/list/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "多人拼团", "enablePullDownRefresh": true, "componentPlaceholder": {"empty-image": "view", "shop-selector": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/spell-coupon/applicable/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/spell-coupon/detail/index.html": {"window": {"navigationStyle": "custom", "enablePullDownRefresh": true, "componentPlaceholder": {"subscribe-mask": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/spell-coupon/group-detail/index.html": {"window": {"navigationStyle": "custom", "enablePullDownRefresh": true, "componentPlaceholder": {"subscribe-mask": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/spell-coupon/group-list/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "多人拼团", "enablePullDownRefresh": true, "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/spell-coupon/members/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "全部拼团小伙伴", "enablePullDownRefresh": true, "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/spell-coupon/order/confirm/index.html": {"window": {"navigationStyle": "default", "enablePullDownRefresh": false, "navigationBarTitleText": "提交订单", "componentPlaceholder": {"v-pay-choose": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/spell-coupon/order/success/index.html": {"window": {"navigationStyle": "default", "enablePullDownRefresh": false, "navigationBarTitleText": "支付完成", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/spell-coupon/order/list/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"empty-image": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "订单列表", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "subpackages/spell-coupon/order/detail/index.html": {"window": {"navigationStyle": "default", "enablePullDownRefresh": false, "navigationBarTitleText": "订单详情", "componentPlaceholder": {"v-brand-call-bottom": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/password-envelope/index/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "componentPlaceholder": {"count-down": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/password-envelope/reward/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "我的奖励", "componentPlaceholder": {"empty-image": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/outbreak-card/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "componentPlaceholder": {"empty-image": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/split-coupons/list/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"empty-image": "view", "zm-button": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "瓜分优惠券"}}, "subpackages/split-coupons/detail/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "瓜分优惠券", "enablePullDownRefresh": true, "backgroundColor": "#F5F5F5", "componentPlaceholder": {"empty-image": "view", "count-down": "view", "zm-button": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/split-coupons/records/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "我的记录", "enablePullDownRefresh": true, "componentPlaceholder": {"empty-image": "view", "zm-button": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/queneUp/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "排队等位", "enablePullDownRefresh": false, "componentPlaceholder": {"queue-up": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarBackgroundColor": "#f5f5f5", "backgroundColor": "#f5f5f5", "navigationBarTextStyle": "black"}}, "subpackages/queneUp/list.html": {"window": {"navigationStyle": "default", "enablePullDownRefresh": false, "componentPlaceholder": {"queue-up-list": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "我的取号", "navigationBarBackgroundColor": "#f5f5f5", "backgroundColor": "#f5f5f5", "navigationBarTextStyle": "black"}}, "subpackages/queneUp/detail.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "排队等位", "enablePullDownRefresh": false, "componentPlaceholder": {"queue-up-detail": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarBackgroundColor": "#f5f5f5", "backgroundColor": "#f5f5f5", "navigationBarTextStyle": "black"}}, "pages/multi/list-instore.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"empty-image": "view", "pop-up": "empty-placeholder", "zm-button": "view", "store-list-item": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pages/multi/choice-multi.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"empty-image": "view", "qm-label": "view", "swipe-cell": "empty-placeholder", "address-item": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "选择收货地址", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "pages/multi/list.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"empty-image": "view", "pop-up": "empty-placeholder", "store-list-cart": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pages/multi/select-city.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "选择城市", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black", "disableScroll": true, "enablePullDownRefresh": false}}, "pages/multi/list-search.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "搜索门店", "componentPlaceholder": {"zm-button": "view", "pop-up": "empty-placeholder", "store-list-item": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pages/multi/list-component.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pages/multi/youdian/search.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "搜索", "componentPlaceholder": {"empty-image": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarBackgroundColor": "#f5f5f5", "backgroundColor": "#f5f5f5"}}, "subpackages/protocol/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black", "enablePullDownRefresh": false}}, "subpackages/protocol/list/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "条款与证照", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black", "enablePullDownRefresh": false}}, "subpackages/protocol/detail/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black", "enablePullDownRefresh": false}}, "subpackages/protocol/privacy/recommend.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "个性化推荐", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/address/add/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"std-modal": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black", "enablePullDownRefresh": false}}, "subpackages/address/list/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"address-list": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "收货地址", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "subpackages/user-equity/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "componentPlaceholder": {"pop-up": "empty-placeholder", "zm-button": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/user-equity/level/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"custom-rich-text": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "等级说明", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "subpackages/user-equity/level-record/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "componentPlaceholder": {"user-avatar": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTextStyle": "white"}}, "subpackages/user-equity/privilege-diff/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/asset-merge/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "合并资产", "componentPlaceholder": {"zm-button": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/asset-merge/merge/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "合并资产", "componentPlaceholder": {"show-toast": "view", "zm-button": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/asset-merge/unionid/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "资产迁入", "componentPlaceholder": {"empty-image": "view", "show-toast": "view", "zm-button": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/bgImage/change-theme/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "更换封面", "enablePullDownRefresh": false, "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/bgImage/choose-way/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "更换主题", "enablePullDownRefresh": false, "backgroundColor": "#fff", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/bgImage/my-theme/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "我的封面", "enablePullDownRefresh": false, "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/gift-card/index/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"code-popup": "view", "shop-selector": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "礼品卡列表", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "subpackages/gift-card/card/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "componentPlaceholder": {"cart-control": "view", "empty-image": "view", "pop-up": "empty-placeholder", "show-toast": "view", "confirm": "view", "shop-selector": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/gift-card/confirm/index.html": {"window": {"navigationStyle": "custom", "backgroundColor": "#f7f7f7", "enablePullDownRefresh": false, "componentPlaceholder": {"v-select-coupon": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/gift-card/exchange/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "", "componentPlaceholder": {"exchagne-template": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/gift-card/details/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "", "componentPlaceholder": {"code-popup": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/gift-card/share-card/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "", "componentPlaceholder": {"show-toast": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/gift-card/give/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/gift-card/record/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "获卡记录", "componentPlaceholder": {"empty-image": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/gift-card/receive/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "领取礼品卡", "componentPlaceholder": {"empty-image": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/gift-card/history/index.html": {"window": {"navigationStyle": "default", "enablePullDownRefresh": true, "navigationBarTitleText": "", "componentPlaceholder": {"empty-image": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/gift-card/notice/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"custom-rich-text": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/gift-card/orderDetail/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "订单详情", "componentPlaceholder": {"v-brand-call-bottom": "view", "show-toast": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/gift-card/order-list/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "", "componentPlaceholder": {"empty-image": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/gift-card/rightsInterestsCard/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "购买礼品卡", "componentPlaceholder": {"show-toast": "view", "confirm": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/gift-card/paySuccess/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/gift-card/batchGiftsCard/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "", "enablePullDownRefresh": true, "componentPlaceholder": {"empty-image": "view", "show-toast": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/gift-card/balance-transfer/index.html": {"window": {"navigationStyle": "default", "enablePullDownRefresh": true, "navigationBarTitleText": "礼品卡余额转移", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/gift-card/confirm-transfer/index.html": {"window": {"navigationStyle": "default", "enablePullDownRefresh": true, "navigationBarTitleText": "确认转移", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/gift-card/physical-card/list.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"empty-image": "view", "code-popup": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/gift-card/physical-card/record.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"empty-image": "view", "v-error": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/gift-card/apply-gift-cards/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"empty-image": "view", "brand-select-head": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "subpackages/premium-membership/index/index.html": {"window": {"navigationStyle": "custom", "disableScroll": true, "navigationBarTitleText": "", "componentPlaceholder": {"empty-image": "view", "v-custom-page": "view", "subscribe-mask": "view", "show-toast": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTextStyle": "white"}}, "subpackages/premium-membership/rich-text/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "权益卡", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black", "enablePullDownRefresh": false, "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/premium-membership/buy-record/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "购买记录", "componentPlaceholder": {"empty-image": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/premium-membership/select-cards/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "可用付费卡优惠", "componentPlaceholder": {"empty-image": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/premium-membership/cover/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "卡面管理", "enablePullDownRefresh": false, "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/premium-membership/give/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "赠送好友", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/premium-membership/receive/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "componentPlaceholder": {"empty-image": "view", "subscribe-mask": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/premium-membership/exchange/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "权益卡兑换", "componentPlaceholder": {"exchagne-template": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/premium-membership/collect/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "权益卡领取", "componentPlaceholder": {"v-error": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/premium-membership/my-cards/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"empty-image": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "权益卡列表", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "subpackages/premium-membership/student-certification/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"v-custom-page": "view", "pop-up": "empty-placeholder", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTextStyle": "white"}}, "subpackages/premium-membership/save-money-preview.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/sign-in/index/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"subscribe-mask": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/sign-in/rule/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "签到规则", "enablePullDownRefresh": false, "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/value-card/agree-info/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "储值规则", "enablePullDownRefresh": false, "componentPlaceholder": {"custom-rich-text": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/value-card/balance-donate/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "余额转赠", "enablePullDownRefresh": false, "componentPlaceholder": {"empty-image": "view", "show-toast": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/value-card/balance-receive/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "余额领取", "enablePullDownRefresh": false, "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/value-card/member-value/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"cart-control": "view", "confirm": "view", "custom-rich-text": "view", "subscribe-mask": "view", "empty-image": "view", "open-subscription": "view", "show-toast": "view", "shop-selector": "view", "stored-value-tag": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/value-card/notice/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "使用须知", "enablePullDownRefresh": false, "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/value-card/pay-success/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "支付成功", "enablePullDownRefresh": false, "componentPlaceholder": {"v-index-ad": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/value-card/record/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"empty-image": "view", "code-popup": "view", "zm-button": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/value-card/exchange/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "储值兑换", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/value-card/physical-card-recharge/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "实体卡充值", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/value-card/manage/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "储值管理", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/refund/refund-apply/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "申请退款", "backgroundColor": "#F5F5F5", "componentPlaceholder": {"qm-label": "view", "zm-button": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/refund/refund-detail/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "退款详情", "backgroundColor": "#F5F5F5", "componentPlaceholder": {"v-call-bottom": "view", "zm-button": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/refund/refund-list/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "退款记录", "backgroundColor": "#F5F5F5", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/refund/refund-content/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "退款明细", "backgroundColor": "#F5F5F5", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/wishMatch/wishMatch.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "componentPlaceholder": {"qm-price-label": "view", "cart-control": "view", "sell-time-popup": "view", "pre-price": "view", "qm-label": "view", "pop-up": "empty-placeholder", "pay-member-price-label": "view", "zm-button": "view", "attach-selector": "view", "premium-member": "view", "screenshot-share-dialog": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/confirm/confirm.html": {"window": {"navigationStyle": "custom", "backgroundColor": "#f7f7f7", "enablePullDownRefresh": false, "componentPlaceholder": {"std-advertisement": "view", "expected-arrival-time": "view", "distance-dialog": "view", "cart-control": "view", "goods-coupons": "view", "v-custom-page": "view", "payment-wait": "view", "subscribe-mask": "view", "v-pay-choose": "view", "show-toast": "view", "pay-gift-module": "view", "zm-button": "view", "v-select-coupon": "view", "v-multi-select-coupon": "view", "premium-member": "view", "meal-info-modal-dinner-people": "view", "scroll-number-to": "view", "flavor-card-popup": "view", "v-gift-card": "view", "v-remark": "view", "coupon-package-confirm": "view", "box-confirm-popup": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/detail/index.html": {"window": {"navigationStyle": "custom", "enablePullDownRefresh": true, "backgroundColor": "#F5F5F5", "componentPlaceholder": {"std-advertisement": "view", "cash-back-order-item": "view", "order-detail-top-tips": "view", "order-numbers": "view", "payment-wait": "view", "v-component-coupon": "view", "video-model": "view", "v-call-bottom": "view", "v-brand-call-bottom": "view", "v-index-ad": "view", "shadow": "view", "order-progress-list": "view", "count-down": "view", "split-delivery-list": "view", "advert-suspension-window": "view", "subscribe-mask": "view", "v-call-confirm": "view", "popup-security": "view", "v-pay-choose": "view", "v-pay-choose-select": "view", "pay-gift-module": "view", "pin-goods": "view", "order-progress": "view", "equity-value-card": "view", "wechat-state-bubble": "view", "zm-button": "view", "order-reward": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/evaluate/index/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "评价", "componentPlaceholder": {"show-toast": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/evaluate/list/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"empty-image": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "我的评价", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black", "enablePullDownRefresh": true}}, "subpackages/evaluate/detail/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "评价详情", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/evaluate/add/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "发表评价", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/select-coupon/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"empty-image": "view", "coupon-package-confirm": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "优惠券列表", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black", "enablePullDownRefresh": false}}, "subpackages/coupon/promotion/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/coupon/voucher-coupon/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "红包详情", "enablePullDownRefresh": false, "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/coupon/get-coupon/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "enablePullDownRefresh": false, "componentPlaceholder": {"empty-image": "view", "subscribe-mask": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/coupon/exchange/index.html": {"window": {"navigationStyle": "default", "backgroundColor": "#f5f5f5", "enablePullDownRefresh": false, "componentPlaceholder": {"std-coupon-exchange": "view", "coupon-exchange": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "兑换优惠券", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "subpackages/coupon/get-activity/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "enablePullDownRefresh": false, "componentPlaceholder": {"empty-image": "view", "subscribe-mask": "view", "shop-selector": "view", "zm-button": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/coupon/tourist-coupon/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "新人领券", "componentPlaceholder": {"coupon-sale": "view", "subscribe-mask": "view", "zm-button": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/coupon/package/list/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"count-down": "view", "empty-image": "view", "shop-selector": "view", "zm-button": "view", "coupon-package-list-item": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/coupon/package/detail/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "enablePullDownRefresh": false, "componentPlaceholder": {"coupon-package-detail": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/coupon/package/record/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "券包记录", "enablePullDownRefresh": false, "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/coupon/package/paySuccess/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "支付详情", "componentPlaceholder": {"v-index-ad": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/coupon/package/agreeInfo/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "优惠券包用户协议", "enablePullDownRefresh": false, "componentPlaceholder": {"custom-rich-text": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/transform/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "", "backgroundColor": "#f5f5f5", "componentPlaceholder": {"empty-image": "view", "zm-button": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/exchange-coupon/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "优惠券", "enablePullDownRefresh": false, "componentPlaceholder": {"subscribe-mask": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pages/pay/success/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "订单支付", "backgroundColor": "#FFC809", "enablePullDownRefresh": false, "componentPlaceholder": {"v-index-ad": "view", "v-component-coupon": "view", "sync-coupon-modal": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pages/user/coupon/index.html": {"window": {"navigationStyle": "default", "backgroundColor": "#f5f5f5", "enablePullDownRefresh": false, "componentPlaceholder": {"std-coupon-list": "view", "coupon-list": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "优惠券列表", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "pages/user/info/info.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "disableScroll": true, "componentPlaceholder": {"std-personal-data": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/user/cancellation-cause/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "确认注销事项", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "subpackages/user/cancellation-event/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "确认注销事项", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "subpackages/user/account-security/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "账号与安全", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/user/cancellation/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "账号注销", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "subpackages/user/agreement/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"empty-image": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black", "enablePullDownRefresh": false}}, "subpackages/user/certification/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "实名登记信息", "componentPlaceholder": {"v-error": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/user/change-mobile/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "切换账号", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "subpackages/user/update-mobile/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "账号设置", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "subpackages/user/change-language/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "语言设置", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "subpackages/user/areaCodeMobile/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "选择地区"}}, "pages/coupon/coupon-receive/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "navigationBarTextStyle": "black", "componentPlaceholder": {"std-coupon-receive": "view", "coupon-receive": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "pages/coupon/user-giving-records/index.html": {"window": {"navigationStyle": "default", "backgroundColor": "#f5f5f5", "enablePullDownRefresh": false, "componentPlaceholder": {"std-coupon-gift-list": "view", "coupon-gift-list": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "赠送记录", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "pages/coupon/user-coupon-stale/index.html": {"window": {"navigationStyle": "default", "backgroundColor": "#f5f5f5", "enablePullDownRefresh": false, "componentPlaceholder": {"std-coupon-history-list": "view", "coupon-history-list": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "历史优惠券", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "pages/coupon/batch-coupons/index.html": {"window": {"navigationStyle": "default", "backgroundColor": "#f5f5f5", "enablePullDownRefresh": false, "componentPlaceholder": {"std-coupon-batch-gift": "view", "coupon-batch-gift": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "批量赠送", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "pages/coupon/customized-coupons/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "componentPlaceholder": {"std-coupon-customized-gift": "view", "coupon-customized-gift": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/invoice/index/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "发票助手"}}, "subpackages/invoice/apply/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"zm-button": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "提交申请"}}, "subpackages/invoice/order-list/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "发票助手"}}, "subpackages/invoice/instructions/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "开票须知"}}, "subpackages/pinDetails/pin-poster-edit.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/pinDetails/index.html": {"window": {"navigationStyle": "custom", "enablePullDownRefresh": false, "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/pinDetailsShare/index.html": {"window": {"navigationStyle": "default", "enablePullDownRefresh": true, "componentPlaceholder": {"pin-goods": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "拼单详情", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "subpackages/suggestionFeedback/list/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "建议反馈", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black", "enablePullDownRefresh": true, "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/suggestionFeedback/add/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "disableScroll": true, "navigationBarTitleText": "建议反馈", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "subpackages/marketing-detail/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"custom-rich-text": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/familyMemberCard/index/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "家庭会员卡", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/familyMemberCard/cartManager/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "卡片管理", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/familyMemberCard/addCard/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "卡片管理", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/familyMemberCard/cardSet/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "卡片管理", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/third-equity/index/index.html": {"window": {"navigationStyle": "default", "enablePullDownRefresh": true, "componentPlaceholder": {"empty-image": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/third-equity/detail/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"empty-image": "view", "pop-up": "empty-placeholder", "address-item": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "权益详情", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "subpackages/category-goods/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "适用商品", "componentPlaceholder": {"empty-image": "view", "apply-goods": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/user-task-center/index/index.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "", "componentPlaceholder": {"empty-image": "view", "pop-up": "empty-placeholder", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/user-task-center/record/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "任务记录", "componentPlaceholder": {"empty-image": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/user-task-center/rule/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "任务规则", "componentPlaceholder": {"empty-image": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/user-task-center/limitGoods/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "活动商品", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/exchange-goods/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "兑换商品", "enablePullDownRefresh": false, "componentPlaceholder": {"cart-control": "view", "food-sku": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/exchange-goods/new-index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "兑换商品", "enablePullDownRefresh": false, "componentPlaceholder": {"config-provider": "view", "cart-control": "view", "food-sku": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/group-restaurant/takefood/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"v-index-ad": "view", "advert-suspension-window": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "onReachBottomDistance": 0, "disableScroll": true, "navigationBarTitleText": ""}}, "subpackages/group-restaurant/confirm/index.html": {"window": {"navigationStyle": "custom", "backgroundColor": "#f7f7f7", "enablePullDownRefresh": false, "componentPlaceholder": {"v-gift-card": "view", "expected-arrival-time": "view", "pay-gift": "view", "cart-control": "view", "goods-coupons": "view", "flavor-card-popup": "view", "v-remark": "view", "v-pay-choose": "view", "coupon-package-confirm": "view", "show-toast": "view", "zm-button": "view", "box-confirm-popup": "view", "v-multi-select-coupon": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "disableScroll": true, "navigationBarTitleText": ""}}, "subpackages/group-restaurant/index/index.html": {"window": {"navigationStyle": "custom", "enablePullDownRefresh": false, "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/group-restaurant/multi/choice-multi.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"empty-image": "view", "address-item": "view", "swipe-cell": "empty-placeholder", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "选择收货地址", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "subpackages/group-restaurant/multi/list.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"empty-image": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/group-restaurant/multi/list-instore.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"empty-image": "view", "zm-button": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/group-restaurant/multi/list-search.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "搜索门店", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/group-restaurant/address/list/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"empty-image": "view", "swipe-cell": "empty-placeholder", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "收货地址", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "subpackages/group-purchase-coupon/list/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "抖音团购券", "componentPlaceholder": {"config-provider": "view", "empty-image": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/group-purchase-coupon/ali-list/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "团购券", "componentPlaceholder": {"v-error": "view", "empty-image": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/group-purchase-coupon/detail/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "团购兑换", "componentPlaceholder": {"config-provider": "view", "v-error": "view", "empty-image": "view", "food-sku": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/group-purchase-coupon/cancel-coupon-helper/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"shop-selector": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": ""}}, "subpackages/enterprise-wechat/customer/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "门店客服", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/enterprise-wechat/group-chat/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "门店群聊", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/lottery-reward/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "领取详情", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/lottery-reward/redirect.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "领取详情", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/lottery-reward/draw-down.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "实物奖品详情", "componentPlaceholder": {"empty-image": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/middlePage/multiple-person-ordering.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "disableScroll": true}}, "subpackages/login-auth/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/coupon-goods-list/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"attach-goods-popup": "view", "sell-time-popup": "view", "food-sku": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "disableScroll": true}}, "subpackages/coupon-goods-list/multi-list.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "选择适用门店", "componentPlaceholder": {"pop-up": "empty-placeholder", "store-list-item": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/coupon-goods-list/list.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"empty-image": "view", "zm-button": "view", "store-list-cart": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/site-pages/apply-goods/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "适用商品", "componentPlaceholder": {"empty-image": "view", "apply-goods": "view", "brand-select-head": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/site-pages/apply-stores/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "适用门店", "componentPlaceholder": {"empty-image": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/site-pages/apply-rights-cards/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"empty-image": "view", "brand-select-head": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "适用权益卡", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, "subpackages/site-pages/search-goods/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"cart-control": "view", "sell-time-popup": "view", "zm-button": "view", "food-sku": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "disableScroll": true}}, "subpackages/site-pages/password-setting/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "设置支付密码", "componentPlaceholder": {"password": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/site-pages/shopQqualification/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "服务资质", "componentPlaceholder": {"empty-image": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/site-pages/pay-success/index.html": {"window": {"navigationStyle": "default", "disableScroll": true, "navigationBarTitleText": "支付详情", "enablePullDownRefresh": false, "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/site-pages/member-card-activation/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/site-pages/packaging-fee-detail/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "包装费说明", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/site-pages/no-queuing-privilege/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "免排队特权", "componentPlaceholder": {"empty-image": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/site-pages/personalized-label/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/site-pages/personalized-label/edit.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/site-pages/shop-home/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"v-custom-page": "view", "v-call-bottom": "view", "store-info-item": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/site-pages/custom-web-view/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/site-pages/pay/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/site-pages/shop/other-sell-shop/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"store-list-item": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/strongestDecibel/index/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"empty-image": "view", "cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "navigationBarTitleText": "最强分贝", "navigationBarBackgroundColor": "#000000", "navigationBarTextStyle": "black"}}, "subpackages/live-player/list/index.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "直播大厅", "enablePullDownRefresh": true, "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "plugins/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "plugins2/index.html": {"window": {"navigationStyle": "default", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}}}, "subpackages/ar-cheers/index.html": {"window": {"navigationStyle": "custom", "componentPlaceholder": {"cell": "view", "half-bowl": "view", "love-dining": "view", "love-donate": "view", "material-cell": "view", "merc-list": "view", "plugin-demo": "view", "pullNewList": "view", "regift": "view", "send-coupon": "view", "studentVerify": "view"}, "disableScroll": true}}, "__wx__/functional-page.html": {"window": {}}, "__wx__/open-api-redirecting-page.html": {"window": {}}, "__wx__/choose-wifi-credential-page.html": {"window": {"navigationStyle": "custom"}}}, "permission": {"scope.userLocation": {"desc": "位置信息将用于为你推荐附近门店"}}, "global": {"window": {"navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black", "navigationStyle": "custom"}}, "plugins": {"materialPlugin": {"version": "1.0.5", "provider": "wx4d2de<PERSON>b3aed6e5a", "subpackage": "/plugins/"}, "sendCoupon": {"version": "latest", "provider": "wxf3f436ba9bd4be7b", "subpackage": "/plugins/"}, "studentVerify": {"version": "latest", "provider": "wxa16657d57059e0f0", "subpackage": "/plugins2/"}}, "tabBar": {"backgroundColor": "#fff", "selectedColor": "#CCCCCC", "list": [{"pagePath": "pages/index/index.html", "text": "", "code": ""}, {"pagePath": "pages/page/p3/index.html", "text": "", "code": "4NTPZGO8_3"}, {"pagePath": "pages/order/list/index.html", "text": "", "code": ""}, {"pagePath": "pages/user/index.html", "text": "", "code": ""}], "color": "#2a2a29", "customIcon": true, "navTemplate": "1", "borderStyle": "custom", "customBorderColor": "#CCCCCC", "custom": true}, "preloadRule": {"pages/index/index": {"network": "all", "packages": ["subpackages/tabbar-pages/takefood"]}, "pages/takefood/index": {"network": "all", "packages": ["subpackages/confirm", "subpackages/address", "subpackages/wishMatch"]}, "pages/pay/index/index": {"network": "all", "packages": ["subpackages/select-coupon"]}}, "ext": {"appName": "爷爷不泡茶", "appVersion": "5.86.0", "switch.nav.is_display": false, "switch.tech_support.is_display": true, "appid": "wx3423ef0c7b7f19af", "storeId": 216652, "env": "master", "colorTheme": "#FF99B1", "colorFont": "#FF99B1", "codes": ["", "", "4NTPZGO8_3", "", ""], "window": {"navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black", "navigationStyle": "custom"}, "tabBarList": [{"selectedIconPath": "https://images.qmai.cn/s216652/2025/07/16/e8f7260f8cd15872eb.png", "iconPath": "https://images.qmai.cn/s216652/2025/05/12/7404bba8d2aec05ee8.png", "code": "", "link_text": "首页", "pagePath": "pages/index/index", "text": ""}, {"pagePath": "pages/takefood/index", "code": "", "link_text": "商品列表点餐", "text": "", "iconPath": "https://images.qmai.cn/s216652/2025/05/12/98f591d037901f8404.png", "selectedIconPath": "https://images.qmai.cn/s216652/2025/07/16/7c83413e96d81e30a0.png"}, {"pagePath": "pages/page/p3/index", "code": "4NTPZGO8_3", "link_text": "自定义页面|会员活动中心", "text": "", "iconPath": "https://images.qmai.cn/s216652/2025/05/12/5e157f3cc40c27c948.png", "selectedIconPath": "https://images.qmai.cn/s216652/2025/07/16/60779911468f611ea6.png"}, {"selectedIconPath": "https://images.qmai.cn/s216652/2025/07/16/43fee5c9b701c722d4.png", "iconPath": "https://images.qmai.cn/s216652/2025/05/12/0e4eab732fa40f593e.png", "code": "", "link_text": "我的订单", "pagePath": "pages/order/list/index", "text": ""}, {"selectedIconPath": "https://images.qmai.cn/s216652/2025/07/16/f16de2d971fef7dc25.png", "iconPath": "https://images.qmai.cn/s216652/2025/05/12/3a0806d36a6b6409b9.png", "code": "", "link_text": "个人中心", "pagePath": "pages/user/index", "text": ""}], "tabBarExt": {"backgroundColor": "#fff", "selectedColor": "#CCCCCC", "color": "#2a2a29", "customIcon": true, "navTemplate": "1", "borderStyle": "custom", "customBorderColor": "#CCCCCC"}, "skeletonEnable": false, "skeletonPages": ["storeSelect", "goodsDetail", "orderDetail"], "activityMiniAppConfig": {"disablePinOrder": false, "enableCDN": true, "useCateringApi2": true, "useFlashLogin": false, "enablePromotion": false, "false": true, "useNoLogin": false, "useBwCouponCode": false, "useQMGateway": false, "useFusionGoodsApi": false, "disablePreOrder": false, "disableReport": false}}, "embeddedAppIdList": ["wx94057b049312ffec"], "renderer": {"allUsed": ["skyline", "webview"], "default": "webview"}, "requiredPrivateInfos": ["getLocation", "chooseLocation", "<PERSON><PERSON><PERSON><PERSON>"], "componentFramework": {"allUsed": ["exparser", "glass-easel"], "default": "exparser"}}