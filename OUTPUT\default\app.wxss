body,wx-view,wx-text,wx-div,wx-span,::before,::after {
    --un-rotate: 0;
    --un-rotate-x: 0;
    --un-rotate-y: 0;
    --un-rotate-z: 0;
    --un-scale-x: 1;
    --un-scale-y: 1;
    --un-scale-z: 1;
    --un-skew-x: 0;
    --un-skew-y: 0;
    --un-translate-x: 0;
    --un-translate-y: 0;
    --un-translate-z: 0;
    --un-pan-x: ;--un-pan-y:;
    --un-pinch-zoom: ;--un-scroll-snap-strictness: proximity;
    --un-ordinal: ;--un-slashed-zero:;
    --un-numeric-figure: ;--un-numeric-spacing:;
    --un-numeric-fraction: ;--un-border-spacing-x: 0;
    --un-border-spacing-y: 0;
    --un-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
    --un-ring-shadow: 0 0 rgba(0, 0, 0, 0);
    --un-shadow-inset: ;--un-shadow: 0 0 rgba(0, 0, 0, 0);
    --un-ring-inset: ;--un-ring-offset-width: 0px;
    --un-ring-offset-color: #fff;
    --un-ring-width: 0px;
    --un-ring-color: rgba(147, 197, 253, 0.5);
    --un-blur: ;--un-brightness:;
    --un-contrast: ;--un-drop-shadow:;
    --un-grayscale: ;--un-hue-rotate:;
    --un-invert: ;--un-saturate:;
    --un-sepia: ;--un-backdrop-blur:;
    --un-backdrop-brightness: ;--un-backdrop-contrast:;
    --un-backdrop-grayscale: ;--un-backdrop-hue-rotate:;
    --un-backdrop-invert: ;--un-backdrop-opacity:;
    --un-backdrop-saturate: ;--un-backdrop-sepia:
}

.wx-button-cover-view-wrapper {
    width: 100%;
    height: 100%;
    opacity: .1
}

wx-view,wx-text {
    font-family: "Open Sans",-apple-system,BlinkMacSystemFont,"Helvetica Neue",Helvetica,"Segoe UI",Arial,Roboto,"PingFang SC",miui,"Hiragino Sans GB","Microsoft Yahei",sans-serif,Simsun
}

wx-button[type=primary] {
    background-color: #07c160
}

wx-button[type=primary]::after {
    display: none
}

wx-button[type=primary][disabled] {
    background-color: #b4ecce
}

wx-button[type=primary].button-hover {
    background-color: #06af56
}

wx-button {
    padding: 0;
    vertical-align: middle;
    border: 0;
}

wx-button::after {
    -moz-opacity: 0;
    opacity: 0;
    filter: alpha(opacity=0);
}

page {
    box-sizing: border-box;
    color: #333;
    font-family: "Open Sans",-apple-system,BlinkMacSystemFont,"Helvetica Neue",Helvetica,"Segoe UI",Arial,Roboto,"PingFang SC",miui,"Hiragino Sans GB","Microsoft Yahei",sans-serif;
    background-color: #f7f7f7;
}

::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    color: rgba(0,0,0,0)
}

@font-face {
    font-family:"i";src:url('https://images.qmai.cn/resource/20241216133947/2025/07/18/font_4785629_p9i3p3s3byo.woff2?t=1752818896128') format('woff2'),url('https://images.qmai.cn/resource/20241216133947/2025/07/18/font_4785629_p9i3p3s3byo.woff?t=1752818896128') format('woff'),url('https://images.qmai.cn/resource/20241216133947/2025/07/18/font_4785629_p9i3p3s3byo.ttf?t=1752818896128') format('truetype'),url('https://images.qmai.cn/resource/20241216133947/2025/07/18/font_4785629_p9i3p3s3byo.svg?t=1752818896128#i') format('svg')
}

@keyframes content-scroll-up {
    0% {
        transform: translate3d(0,0,0)
    }

    100% {
        transform: translate3d(0,-50%,0)
    }
}

@keyframes content-scroll-left {
    0% {
        transform: translate3d(0,0,0)
    }

    100% {
        transform: translate3d(-50%,0,0)
    }
}

@keyframes slide-in-from-top {
    0% {
        top: -100%
    }

    100% {
        top: 50%
    }
}

@keyframes slide-out-down {
    0% {
        transform: translate3d(0,0,0)
    }

    100% {
        transform: translate3d(0,100%,0)
    }
}

@keyframes slide-out-down-and-back {
    0% {
        transform: translate3d(0,-10%,0)
    }

    50% {
        transform: translate3d(0,10%,0)
    }

    100% {
        transform: translate3d(0,-10%,0)
    }
}

@keyframes slide-out-down-and-hide {
    from {
        transform: translate3d(0,0,0)
    }

    to {
        transform: translate3d(0,100%,0);
        visibility: hidden
    }
}

@keyframes slide-out-bottom {
    0% {
        bottom: 0
    }

    100% {
        bottom: -100%
    }
}

@keyframes slide-in-bottom {
    0% {
        bottom: -100%
    }

    100% {
        bottom: 0
    }
}

@keyframes slide-in-up {
    0% {
        transform: translate3d(0,100%,0)
    }

    100% {
        transform: translate3d(0,0,0)
    }
}

@keyframes slide-in-vh-down {
    0% {
        transform: translate3d(0,-100vh,0)
    }

    100% {
        transform: translate3d(0,0,0)
    }
}

@keyframes slide-in-vh-up {
    0% {
        transform: translate3d(0,100vh,0)
    }

    100% {
        transform: translate3d(0,0,0)
    }
}

@keyframes shake-on-load {
    0% {
        transform: rotate(-15deg)
    }

    50% {
        transform: rotate(0deg)
    }

    75% {
        transform: rotate(3deg)
    }

    100% {
        transform: rotate(0deg)
    }
}

@keyframes shake-horizontal {
    10%,90% {
        transform: translate3d(-1px,0,0)
    }

    20%,80% {
        transform: translate3d(2px,0,0)
    }

    30%,70% {
        transform: translate3d(-4px,0,0)
    }

    40%,60% {
        transform: translate3d(4px,0,0)
    }

    50% {
        transform: translate3d(-4px,0,0)
    }
}

@keyframes shake-steady {
    0%,100% {
        transform: translate3d(0,0,0)
    }

    10%,30%,50%,70%,90% {
        transform: translate3d(-10rpx,0,0)
    }

    20%,40%,60%,80% {
        transform: translate3d(10rpx,0,0)
    }
}

@keyframes shake-rotate {
    0% {
        transform: rotate(0deg)
    }

    10%,20%,30% {
        transform: rotate(-3deg)
    }

    15%,25%,35% {
        transform: rotate(3deg)
    }

    40% {
        transform: rotate(-2deg)
    }

    45% {
        transform: rotate(2deg)
    }

    50% {
        transform: rotate(0deg)
    }

    100% {
        transform: rotate(0deg)
    }
}

@keyframes fade-in {
    0% {
        opacity: 0
    }

    100% {
        opacity: 1
    }
}

@keyframes fade-in-40 {
    0% {
        opacity: 0
    }

    100% {
        opacity: 0.4
    }
}

@keyframes fade-in-50 {
    0% {
        opacity: 0
    }

    100% {
        opacity: 0.5
    }
}

@keyframes fade-in-80 {
    0% {
        opacity: 0
    }

    100% {
        opacity: 0.8
    }
}

@keyframes fade-in-88 {
    0% {
        opacity: 0
    }

    100% {
        opacity: 0.88
    }
}

@keyframes fade-out {
    0% {
        opacity: 1
    }

    100% {
        opacity: 0
    }
}

@keyframes fade-out-up {
    0% {
        transform: translate3d(0,0,0);
        opacity: 1
    }

    100% {
        transform: translate3d(0,-100%,0);
        opacity: 0
    }
}

@keyframes fade-in-up {
    0% {
        transform: translate3d(0,100%,0);
        opacity: 0
    }

    100% {
        transform: translate3d(0,0,0);
        opacity: 1
    }
}

@keyframes fade-in-down {
    0% {
        transform: translate3d(0,-100%,0);
        opacity: 0
    }

    100% {
        transform: translate3d(0,0,0);
        opacity: 1
    }
}

@keyframes fade-in-left-375px {
    0% {
        opacity: 0;
        transform: translate3d(-375px,0,0)
    }

    100% {
        opacity: 1;
        transform: translate3d(0,0,0)
    }
}

@keyframes fade-in-right-375px {
    0% {
        opacity: 0;
        transform: translate3d(375px,0,0)
    }

    100% {
        opacity: 1;
        transform: translate3d(0,0,0)
    }
}

@keyframes shade-in {
    0% {
        transform: translateY(100%);
        opacity: 0.1
    }

    100% {
        transform: translateY(0);
        opacity: 1
    }
}

@keyframes shade-out {
    0% {
        transform: translateY(0);
        opacity: 1
    }

    100% {
        transform: translateY(100%);
        opacity: 0.1
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg)
    }

    100% {
        transform: rotate(360deg)
    }
}

@keyframes spin-y {
    0% {
        transform: rotateY(0deg)
    }

    100% {
        transform: rotateY(360deg)
    }
}

@keyframes reveal-flip {
    0% {
        transform: rotateY(0deg)
    }

    100% {
        transform: rotateY(180deg)
    }
}

@keyframes reset-flip {
    0% {
        transform: rotateY(180deg)
    }

    100% {
        transform: rotateY(360deg)
    }
}

@keyframes scale-in {
    0% {
        transform: scale(0)
    }

    100% {
        transform: scale(1)
    }
}

@keyframes breath-zoom {
    0% {
        transform: scale(0.88)
    }

    50% {
        transform: scale(1)
    }

    100% {
        transform: scale(0.88)
    }
}

@keyframes jump-up {
    0% {
        transform: translate3d(0,0,0)
    }

    100% {
        transform: translate3d(0,-1em,0)
    }
}

@keyframes pulse {
    0% {
        transform: scale3d(1,1,1)
    }

    50% {
        transform: scale3d(1.2,1.2,1.2)
    }

    100% {
        transform: scale3d(1,1,1)
    }
}

@keyframes pulse-text {
    0% {
        height: 48rpx
    }

    50% {
        height: 20rpx
    }

    100% {
        height: 48rpx
    }
}

@keyframes pulse-double {
    0% {
        transform: scale(1)
    }

    14% {
        transform: scale(1.3)
    }

    28% {
        transform: scale(1)
    }

    42% {
        transform: scale(1.3)
    }

    70% {
        transform: scale(1)
    }
}

@keyframes appear {
    0% {
        visibility: hidden
    }

    100% {
        visibility: visible
    }
}

@keyframes disappear {
    0% {
        visibility: visible
    }

    100% {
        visibility: hidden
    }
}

@keyframes marquee-93-left {
    0% {
        transform: translateX(0)
    }

    100% {
        transform: translateX(-93.3%)
    }
}

@keyframes marquee-105-slide {
    0% {
        transform: translateX(100%)
    }

    100% {
        transform: translateX(-105%)
    }
}

@keyframes tada {
    from {
        transform: scale3d(1,1,1)
    }

    10%,20% {
        transform: scale3d(0.9,0.9,0.9) rotate3d(0,0,1,-3deg)
    }

    30%,50%,70%,90% {
        transform: scale3d(1.1,1.1,1.1) rotate3d(0,0,1,3deg)
    }

    40%,60%,80% {
        transform: scale3d(1.1,1.1,1.1) rotate3d(0,0,1,-3deg)
    }

    to {
        transform: scale3d(1,1,1)
    }
}

@keyframes light-scale {
    0% {
        transform: scale(0);
        opacity: 1
    }

    100% {
        transform: scale(1.2);
        opacity: 1
    }
}

@keyframes light-rotate {
    0% {
        transform: scale(1.2) rotate(0);
        opacity: 1
    }

    100% {
        transform: scale(1.2) rotate(360deg);
        opacity: 1
    }
}

@keyframes slide-down-50px {
    0% {
        bottom: 50px
    }

    100% {
        bottom: 0
    }
}

@keyframes slide-up-50px {
    0% {
        top: 50px
    }

    100% {
        top: 0
    }
}

@keyframes loop-up {
    0% {
        transform: translateY(0)
    }

    100% {
        transform: translateY(-100%)
    }
}

@keyframes twinkling {
    0% {
        background: #fff
    }

    100% {
        background: #666
    }
}

@keyframes position-200-left {
    0% {
        left: 100%
    }

    100% {
        left: -200%
    }
}

@keyframes scale-fade-out {
    0% {
        opacity: 1;
        transform: scale(1)
    }

    100% {
        opacity: 0;
        transform: scale(0.5)
    }
}

@keyframes flash-across {
    0% {
        transform: translateX(-100%)
    }

    15% {
        transform: translateX(-100%)
    }

    30% {
        transform: translateX(100%)
    }

    100% {
        transform: translateX(100%)
    }
}

@keyframes rise-step {
    0% {
        transform: translateY(0)
    }

    5% {
        transform: translateY(-25%)
    }

    50% {
        transform: translateY(-25%)
    }

    55% {
        transform: translateY(-50%)
    }

    100% {
        transform: translateY(-50%)
    }
}

@keyframes pop-in {
    0% {
        transform: scale(0)
    }

    9% {
        transform: scale(0)
    }

    18% {
        transform: scale(1.04)
    }

    22% {
        transform: scale(0.98)
    }

    26% {
        transform: scale(1.02)
    }

    30% {
        transform: scale(1)
    }

    100% {
        transform: scale(1)
    }
}

@keyframes blind-box-reveal {
    0% {
        transform: translateY(-100%);
        opacity: 0
    }

    100% {
        transform: translateY(-50%);
        opacity: 1
    }
}

@keyframes like-bounce {
    0% {
        transform: scale(1);
        background-color: #fff
    }

    25% {
        transform: scale(0.9)
    }

    50% {
        transform: scale(0.85);
        background-color: #ffe7e9
    }

    75% {
        transform: scale(0.9)
    }

    100% {
        transform: scale(1);
        background-color: #fff
    }
}

@keyframes like-pulse {
    0% {
        transform: scale(1)
    }

    25% {
        transform: scale(0.9)
    }

    50% {
        transform: scale(0.85)
    }

    75% {
        transform: scale(0.9)
    }

    100% {
        transform: scale(1)
    }
}

@keyframes ripple-out {
    0% {
        transform: scale(0.5);
        opacity: 1
    }

    100% {
        transform: scale(4);
        opacity: 0
    }
}

@keyframes light-swipe {
    0% {
        transform: translateX(20rpx)
    }

    80% {
        opacity: 1
    }

    81% {
        opacity: 0
    }

    100% {
        transform: translateX(60vw);
        opacity: 0
    }
}

@keyframes shadow-spread {
    0% {
        transform: scale(0.2)
    }

    100% {
        transform: scale(0.7)
    }
}

@keyframes order-expand {
    0% {
        height: 164rpx;
        border-radius: 50%
    }

    100% {
        height: 286rpx;
        border-radius: 160rpx
    }
}

@keyframes order-collapse {
    0% {
        height: 286rpx;
        border-radius: 160rpx
    }

    100% {
        height: 164rpx;
        border-radius: 50%
    }
}

@keyframes order-expand-2 {
    0% {
        height: 148rpx;
        border-radius: 50%
    }

    100% {
        height: 270rpx;
        border-radius: 160rpx
    }
}

@keyframes order-collapse-2 {
    0% {
        height: 270rpx;
        border-radius: 160rpx
    }

    100% {
        height: 148rpx;
        border-radius: 50%
    }
}

@keyframes cart-bar-collapse {
    0% {
        padding-left: 40rpx;
        color: #fff;
        background-color: #323232
    }

    100% {
        padding-left: 20rpx;
        color: #323232;
        background-color: #fff
    }
}

@keyframes cart-bar-expand {
    0% {
        padding-left: 20rpx;
        color: #323232;
        background-color: #fff
    }

    100% {
        padding-left: 40rpx;
        color: #fff;
        background-color: #323232
    }
}

@keyframes flip-back {
    0% {
        transform: rotateY(-180deg)
    }

    100% {
        transform: rotateY(0deg)
    }
}

@keyframes three-cinton-1 {
    0% {
        top: 0;
        left: 0
    }

    100% {
        top: 0;
        left: 170rpx
    }
}

@keyframes three-cinton-3 {
    0% {
        top: 0;
        left: 0
    }

    100% {
        top: 0;
        left: -170rpx
    }
}

@keyframes six-cinton-1 {
    0% {
        top: 0;
        left: 0
    }

    100% {
        top: 127rpx;
        left: 170rpx
    }
}

@keyframes six-cinton-2 {
    0% {
        top: 0;
        left: 0
    }

    100% {
        top: 127rpx;
        left: 0
    }
}

@keyframes six-cinton-3 {
    0% {
        top: 0;
        left: 0
    }

    100% {
        top: 127rpx;
        left: -170rpx
    }
}

@keyframes six-cinton-4 {
    0% {
        top: 0;
        left: 0
    }

    100% {
        top: -127rpx;
        left: 170rpx
    }
}

@keyframes six-cinton-5 {
    0% {
        top: 0;
        left: 0
    }

    100% {
        top: -127rpx;
        left: 0
    }
}

@keyframes six-cinton-6 {
    0% {
        top: 0;
        left: 0
    }

    100% {
        top: -127rpx;
        left: -170rpx
    }
}

@keyframes nine-cinton-1 {
    0% {
        top: 0;
        left: 0
    }

    100% {
        top: 170rpx;
        left: 174rpx
    }
}

@keyframes nine-cinton-2 {
    0% {
        top: 0;
        left: 0
    }

    100% {
        top: 170rpx;
        left: 0
    }
}

@keyframes nine-cinton-3 {
    0% {
        top: 0;
        left: 0
    }

    100% {
        top: 170rpx;
        left: -174rpx
    }
}

@keyframes nine-cinton-4 {
    0% {
        top: 0;
        left: 0
    }

    100% {
        top: 0;
        left: 174rpx
    }
}

@keyframes nine-cinton-6 {
    0% {
        top: 0;
        left: 0
    }

    100% {
        top: 0;
        left: -174rpx
    }
}

@keyframes nine-cinton-7 {
    0% {
        top: 0;
        left: 0
    }

    100% {
        top: -170rpx;
        left: 174rpx
    }
}

@keyframes nine-cinton-8 {
    0% {
        top: 0;
        left: 0
    }

    100% {
        top: -170rpx;
        left: 0
    }
}

@keyframes nine-cinton-9 {
    0% {
        top: 0;
        left: 0
    }

    100% {
        top: -170rpx;
        left: -174rpx
    }
}

.i-fold {
    font-family: "i";
}

.i-fold:before {
    content: "\e862";
}

.i-exp {
    font-family: "i";
}

.i-exp:before {
    content: "\e85e";
}

.i-jifenjilu {
    font-family: "i";
}

.i-jifenjilu:before {
    content: "\e85b";
}

.i-jifenmingxi {
    font-family: "i";
}

.i-jifenmingxi:before {
    content: "\e85c";
}

.i-jifenguize {
    font-family: "i";
}

.i-jifenguize:before {
    content: "\e85d";
}

.i-fanxian {
    font-family: "i";
}

.i-fanxian:before {
    content: "\e857";
}

.i-sousuo1 {
    font-family: "i";
}

.i-sousuo1:before {
    content: "\e855";
}

.i-ziqu2 {
    font-family: "i";
}

.i-ziqu2:before {
    content: "\e84c";
}

.i-haibao1 {
    font-family: "i";
}

.i-haibao1:before {
    content: "\e846";
}

.i-a-Property1kuaidi {
    font-family: "i";
}

.i-a-Property1kuaidi:before {
    content: "\e811";
}

.i-fankui {
    font-family: "i";
}

.i-fankui:before {
    content: "\e810";
}

.i-tongzhi {
    font-family: "i";
}

.i-tongzhi:before {
    content: "\e7d3";
}

.i-juli1 {
    font-family: "i";
}

.i-juli1:before {
    content: "\e7da";
}

.i-yishoucang {
    font-family: "i";
}

.i-yishoucang:before {
    content: "\e7d1";
}

.i-weishoucang {
    font-family: "i";
}

.i-weishoucang:before {
    content: "\e7d2";
}

.i-Frame {
    font-family: "i";
}

.i-Frame:before {
    content: "\e7d0";
}

.i-fenge {
    font-family: "i";
}

.i-fenge:before {
    content: "\e7cd";
}

.i-quanbg {
    font-family: "i";
}

.i-quanbg:before {
    content: "\e7cf";
}

.i-Union {
    font-family: "i";
}

.i-Union:before {
    content: "\e7c9";
}

.i-kaifapiao {
    font-family: "i";
}

.i-kaifapiao:before {
    content: "\e7c8";
}

.i-pindan {
    font-family: "i";
}

.i-pindan:before {
    content: "\e7c7";
}

.i-sousuo {
    font-family: "i";
}

.i-sousuo:before {
    content: "\e7b0";
}

.i-paymentfailed {
    font-family: "i";
}

.i-paymentfailed:before {
    content: "\e7aa";
}

.i-medal {
    font-family: "i";
}

.i-medal:before {
    content: "\e77e";
}

.i-back {
    font-family: "i";
}

.i-back:before {
    content: "\e6f0";
}

.i-search3 {
    font-family: "i";
}

.i-search3:before {
    content: "\e8ef";
}

.i-cart1 {
    font-family: "i";
}

.i-cart1:before {
    content: "\e81c";
}

.i-add2 {
    font-family: "i";
}

.i-add2:before {
    content: "\e81d";
}

.i-close1 {
    font-family: "i";
}

.i-close1:before {
    content: "\e81e";
}

.i-delete {
    font-family: "i";
}

.i-delete:before {
    content: "\e829";
}

.i-arrow-r1 {
    font-family: "i";
}

.i-arrow-r1:before {
    content: "\e82a";
}

.i-order {
    font-family: "i";
}

.i-order:before {
    content: "\e82b";
}

.i-cart-2 {
    font-family: "i";
}

.i-cart-2:before {
    content: "\e82c";
}

.i-close-2 {
    font-family: "i";
}

.i-close-2:before {
    content: "\e82d";
}

.i-addr {
    font-family: "i";
}

.i-addr:before {
    content: "\e82e";
}

.i-refund {
    font-family: "i";
}

.i-refund:before {
    content: "\e82f";
}

.i-rgoods {
    font-family: "i";
}

.i-rgoods:before {
    content: "\e832";
}

.i-upload {
    font-family: "i";
}

.i-upload:before {
    content: "\e833";
}

.i-wx {
    font-family: "i";
}

.i-wx:before {
    content: "\e77c";
}

.i-balance1 {
    font-family: "i";
}

.i-balance1:before {
    content: "\e77d";
}

.i-a-lujing4 {
    font-family: "i";
}

.i-a-lujing4:before {
    content: "\e7c5";
}

.i-juxing1 {
    font-family: "i";
}

.i-juxing1:before {
    content: "\e7c6";
}

.i-bonus-line11 {
    font-family: "i";
}

.i-bonus-line11:before {
    content: "\e7ce";
}

.i-lingqu-jiant {
    font-family: "i";
}

.i-lingqu-jiant:before {
    content: "\e84a";
}

.i-qm1 {
    font-family: "i";
}

.i-qm1:before {
    content: "\e7d4";
}

.i-timerauto {
    font-family: "i";
}

.i-timerauto:before {
    content: "\e7d5";
}

.i-jifen-bg {
    font-family: "i";
}

.i-jifen-bg:before {
    content: "\e84e";
}

.i-jifen1 {
    font-family: "i";
}

.i-jifen1:before {
    content: "\e7d7";
}

.i-fenlei {
    font-family: "i";
}

.i-fenlei:before {
    content: "\e7ea";
}

.i-a-juli1 {
    font-family: "i";
}

.i-a-juli1:before {
    content: "\e7ec";
}

.i-alipay {
    font-family: "i";
}

.i-alipay:before {
    content: "\e7f1";
}

.i-mianfei {
    font-family: "i";
}

.i-mianfei:before {
    content: "\e7f2";
}

.i-jifen11 {
    font-family: "i";
}

.i-jifen11:before {
    content: "\e7f3";
}

.i-fudingjin {
    font-family: "i";
}

.i-fudingjin:before {
    content: "\e87a";
}

.i-fuweikuan {
    font-family: "i";
}

.i-fuweikuan:before {
    content: "\e87b";
}

.i-fahuo {
    font-family: "i";
}

.i-fahuo:before {
    content: "\e87c";
}

.i-a-xingzhuangjiehebeifen3 {
    font-family: "i";
}

.i-a-xingzhuangjiehebeifen3:before {
    content: "\e7f4";
}

.i-shoujia-yuce {
    font-family: "i";
}

.i-shoujia-yuce:before {
    content: "\e7f5";
}

.i-a-bianzu10 {
    font-family: "i";
}

.i-a-bianzu10:before {
    content: "\e7f7";
}

.i-a-bianzu9 {
    font-family: "i";
}

.i-a-bianzu9:before {
    content: "\e7f8";
}

.i-a-bianzu23 {
    font-family: "i";
}

.i-a-bianzu23:before {
    content: "\e7f9";
}

.i-bianzu {
    font-family: "i";
}

.i-bianzu:before {
    content: "\e804";
}

.i-xingzhuangjiehe1 {
    font-family: "i";
}

.i-xingzhuangjiehe1:before {
    content: "\e805";
}

.i-a-bianzu24 {
    font-family: "i";
}

.i-a-bianzu24:before {
    content: "\e806";
}

.i-check-outlined {
    font-family: "i";
}

.i-check-outlined:before {
    content: "\e807";
}

.i-a-bianzu14 {
    font-family: "i";
}

.i-a-bianzu14:before {
    content: "\e808";
}

.i-a-bianzu27 {
    font-family: "i";
}

.i-a-bianzu27:before {
    content: "\e809";
}

.i-a-bianzu25 {
    font-family: "i";
}

.i-a-bianzu25:before {
    content: "\e80a";
}

.i-a-bianzu28 {
    font-family: "i";
}

.i-a-bianzu28:before {
    content: "\e80b";
}

.i-a-bianzu26 {
    font-family: "i";
}

.i-a-bianzu26:before {
    content: "\e80c";
}

.i-a-bianzu17 {
    font-family: "i";
}

.i-a-bianzu17:before {
    content: "\e80d";
}

.i-dizhi4 {
    font-family: "i";
}

.i-dizhi4:before {
    content: "\e80e";
}

.i-bianji22 {
    font-family: "i";
}

.i-bianji22:before {
    content: "\e872";
}

.i-download1 {
    font-family: "i";
}

.i-download1:before {
    content: "\e812";
}

.i-mobile {
    font-family: "i";
}

.i-mobile:before {
    content: "\e819";
}

.i-money1 {
    font-family: "i";
}

.i-money1:before {
    content: "\e835";
}

.i-sort-down {
    font-family: "i";
}

.i-sort-down:before {
    content: "\e837";
}

.i-sort-up {
    font-family: "i";
}

.i-sort-up:before {
    content: "\e838";
}

.i-saoyisao {
    font-family: "i";
}

.i-saoyisao:before {
    content: "\e839";
}

.i-fenxiang3 {
    font-family: "i";
}

.i-fenxiang3:before {
    content: "\e91b";
}

.i-rili2 {
    font-family: "i";
}

.i-rili2:before {
    content: "\e83a";
}

.i-card {
    font-family: "i";
}

.i-card:before {
    content: "\e83b";
}

.i-jian2 {
    font-family: "i";
}

.i-jian2:before {
    content: "\e840";
}

.i-shoucang2 {
    font-family: "i";
}

.i-shoucang2:before {
    content: "\e849";
}

.i-shoucang21 {
    font-family: "i";
}

.i-shoucang21:before {
    content: "\e84b";
}

.i-jt-t {
    font-family: "i";
}

.i-jt-t:before {
    content: "\e850";
}

.i-guanbi4 {
    font-family: "i";
}

.i-guanbi4:before {
    content: "\e871";
}

.i-zhankai {
    font-family: "i";
}

.i-zhankai:before {
    content: "\e874";
}

.i-canyudati {
    font-family: "i";
}

.i-canyudati:before {
    content: "\e875";
}

.i-lengxing {
    font-family: "i";
}

.i-lengxing:before {
    content: "\e876";
}

.i-yaoqingdeka {
    font-family: "i";
}

.i-yaoqingdeka:before {
    content: "\e878";
}

.i-xinxirenzheng {
    font-family: "i";
}

.i-xinxirenzheng:before {
    content: "\e879";
}

.i-finish-order {
    font-family: "i";
}

.i-finish-order:before {
    content: "\e87e";
}

.i-yaoqing {
    font-family: "i";
}

.i-yaoqing:before {
    content: "\e880";
}

.i-zhengque {
    font-family: "i";
}

.i-zhengque:before {
    content: "\e881";
}

.i-cuowu {
    font-family: "i";
}

.i-cuowu:before {
    content: "\e882";
}

.i-shandian {
    font-family: "i";
}

.i-shandian:before {
    content: "\e883";
}

.i-home1 {
    font-family: "i";
}

.i-home1:before {
    content: "\e884";
}

.i-a-wanchengquanyikagoumai1x {
    font-family: "i";
}

.i-a-wanchengquanyikagoumai1x:before {
    content: "\e885";
}

.i-wanchengshoucixiadan1 {
    font-family: "i";
}

.i-wanchengshoucixiadan1:before {
    content: "\e886";
}

.i-fafangjianglizhizhanghu {
    font-family: "i";
}

.i-fafangjianglizhizhanghu:before {
    content: "\e887";
}

.i-fenxianglianjiegeihaoyou1 {
    font-family: "i";
}

.i-fenxianglianjiegeihaoyou1:before {
    content: "\e888";
}

.i-wanchengdengluzhuce {
    font-family: "i";
}

.i-wanchengdengluzhuce:before {
    content: "\e889";
}

.i-haoyoujieshouyaoqing {
    font-family: "i";
}

.i-haoyoujieshouyaoqing:before {
    content: "\e88a";
}

.i-a-zu13421x {
    font-family: "i";
}

.i-a-zu13421x:before {
    content: "\e88b";
}

.i-a-zhankai1x {
    font-family: "i";
}

.i-a-zhankai1x:before {
    content: "\e88d";
}

.i-fuhao {
    font-family: "i";
}

.i-fuhao:before {
    content: "\e88e";
}

.i-fuhao-copy {
    font-family: "i";
}

.i-fuhao-copy:before {
    content: "\e9cc";
}

.i-a-liwu {
    font-family: "i";
}

.i-a-liwu:before {
    content: "\e88f";
}

.i-a-jifen {
    font-family: "i";
}

.i-a-jifen:before {
    content: "\e890";
}

.i-xunzhang1 {
    font-family: "i";
}

.i-xunzhang1:before {
    content: "\e892";
}

.i-download2 {
    font-family: "i";
}

.i-download2:before {
    content: "\e89c";
}

.i-dizhi3 {
    font-family: "i";
}

.i-dizhi3:before {
    content: "\e799";
}

.i-chaidanjiantou-you {
    font-family: "i";
}

.i-chaidanjiantou-you:before {
    content: "\e7a5";
}

.i-wenhao {
    font-family: "i";
}

.i-wenhao:before {
    content: "\e7b1";
}

.i-moren1 {
    font-family: "i";
}

.i-moren1:before {
    content: "\e7b6";
}

.i-moren2 {
    font-family: "i";
}

.i-moren2:before {
    content: "\e7b7";
}

.i-xz-xyy2 {
    font-family: "i";
}

.i-xz-xyy2:before {
    content: "\e7c1";
}

.i-wenhao1 {
    font-family: "i";
}

.i-wenhao1:before {
    content: "\e7c2";
}

.i-huaban21 {
    font-family: "i";
}

.i-huaban21:before {
    content: "\e7db";
}

.i-arrow-up-bold {
    font-family: "i";
}

.i-arrow-up-bold:before {
    content: "\e6ae";
}

.i-arrow-down-bold {
    font-family: "i";
}

.i-arrow-down-bold:before {
    content: "\e6af";
}

.i-checked {
    font-family: "i";
}

.i-checked:before {
    content: "\e6f1";
}

.i-unchecked {
    font-family: "i";
}

.i-unchecked:before {
    content: "\e6fb";
}

.i-selected {
    font-family: "i";
}

.i-selected:before {
    content: "\e81f";
}

.i-not-selected {
    font-family: "i";
}

.i-not-selected:before {
    content: "\e820";
}

.i-delete-2 {
    font-family: "i";
}

.i-delete-2:before {
    content: "\e830";
}

.i-nav {
    font-family: "i";
}

.i-nav:before {
    content: "\e715";
}

.i-tel {
    font-family: "i";
}

.i-tel:before {
    content: "\e76d";
}

.i-voice {
    font-family: "i";
}

.i-voice:before {
    content: "\e701";
}

.i-bag {
    font-family: "i";
}

.i-bag:before {
    content: "\e76e";
}

.i-search2 {
    font-family: "i";
}

.i-search2:before {
    content: "\e770";
}

.i-arrow-b {
    font-family: "i";
}

.i-arrow-b:before {
    content: "\e772";
}

.i-time {
    font-family: "i";
}

.i-time:before {
    content: "\e774";
}

.i-tel-2 {
    font-family: "i";
}

.i-tel-2:before {
    content: "\e775";
}

.i-cert {
    font-family: "i";
}

.i-cert:before {
    content: "\e776";
}

.i-file {
    font-family: "i";
}

.i-file:before {
    content: "\e777";
}

.i-address {
    font-family: "i";
}

.i-address:before {
    content: "\e778";
}

.i-arrow-t {
    font-family: "i";
}

.i-arrow-t:before {
    content: "\e831";
}

.i-arrow-r-2 {
    font-family: "i";
}

.i-arrow-r-2:before {
    content: "\e847";
}

.i-edit {
    font-family: "i";
}

.i-edit:before {
    content: "\e6a7";
}

.i-guanbi2 {
    font-family: "i";
}

.i-guanbi2:before {
    content: "\e76b";
}

.i-huabi {
    font-family: "i";
}

.i-huabi:before {
    content: "\e767";
}

.i-guanbi1 {
    font-family: "i";
}

.i-guanbi1:before {
    content: "\e768";
}

.i-xiangpica {
    font-family: "i";
}

.i-xiangpica:before {
    content: "\e769";
}

.i-queren {
    font-family: "i";
}

.i-queren:before {
    content: "\e76a";
}

.i-wenzi {
    font-family: "i";
}

.i-wenzi:before {
    content: "\e765";
}

.i-tuya {
    font-family: "i";
}

.i-tuya:before {
    content: "\e766";
}

.i-huiche {
    font-family: "i";
}

.i-huiche:before {
    content: "\e760";
}

.i-chexiao {
    font-family: "i";
}

.i-chexiao:before {
    content: "\e761";
}

.i-xiahuaxian {
    font-family: "i";
}

.i-xiahuaxian:before {
    content: "\e762";
}

.i-cuti {
    font-family: "i";
}

.i-cuti:before {
    content: "\e763";
}

.i-xieti {
    font-family: "i";
}

.i-xieti:before {
    content: "\e764";
}

.i-quote-left {
    font-family: "i";
}

.i-quote-left:before {
    content: "\e6a5";
}

.i-quote-right {
    font-family: "i";
}

.i-quote-right:before {
    content: "\e6a6";
}

.i-jia1 {
    font-family: "i";
}

.i-jia1:before {
    content: "\e75c";
}

.i-jian {
    font-family: "i";
}

.i-jian:before {
    content: "\e75e";
}

.i-anquan {
    font-family: "i";
}

.i-anquan:before {
    content: "\e75b";
}

.i-guanbi {
    font-family: "i";
}

.i-guanbi:before {
    content: "\e758";
}

.i-a-Frame82 {
    font-family: "i";
}

.i-a-Frame82:before {
    content: "\e759";
}

.i-ziquxin1 {
    font-family: "i";
}

.i-ziquxin1:before {
    content: "\e751";
}

.i-waimai11 {
    font-family: "i";
}

.i-waimai11:before {
    content: "\e755";
}

.i-pengzhangxiao2 {
    font-family: "i";
}

.i-pengzhangxiao2:before {
    content: "\e750";
}

.i-pengzhangxiao1 {
    font-family: "i";
}

.i-pengzhangxiao1:before {
    content: "\e753";
}

.i-pengzhangda1 {
    font-family: "i";
}

.i-pengzhangda1:before {
    content: "\e754";
}

.i-ziquxin {
    font-family: "i";
}

.i-ziquxin:before {
    content: "\e752";
}

.i-cup {
    font-family: "i";
}

.i-cup:before {
    content: "\e75a";
}

.i-design-member-agreement {
    font-family: "i";
}

.i-design-member-agreement:before {
    content: "\ed4c";
}

.i-design-store-order {
    font-family: "i";
}

.i-design-store-order:before {
    content: "\ed36";
}

.i-design-pay-order {
    font-family: "i";
}

.i-design-pay-order:before {
    content: "\ed37";
}

.i-design-member-order {
    font-family: "i";
}

.i-design-member-order:before {
    content: "\ed39";
}

.i-design-voucher-order {
    font-family: "i";
}

.i-design-voucher-order:before {
    content: "\ed3a";
}

.i-design-pin-coupon-order {
    font-family: "i";
}

.i-design-pin-coupon-order:before {
    content: "\ed3b";
}

.i-design-gift-order {
    font-family: "i";
}

.i-design-gift-order:before {
    content: "\ed38";
}

.i-kefu1 {
    font-family: "i";
}

.i-kefu1:before {
    content: "\e69f";
}

.i-kefu2 {
    font-family: "i";
}

.i-kefu2:before {
    content: "\e85a";
}

.i-design-value-add {
    font-family: "i";
}

.i-design-value-add:before {
    content: "\ed1b";
}

.i-design-free-delivery {
    font-family: "i";
}

.i-design-free-delivery:before {
    content: "\ed1c";
}

.i-design-multi-exp {
    font-family: "i";
}

.i-design-multi-exp:before {
    content: "\ed20";
}

.i-design-free-queue {
    font-family: "i";
}

.i-design-free-queue:before {
    content: "\ed1d";
}

.i-design-discount {
    font-family: "i";
}

.i-design-discount:before {
    content: "\ed1e";
}

.i-design-multi-integral {
    font-family: "i";
}

.i-design-multi-integral:before {
    content: "\ed1f";
}

.i-design-service-qualification {
    font-family: "i";
}

.i-design-service-qualification:before {
    content: "\ed3c";
}

.i-design-address {
    font-family: "i";
}

.i-design-address:before {
    content: "\ed3d";
}

.i-design-about-us {
    font-family: "i";
}

.i-design-about-us:before {
    content: "\ed3e";
}

.i-design-redeem-offer {
    font-family: "i";
}

.i-design-redeem-offer:before {
    content: "\ed44";
}

.i-design-integral-mall {
    font-family: "i";
}

.i-design-integral-mall:before {
    content: "\ed45";
}

.i-design-gift-card {
    font-family: "i";
}

.i-design-gift-card:before {
    content: "\ed3f";
}

.i-design-suggested-feedback {
    font-family: "i";
}

.i-design-suggested-feedback:before {
    content: "\ed46";
}

.i-design-customer-service {
    font-family: "i";
}

.i-design-customer-service:before {
    content: "\ed47";
}

.i-design-voucher {
    font-family: "i";
}

.i-design-voucher:before {
    content: "\ed48";
}

.i-design-promotion-gift {
    font-family: "i";
}

.i-design-promotion-gift:before {
    content: "\ed40";
}

.i-design-user-welfare {
    font-family: "i";
}

.i-design-user-welfare:before {
    content: "\ed41";
}

.i-design-member-code {
    font-family: "i";
}

.i-design-member-code:before {
    content: "\ed42";
}

.i-design-faq {
    font-family: "i";
}

.i-design-faq:before {
    content: "\ed43";
}

.i-equity-card {
    font-family: "i";
}

.i-equity-card:before {
    content: "\ed2f";
}

.i-physical-card {
    font-family: "i";
}

.i-physical-card:before {
    content: "\ed30";
}

.i-gift-card {
    font-family: "i";
}

.i-gift-card:before {
    content: "\ed31";
}

.i-design-physical-card {
    font-family: "i";
}

.i-design-physical-card:before {
    content: "\ed49";
}

.i-design-equity-card {
    font-family: "i";
}

.i-design-equity-card:before {
    content: "\ed4a";
}

.i-design-stored-value-card {
    font-family: "i";
}

.i-design-stored-value-card:before {
    content: "\ed4b";
}

.i-stored-value-Card {
    font-family: "i";
}

.i-stored-value-Card:before {
    content: "\ed2e";
}

.i-qr-code2 {
    font-family: "i";
}

.i-qr-code2:before {
    content: "\ed2c";
}

.i-huiyuanma {
    font-family: "i";
}

.i-huiyuanma:before {
    content: "\e72c";
}

.i-integral2 {
    font-family: "i";
}

.i-integral2:before {
    content: "\ed35";
}

.i-balance {
    font-family: "i";
}

.i-balance:before {
    content: "\ed32";
}

.i-equity {
    font-family: "i";
}

.i-equity:before {
    content: "\ed2d";
}

.i-coupon3 {
    font-family: "i";
}

.i-coupon3:before {
    content: "\ed33";
}

.i-left-half {
    font-family: "i";
}

.i-left-half:before {
    content: "\ed2a";
}

.i-radio {
    font-family: "i";
}

.i-radio:before {
    content: "\ed25";
}

.i-checkbox {
    font-family: "i";
}

.i-checkbox:before {
    content: "\ed26";
}

.i-right-half {
    font-family: "i";
}

.i-right-half:before {
    content: "\ed29";
}

.i-success {
    font-family: "i";
}

.i-success:before {
    content: "\ed27";
}

.i-fail {
    font-family: "i";
}

.i-fail:before {
    content: "\ed28";
}

.i-kaiguan-kai {
    font-family: "i";
}

.i-kaiguan-kai:before {
    content: "\e689";
}

.i-kaiguan-guan {
    font-family: "i";
}

.i-kaiguan-guan:before {
    content: "\e698";
}

.i-close2 {
    font-family: "i";
}

.i-close2:before {
    content: "\ed24";
}

.i-gift {
    font-family: "i";
}

.i-gift:before {
    content: "\ed21";
}

.i-avatar-choose {
    font-family: "i";
}

.i-avatar-choose:before {
    content: "\ed22";
}

.i-guanli {
    font-family: "i";
}

.i-guanli:before {
    content: "\e710";
}

.i-jilu {
    font-family: "i";
}

.i-jilu:before {
    content: "\e711";
}

.i-integral {
    font-family: "i";
}

.i-integral:before {
    content: "\ed23";
}

.i-paygift-jifen {
    font-family: "i";
}

.i-paygift-jifen:before {
    content: "\e70e";
}

.i-coupon2 {
    font-family: "i";
}

.i-coupon2:before {
    content: "\ed34";
}

.i-a-bianzu30 {
    font-family: "i";
}

.i-a-bianzu30:before {
    content: "\e6f6";
}

.i-lipinqia1 {
    font-family: "i";
}

.i-lipinqia1:before {
    content: "\e6f7";
}

.i-a-bianzu31 {
    font-family: "i";
}

.i-a-bianzu31:before {
    content: "\e6f8";
}

.i-shangpin-2-2 {
    font-family: "i";
}

.i-shangpin-2-2:before {
    content: "\e6f9";
}

.i-paygift-sanfangquanyi {
    font-family: "i";
}

.i-paygift-sanfangquanyi:before {
    content: "\e6fa";
}

.i-beitieicon {
    font-family: "i";
}

.i-beitieicon:before {
    content: "\e70a";
}

.i-beitiebgsvg {
    font-family: "i";
}

.i-beitiebgsvg:before {
    content: "\e70b";
}

.i-ziqu1 {
    font-family: "i";
}

.i-ziqu1:before {
    content: "\e708";
}

.i-waimai1 {
    font-family: "i";
}

.i-waimai1:before {
    content: "\e709";
}

.i-nltop {
    font-family: "i";
}

.i-nltop:before {
    content: "\e702";
}

.i-energy {
    font-family: "i";
}

.i-energy:before {
    content: "\e67e";
}

.i-dengwei {
    font-family: "i";
}

.i-dengwei:before {
    content: "\e67a";
}

.i-huaxian {
    font-family: "i";
}

.i-huaxian:before {
    content: "\e6ff";
}

.i-shang {
    font-family: "i";
}

.i-shang:before {
    content: "\e6f4";
}

.i-xia {
    font-family: "i";
}

.i-xia:before {
    content: "\e6f5";
}

.i-zan {
    font-family: "i";
}

.i-zan:before {
    content: "\e6ed";
}

.i-fenxiang1 {
    font-family: "i";
}

.i-fenxiang1:before {
    content: "\e6f3";
}

.i-Vector {
    font-family: "i";
}

.i-Vector:before {
    content: "\e663";
}

.i-chengshijiantou {
    font-family: "i";
}

.i-chengshijiantou:before {
    content: "\e6e7";
}

.i-pd-diand {
    font-family: "i";
}

.i-pd-diand:before {
    content: "\e918";
}

.i-pd-tijiao {
    font-family: "i";
}

.i-pd-tijiao:before {
    content: "\e919";
}

.i-pd-wanc {
    font-family: "i";
}

.i-pd-wanc:before {
    content: "\e91a";
}

.i-youhuiquan {
    font-family: "i";
}

.i-youhuiquan:before {
    content: "\e6ec";
}

.i-renshu1 {
    font-family: "i";
}

.i-renshu1:before {
    content: "\e660";
}

.i-bg-zc {
    font-family: "i";
}

.i-bg-zc:before {
    content: "\e6ea";
}

.i-dengdai {
    font-family: "i";
}

.i-dengdai:before {
    content: "\e656";
}

.i-jiaohao {
    font-family: "i";
}

.i-jiaohao:before {
    content: "\e659";
}

.i-a-bianzu2 {
    font-family: "i";
}

.i-a-bianzu2:before {
    content: "\e653";
}

.i-wifi {
    font-family: "i";
}

.i-wifi:before {
    content: "\e9a6";
}

.i-yuyue {
    font-family: "i";
}

.i-yuyue:before {
    content: "\e6e8";
}

.i-yuyue2 {
    font-family: "i";
}

.i-yuyue2:before {
    content: "\e6e9";
}

.i-jiucanfs {
    font-family: "i";
}

.i-jiucanfs:before {
    content: "\e6e5";
}

.i-shijian2 {
    font-family: "i";
}

.i-shijian2:before {
    content: "\e6e6";
}

.i-kefu {
    font-family: "i";
}

.i-kefu:before {
    content: "\e6e3";
}

.i-dingdan1 {
    font-family: "i";
}

.i-dingdan1:before {
    content: "\e6e4";
}

.i-bangding {
    font-family: "i";
}

.i-bangding:before {
    content: "\e6e2";
}

.i-dangan2 {
    font-family: "i";
}

.i-dangan2:before {
    content: "\e6e1";
}

.i-shijian1 {
    font-family: "i";
}

.i-shijian1:before {
    content: "\e6de";
}

.i-dizhi_huaban1 {
    font-family: "i";
}

.i-dizhi_huaban1:before {
    content: "\e6e0";
}

.i-fwyxd {
    font-family: "i";
}

.i-fwyxd:before {
    content: "\e6dc";
}

.i-circle1 {
    font-family: "i";
}

.i-circle1:before {
    content: "\eb95";
}

.i-phone-outgoing {
    font-family: "i";
}

.i-phone-outgoing:before {
    content: "\e6d0";
}

.i-rili1 {
    font-family: "i";
}

.i-rili1:before {
    content: "\e6cf";
}

.i-coupon1 {
    font-family: "i";
}

.i-coupon1:before {
    content: "\e6cc";
}

.i-daohang3 {
    font-family: "i";
}

.i-daohang3:before {
    content: "\e6cb";
}

.i-qiehuan1 {
    font-family: "i";
}

.i-qiehuan1:before {
    content: "\e6ca";
}

.i-duoxuan {
    font-family: "i";
}

.i-duoxuan:before {
    content: "\e6c7";
}

.i-danxuan {
    font-family: "i";
}

.i-danxuan:before {
    content: "\e6c8";
}

.i-liuhai {
    font-family: "i";
}

.i-liuhai:before {
    content: "\e6bf";
}

.i-shanchu {
    font-family: "i";
}

.i-shanchu:before {
    content: "\e65b";
}

.i-bianji {
    font-family: "i";
}

.i-bianji:before {
    content: "\e6be";
}

.i-xingxing-2 {
    font-family: "i";
}

.i-xingxing-2:before {
    content: "\e6b9";
}

.i-bonus-line {
    font-family: "i";
}

.i-bonus-line:before {
    content: "\e6ba";
}

.i-jingyan {
    font-family: "i";
}

.i-jingyan:before {
    content: "\e6bb";
}

.i-shuaxin {
    font-family: "i";
}

.i-shuaxin:before {
    content: "\e60f";
}

.i-qr-code {
    font-family: "i";
}

.i-qr-code:before {
    content: "\e6b8";
}

.i-jindu-5 {
    font-family: "i";
}

.i-jindu-5:before {
    content: "\e6b1";
}

.i-jindu-1 {
    font-family: "i";
}

.i-jindu-1:before {
    content: "\e6b2";
}

.i-jindu-2 {
    font-family: "i";
}

.i-jindu-2:before {
    content: "\e6b3";
}

.i-jindu-3 {
    font-family: "i";
}

.i-jindu-3:before {
    content: "\e6b4";
}

.i-jindu-4 {
    font-family: "i";
}

.i-jindu-4:before {
    content: "\e6b7";
}

.i-jindu {
    font-family: "i";
}

.i-jindu:before {
    content: "\e6ac";
}

.i-jfbg2 {
    font-family: "i";
}

.i-jfbg2:before {
    content: "\e6a4";
}

.i-peisong {
    font-family: "i";
}

.i-peisong:before {
    content: "\e691";
}

.i-dingdan {
    font-family: "i";
}

.i-dingdan:before {
    content: "\e692";
}

.i-zhizuo {
    font-family: "i";
}

.i-zhizuo:before {
    content: "\e694";
}

.i-wancheng {
    font-family: "i";
}

.i-wancheng:before {
    content: "\e695";
}

.i-buzengsong {
    font-family: "i";
}

.i-buzengsong:before {
    content: "\e69a";
}

.i-zengsong {
    font-family: "i";
}

.i-zengsong:before {
    content: "\e69c";
}

.i-yuandian {
    font-family: "i";
}

.i-yuandian:before {
    content: "\e609";
}

.i-notice {
    font-family: "i";
}

.i-notice:before {
    content: "\e639";
}

.i-collect-point-bg {
    font-family: "i";
}

.i-collect-point-bg:before {
    content: "\e693";
}

.i-solid-arrow-down {
    font-family: "i";
}

.i-solid-arrow-down:before {
    content: "\ffed";
}

.i-shangjiantou {
    font-family: "i";
}

.i-shangjiantou:before {
    content: "\eb6d";
}

.i-xiajiantou {
    font-family: "i";
}

.i-xiajiantou:before {
    content: "\e690";
}

.i-a-jifen1 {
    font-family: "i";
}

.i-a-jifen1:before {
    content: "\e68e";
}

.i-jidian {
    font-family: "i";
}

.i-jidian:before {
    content: "\e68d";
}

.i-jiahao2 {
    font-family: "i";
}

.i-jiahao2:before {
    content: "\e68a";
}

.i-jianhao1 {
    font-family: "i";
}

.i-jianhao1:before {
    content: "\e68c";
}

.i-checked-plain {
    font-family: "i";
}

.i-checked-plain:before {
    content: "\ed2b";
}

.i-xuanze1 {
    font-family: "i";
}

.i-xuanze1:before {
    content: "\e742";
}

.i-scan {
    font-family: "i";
}

.i-scan:before {
    content: "\e749";
}

.i-fenxiang {
    font-family: "i";
}

.i-fenxiang:before {
    content: "\e686";
}

.i-fuwuzizhi {
    font-family: "i";
}

.i-fuwuzizhi:before {
    content: "\e696";
}

.i-pintuantuangouchenggong {
    font-family: "i";
}

.i-pintuantuangouchenggong:before {
    content: "\e637";
}

.i-xihuan {
    font-family: "i";
}

.i-xihuan:before {
    content: "\e672";
}

.i-exchange {
    font-family: "i";
}

.i-exchange:before {
    content: "\ffef";
}

.i-duihuan {
    font-family: "i";
}

.i-duihuan:before {
    content: "\e71d";
}

.i-jiantou_xiangzuo_o {
    font-family: "i";
}

.i-jiantou_xiangzuo_o:before {
    content: "\eb92";
}

.i-jiantou_xiangyou_o {
    font-family: "i";
}

.i-jiantou_xiangyou_o:before {
    content: "\eb94";
}

.i-jia {
    font-family: "i";
}

.i-jia:before {
    content: "\e661";
}

.i-circle {
    font-family: "i";
}

.i-circle:before {
    content: "\e69b";
}

.i-a-touxiudengjizuizhong_huaban1fuben5_gai155 {
    font-family: "i";
}

.i-a-touxiudengjizuizhong_huaban1fuben5_gai155:before {
    content: "\e610";
}

.i-a-touxiudengjizuizhong_huaban1fuben6gai155 {
    font-family: "i";
}

.i-a-touxiudengjizuizhong_huaban1fuben6gai155:before {
    content: "\e613";
}

.i-a-touxiudengjizuizhong_huaban1fuben2_gai155 {
    font-family: "i";
}

.i-a-touxiudengjizuizhong_huaban1fuben2_gai155:before {
    content: "\e615";
}

.i-a-touxiudengjizuizhong_huaban1fuben3gai155 {
    font-family: "i";
}

.i-a-touxiudengjizuizhong_huaban1fuben3gai155:before {
    content: "\e621";
}

.i-a-touxiudengjizuizhong_huaban1fuben7gai155 {
    font-family: "i";
}

.i-a-touxiudengjizuizhong_huaban1fuben7gai155:before {
    content: "\e623";
}

.i-a-touxiudengjizuizhong_huaban1fuben4_gai155 {
    font-family: "i";
}

.i-a-touxiudengjizuizhong_huaban1fuben4_gai155:before {
    content: "\e625";
}

.i-a-touxiudengjizuizhong_huaban1fuben8gai155 {
    font-family: "i";
}

.i-a-touxiudengjizuizhong_huaban1fuben8gai155:before {
    content: "\e626";
}

.i-a-touxiudengjizuizhong_huaban1gai155 {
    font-family: "i";
}

.i-a-touxiudengjizuizhong_huaban1gai155:before {
    content: "\e627";
}

.i-a-touxiudengjizuizhong_huaban1fubengai155 {
    font-family: "i";
}

.i-a-touxiudengjizuizhong_huaban1fubengai155:before {
    content: "\e628";
}

.i-a-touxiudengjizuizhong_huaban1fuben9gai155 {
    font-family: "i";
}

.i-a-touxiudengjizuizhong_huaban1fuben9gai155:before {
    content: "\e62a";
}

.i-saoma {
    font-family: "i";
}

.i-saoma:before {
    content: "\e619";
}

.i-jianyifankui {
    font-family: "i";
}

.i-jianyifankui:before {
    content: "\e655";
}

.i-xuanze-copy {
    font-family: "i";
}

.i-xuanze-copy:before {
    content: "\ed18";
}

.i-chahao {
    font-family: "i";
}

.i-chahao:before {
    content: "\e68b";
}

.i-jiantou1 {
    font-family: "i";
}

.i-jiantou1:before {
    content: "\e650";
}

.i-anquan-mianxing {
    font-family: "i";
}

.i-anquan-mianxing:before {
    content: "\e635";
}

.i-question {
    font-family: "i";
}

.i-question:before {
    content: "\ffee";
}

.i-yiwen {
    font-family: "i";
}

.i-yiwen:before {
    content: "\e666";
}

.i-naichatianpin {
    font-family: "i";
}

.i-naichatianpin:before {
    content: "\e683";
}

.i-zhanghuyue {
    font-family: "i";
}

.i-zhanghuyue:before {
    content: "\e77a";
}

.i-piaofang {
    font-family: "i";
}

.i-piaofang:before {
    content: "\e608";
}

.i-putonghuiyuan {
    font-family: "i";
}

.i-putonghuiyuan:before {
    content: "\e697";
}

.i-lipinqia {
    font-family: "i";
}

.i-lipinqia:before {
    content: "\e620";
}

.i-jifen {
    font-family: "i";
}

.i-jifen:before {
    content: "\e61a";
}

.i-qiehuan {
    font-family: "i";
}

.i-qiehuan:before {
    content: "\ed17";
}

.i-zhengque-correct {
    font-family: "i";
}

.i-zhengque-correct:before {
    content: "\e651";
}

.i-gonggao {
    font-family: "i";
}

.i-gonggao:before {
    content: "\e646";
}

.i-xihuanlike {
    font-family: "i";
}

.i-xihuanlike:before {
    content: "\e622";
}

.i-close {
    font-family: "i";
}

.i-close:before {
    content: "\e606";
}

.i-add1 {
    font-family: "i";
}

.i-add1:before {
    content: "\e604";
}

.i-tuikuan {
    font-family: "i";
}

.i-tuikuan:before {
    content: "\e63f";
}

.i-xiaofei {
    font-family: "i";
}

.i-xiaofei:before {
    content: "\e640";
}

.i-chongzhi {
    font-family: "i";
}

.i-chongzhi:before {
    content: "\e641";
}

.i-qita {
    font-family: "i";
}

.i-qita:before {
    content: "\e642";
}

.i-dianzan-m1 {
    font-family: "i";
}

.i-dianzan-m1:before {
    content: "\e63a";
}

.i-Giftliwu2 {
    font-family: "i";
}

.i-Giftliwu2:before {
    content: "\e72b";
}

.i-dianzan {
    font-family: "i";
}

.i-dianzan:before {
    content: "\e60c";
}

.i-md-information-circle-outline {
    font-family: "i";
}

.i-md-information-circle-outline:before {
    content: "\e69d";
}

.i-qian3 {
    font-family: "i";
}

.i-qian3:before {
    content: "\e682";
}

.i-lipinka {
    font-family: "i";
}

.i-lipinka:before {
    content: "\e633";
}

.i-icon_erweima {
    font-family: "i";
}

.i-icon_erweima:before {
    content: "\e634";
}

.i-dizhi {
    font-family: "i";
}

.i-dizhi:before {
    content: "\e617";
}

.i-10 {
    font-family: "i";
}

.i-10:before {
    content: "\e624";
}

.i-liwu {
    font-family: "i";
}

.i-liwu:before {
    content: "\e607";
}

.i-tuiguang {
    font-family: "i";
}

.i-tuiguang:before {
    content: "\e632";
}

.i-jianhao {
    font-family: "i";
}

.i-jianhao:before {
    content: "\e62f";
}

.i-jiahao {
    font-family: "i";
}

.i-jiahao:before {
    content: "\e630";
}

.i-duihao {
    font-family: "i";
}

.i-duihao:before {
    content: "\e61f";
}

.i-daohang2 {
    font-family: "i";
}

.i-daohang2:before {
    content: "\e62e";
}

.i-add-circle {
    font-family: "i";
}

.i-add-circle:before {
    content: "\e664";
}

.i-minus-circle1 {
    font-family: "i";
}

.i-minus-circle1:before {
    content: "\e677";
}

.i-dianhua1 {
    font-family: "i";
}

.i-dianhua1:before {
    content: "\e61d";
}

.i-shangpinguanli {
    font-family: "i";
}

.i-shangpinguanli:before {
    content: "\e61e";
}

.i-shijian {
    font-family: "i";
}

.i-shijian:before {
    content: "\e618";
}

.i-dizhi_huaban {
    font-family: "i";
}

.i-dizhi_huaban:before {
    content: "\e629";
}

.i-dianhua {
    font-family: "i";
}

.i-dianhua:before {
    content: "\e66e";
}

.i-daohang {
    font-family: "i";
}

.i-daohang:before {
    content: "\e6bd";
}

.i-shoucang1 {
    font-family: "i";
}

.i-shoucang1:before {
    content: "\e605";
}

.i-jinggao {
    font-family: "i";
}

.i-jinggao:before {
    content: "\e602";
}

.i-star {
    font-family: "i";
}

.i-star:before {
    content: "\e601";
}

.i-cainixihuan1 {
    font-family: "i";
}

.i-cainixihuan1:before {
    content: "\e612";
}

.i-rexiao {
    font-family: "i";
}

.i-rexiao:before {
    content: "\e64a";
}

.i-huodong {
    font-family: "i";
}

.i-huodong:before {
    content: "\e76f";
}

.i-coupon {
    font-family: "i";
}

.i-coupon:before {
    content: "\e616";
}

.i-arrow-r {
    font-family: "i";
}

.i-arrow-r:before {
    content: "\ed1a";
}

.i-jiantou-shang {
    font-family: "i";
}

.i-jiantou-shang:before {
    content: "\e719";
}

.i-jiantou-you {
    font-family: "i";
}

.i-jiantou-you:before {
    content: "\e71a";
}

.i-jiantou-zuo {
    font-family: "i";
}

.i-jiantou-zuo:before {
    content: "\e71b";
}

.i-jiantou-xia {
    font-family: "i";
}

.i-jiantou-xia:before {
    content: "\e71c";
}

.i-icon_duihao-xian {
    font-family: "i";
}

.i-icon_duihao-xian:before {
    content: "\e63c";
}

.i-add {
    font-family: "i";
}

.i-add:before {
    content: "\e61c";
}

.i-dengdaizhongbeifen {
    font-family: "i";
}

.i-dengdaizhongbeifen:before {
    content: "\e62b";
}

.i-chenggong1 {
    font-family: "i";
}

.i-chenggong1:before {
    content: "\e62c";
}

.i-xuanze_xuanzhong {
    font-family: "i";
}

.i-xuanze_xuanzhong:before {
    content: "\e60d";
}

.i-jiahao1 {
    font-family: "i";
}

.i-jiahao1:before {
    content: "\e727";
}

.i-minus-circle {
    font-family: "i";
}

.i-minus-circle:before {
    content: "\e780";
}

.i-plus-circle-fill {
    font-family: "i";
}

.i-plus-circle-fill:before {
    content: "\e845";
}

.extend-click-20 {
    position: relative;
}

.extend-click-20::after {
    content: '';
    position: absolute;
    left: -20rpx;
    right: -20rpx;
    top: -20rpx;
    bottom: -20rpx;
}

.extend-click {
    position: relative;
}

.extend-click::after {
    content: '';
    position: absolute;
    left: -10rpx;
    right: -10rpx;
    top: -10rpx;
    bottom: -10rpx;
}

.hairline-t {
    position: relative;
}

.hairline-t::after {
    position: absolute;
    box-sizing: border-box;
    content: '';
    pointer-events: none;
    top: -50%;
    right: -50%;
    bottom: -50%;
    left: -50%;
    border: 0 solid #eeeeee;
    transform: scale(0.5);
    border-top-width: 1px;
}

.hairline-b {
    position: relative;
}

.hairline-b::after {
    position: absolute;
    box-sizing: border-box;
    content: '';
    pointer-events: none;
    top: -50%;
    right: -50%;
    bottom: -50%;
    left: -50%;
    border: 0 solid #eeeeee;
    transform: scale(0.5);
    border-bottom-width: 1px;
}

.hairline-l {
    position: relative;
}

.hairline-l::after {
    position: absolute;
    box-sizing: border-box;
    content: '';
    pointer-events: none;
    top: -50%;
    right: -50%;
    bottom: -50%;
    left: -50%;
    border: 0 solid #eeeeee;
    transform: scale(0.5);
    border-left-width: 1px;
}

.hairline-r {
    position: relative;
}

.hairline-r::after {
    position: absolute;
    box-sizing: border-box;
    content: '';
    pointer-events: none;
    top: -50%;
    right: -50%;
    bottom: -50%;
    left: -50%;
    border: 0 solid #eeeeee;
    transform: scale(0.5);
    border-right-width: 1px;
}

.button {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    height: 88rpx;
    padding: 0 30rpx;
    margin-left: 0;
    margin-right: 0;
    color: #323233;
    font-size: 32rpx;
    line-height: 40rpx;
    text-align: center;
    vertical-align: middle;
    background-color: #fff;
    border: 1px solid #ebedf0;
    border-radius: 12rpx;
    transition: opacity 0.2s;
    -webkit-appearance: none;
    appearance: none;
    -webkit-text-size-adjust: 100%
}

.button::before {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 2;
    width: 100%;
    height: 100%;
    background-color: #000;
    border: inherit;
    border-color: #000;
    border-radius: inherit;
    transform: translate(-50%,-50%);
    opacity: 0;
    content: ' '
}

.button::after {
    z-index: 2;
    border-width: 0
}

.button-hover::before {
    opacity: 0.15
}

.button-primary {
    color: #fff;
    background-color: var(--std-primary-color);
    border: 2rpx solid var(--std-primary-color)
}

.button.button-primary[disabled] {
    color: #fff;
    background-color: var(--std-primary-color);
    border: 2rpx solid var(--std-primary-color)
}

.button-primary[disabled] {
    opacity: 0.5
}

.button-primary[disabled]::before {
    opacity: 0
}

.button-primary-plain {
    color: var(--std-primary-color);
    background-color: #fff;
    border: 2rpx solid var(--std-primary-color)
}

.button-primary-plain::before {
    background-color: var(--std-primary-color-opacity-40);
    border-color: var(--std-primary-color-opacity-40)
}

.button-disabled {
    opacity: 0.5
}

.button-disabled::before {
    opacity: 0
}

._bl_background-size_c_100_p__auto_br_,.bg-_bl_length_c_100_p__auto_br_ {
    background-size: 100% auto;
}

.pointer-events-auto {
    pointer-events: auto;
}

.pointer-events-none {
    pointer-events: none;
}

.after-pointer-events-none::after {
    pointer-events: none;
}

.visible {
    visibility: visible;
}

.invisible {
    visibility: hidden;
}

.backface-hidden {
    backface-visibility: hidden;
}

._i_absolute,.absolute_i_ {
    position: absolute !important;
}

.absolute {
    position: absolute;
}

.fixed {
    position: fixed;
}

.relative {
    position: relative;
}

.sticky {
    position: sticky;
}

.before-absolute::before {
    position: absolute;
}

.after-absolute::after {
    position: absolute;
}

.not-last-after-absolute:not(:last-child)::after {
    position: absolute;
}

.static {
    position: static;
}

.after-static::after {
    position: static;
}

.inset-x-0 {
    left: 0;
    right: 0;
}

.-bottom-12,.bottom--12,.bottom--12rpx {
    bottom: -12rpx;
}

.-bottom-16 {
    bottom: -16rpx;
}

.-bottom-18 {
    bottom: -18rpx;
}

.-bottom-20,.-bottom-20rpx,.bottom--20rpx {
    bottom: -20rpx;
}

.-bottom-24,.bottom--24 {
    bottom: -24rpx;
}

.-bottom-3 {
    bottom: -3rpx;
}

.-bottom-8 {
    bottom: -8rpx;
}

.-left-10,.left--10rpx {
    left: -10rpx;
}

.-left-32 {
    left: -32rpx;
}

.-left-4,.left--4 {
    left: -4rpx;
}

.-right-10,.right--10,.right--10rpx,.right-_bl_-10rpx_br_ {
    right: -10rpx;
}

.-right-10_p_ {
    right: -10%;
}

.-right-11rpx,.right--11rpx {
    right: -11rpx;
}

.-right-12,.right--12rpx {
    right: -12rpx;
}

.-right-13 {
    right: -13rpx;
}

.-right-18,.right--18,.right-_bl_-18rpx_br_ {
    right: -18rpx;
}

.-right-20,.right--20rpx,.right-_bl_-20rpx_br_ {
    right: -20rpx;
}

.-right-21 {
    right: -21rpx;
}

.-right-25 {
    right: -25rpx;
}

.-right-32 {
    right: -32rpx;
}

.-top-1,.top--1rpx {
    top: -1rpx;
}

.-top-10,.top--10,.top--10rpx,.top-_bl_-10rpx_br_ {
    top: -10rpx;
}

.-top-12,.top--12,.top--12rpx {
    top: -12rpx;
}

.-top-14,.top--14rpx {
    top: -14rpx;
}

.-top-15,.top--15rpx {
    top: -15rpx;
}

.-top-18,.top--18,.top--18rpx {
    top: -18rpx;
}

.-top-1px,.top--1px {
    top: -1px;
}

.-top-24,.top--24 {
    top: -24rpx;
}

.-top-28 {
    top: -28rpx;
}

.-top-29,.top--29rpx {
    top: -29rpx;
}

.-top-30,.top--30,.top--30rpx {
    top: -30rpx;
}

.-top-40,.top-_bl_-40rpx_br_ {
    top: -40rpx;
}

.-top-42 {
    top: -42rpx;
}

.-top-476 {
    top: -476rpx;
}

.-top-50 {
    top: -50rpx;
}

.-top-57 {
    top: -57rpx;
}

.-top-59 {
    top: -59rpx;
}

.-top-6,.top--6rpx {
    top: -6rpx;
}

.-top-full,.top-_bl_-100_p__br_ {
    top: -100%;
}

.bottom--0_d_5 {
    bottom: -0.5rpx;
}

.bottom--1000rpx {
    bottom: -1000rpx;
}

.bottom--100rpx {
    bottom: -100rpx;
}

.bottom--105rpx {
    bottom: -105rpx;
}

.bottom--108rpx {
    bottom: -108rpx;
}

.bottom--10px {
    bottom: -10px;
}

.bottom--10rpx {
    bottom: -10rpx;
}

.bottom--110rpx {
    bottom: -110rpx;
}

.bottom--114rpx {
    bottom: -114rpx;
}

.bottom--120rpx {
    bottom: -120rpx;
}

.bottom--130rpx,.bottom-_bl_-130rpx_br_ {
    bottom: -130rpx;
}

.bottom--13rpx {
    bottom: -13rpx;
}

.bottom--14rpx {
    bottom: -14rpx;
}

.bottom--1rpx {
    bottom: -1rpx;
}

.bottom--30rpx {
    bottom: -30rpx;
}

.bottom--32rpx {
    bottom: -32rpx;
}

.bottom--38rpx {
    bottom: -38rpx;
}

.bottom--44rpx {
    bottom: -44rpx;
}

.bottom--450rpx {
    bottom: -450rpx;
}

.bottom--4rpx {
    bottom: -4rpx;
}

.bottom--6,.bottom--6rpx {
    bottom: -6rpx;
}

.bottom--80,.bottom--80rpx {
    bottom: -80rpx;
}

.bottom-_bl_-50_p__br_ {
    bottom: -50%;
}

.bottom-_bl_13_p__br_ {
    bottom: 13%;
}

.bottom-_bl_calc_pl_120rpx__a__constant_pl_safe-area-inset-bottom_pr__pr__br_,.bottom-_bl_calc_pl_120rpx_a_constant_pl_safe-area-inset-bottom_pr__pr__br_ {
    bottom: calc(120rpx + constant(safe-area-inset-bottom));
}

.bottom-_bl_calc_pl_120rpx__a__env_pl_safe-area-inset-bottom_pr__pr__br_,.bottom-_bl_calc_pl_120rpx_a_env_pl_safe-area-inset-bottom_pr__pr__br_ {
    bottom: calc(120rpx + env(safe-area-inset-bottom));
}

.bottom-_bl_calc_pl_env_pl_safe-area-inset-bottom_pr__a_120rpx_pr__br_ {
    bottom: calc(env(safe-area-inset-bottom) + 120rpx);
}

.bottom-_bl_calc_pl_env_pl_safe-area-inset-bottom_pr__a_132rpx_pr__br_ {
    bottom: calc(env(safe-area-inset-bottom) + 132rpx);
}

.bottom-_bl_calc_pl_env_pl_safe-area-inset-bottom_pr__a_140rpx_pr__br_ {
    bottom: calc(env(safe-area-inset-bottom) + 140rpx);
}

.bottom-_bl_constant_pl_safe-area-inset-bottom_pr__br_ {
    bottom: constant(safe-area-inset-bottom);
}

.bottom-_bl_env_pl_safe-area-inset-bottom_pr__br_ {
    bottom: env(safe-area-inset-bottom);
}

.bottom-0,.bottom-0rpx {
    bottom: 0;
}

.bottom-10,.bottom-10rpx {
    bottom: 10rpx;
}

.bottom-100,.bottom-100rpx {
    bottom: 100rpx;
}

.bottom-108 {
    bottom: 108rpx;
}

.bottom-10rpx_i_ {
    bottom: 10rpx !important;
}

.bottom-115rpx {
    bottom: 115rpx;
}

.bottom-12 {
    bottom: 12rpx;
}

.bottom-120rpx {
    bottom: 120rpx;
}

.bottom-122rpx {
    bottom: 122rpx;
}

.bottom-140rpx {
    bottom: 140rpx;
}

.bottom-14rpx {
    bottom: 14rpx;
}

.bottom-16,.bottom-16rpx {
    bottom: 16rpx;
}

.bottom-166rpx {
    bottom: 166rpx;
}

.bottom-176rpx {
    bottom: 176rpx;
}

.bottom-18rpx {
    bottom: 18rpx;
}

.bottom-20 {
    bottom: 20rpx;
}

.bottom-210rpx {
    bottom: 210rpx;
}

.bottom-24,.bottom-24rpx {
    bottom: 24rpx;
}

.bottom-26rpx {
    bottom: 26rpx;
}

.bottom-274 {
    bottom: 274rpx;
}

.bottom-2rpx {
    bottom: 2rpx;
}

.bottom-30 {
    bottom: 30rpx;
}

.bottom-300 {
    bottom: 300rpx;
}

.bottom-32,.bottom-32rpx {
    bottom: 32rpx;
}

.bottom-34,.bottom-34rpx {
    bottom: 34rpx;
}

.bottom-36 {
    bottom: 36rpx;
}

.bottom-4,.bottom-4rpx {
    bottom: 4rpx;
}

.bottom-40,.bottom-40rpx {
    bottom: 40rpx;
}

.bottom-500 {
    bottom: 500rpx;
}

.bottom-50rpx {
    bottom: 50rpx;
}

.bottom-51rpx {
    bottom: 51rpx;
}

.bottom-52 {
    bottom: 52rpx;
}

.bottom-550rpx {
    bottom: 550rpx;
}

.bottom-60 {
    bottom: 60rpx;
}

.bottom-64 {
    bottom: 64rpx;
}

.bottom-66rpx {
    bottom: 66rpx;
}

.bottom-6rpx {
    bottom: 6rpx;
}

.bottom-78rpx {
    bottom: 78rpx;
}

.bottom-8,.bottom-8rpx {
    bottom: 8rpx;
}

.bottom-80 {
    bottom: 80rpx;
}

.bottom-auto {
    bottom: auto;
}

.left--100rpx {
    left: -100rpx;
}

.left--10px {
    left: -10px;
}

.left--15 {
    left: -15rpx;
}

.left--16 {
    left: -16rpx;
}

.left--20,.left--20rpx {
    left: -20rpx;
}

.left--24,.left--24rpx {
    left: -24rpx;
}

.left--26 {
    left: -26rpx;
}

.left--2rpx {
    left: -2rpx;
}

.left--33rpx {
    left: -33rpx;
}

.left--40,.left--40rpx {
    left: -40rpx;
}

.left--420 {
    left: -420rpx;
}

.left--48 {
    left: -48rpx;
}

.left--55 {
    left: -55rpx;
}

.left--5rpx,.left-_bl_-5rpx_br_ {
    left: -5rpx;
}

.left--6rpx {
    left: -6rpx;
}

.left--8rpx {
    left: -8rpx;
}

.left-_bl_-50_p__br_ {
    left: -50%;
}

.left-_bl_10_p__br_ {
    left: 10%;
}

.left-_bl_12_p__br_ {
    left: 12%;
}

.left-_bl_13_p__br_ {
    left: 13%;
}

.left-_bl_20_p__br_ {
    left: 20%;
}

.left-_bl_30_p__br_ {
    left: 30%;
}

.left-_bl_50_p__br_,.left-1_s_2,.left-2_s_4,.left-50_p_ {
    left: 50%;
}

.left-_bl_calc_pl_100_p_-28rpx_pr__br_ {
    left: calc(100% - 28rpx);
}

.left-_bl_calc_pl_var_pl_--lr-padding_pr__u_-1_pr__br_ {
    left: calc(var(--lr-padding) * -1);
}

.left-0,.left-0rpx {
    left: 0;
}

.left-10,.left-10rpx {
    left: 10rpx;
}

.left-1000000px {
    left: 1000000px;
}

.left-10000px {
    left: 10000px;
}

.left-12,.left-12rpx {
    left: 12rpx;
}

.left-136 {
    left: 136rpx;
}

.left-153rpx {
    left: 153rpx;
}

.left-16rpx {
    left: 16rpx;
}

.left-170rpx {
    left: 170rpx;
}

.left-176,.left-176rpx {
    left: 176rpx;
}

.left-18rpx {
    left: 18rpx;
}

.left-20 {
    left: 20rpx;
}

.left-24,.left-24rpx {
    left: 24rpx;
}

.left-25,.left-25rpx {
    left: 25rpx;
}

.left-26px {
    left: 26px;
}

.left-3 {
    left: 3rpx;
}

.left-30,.left-30rpx {
    left: 30rpx;
}

.left-32,.left-32rpx {
    left: 32rpx;
}

.left-34rpx {
    left: 34rpx;
}

.left-36 {
    left: 36rpx;
}

.left-40 {
    left: 40rpx;
}

.left-47 {
    left: 47rpx;
}

.left-50,.left-50rpx {
    left: 50rpx;
}

.left-52rpx {
    left: 52rpx;
}

.left-60rpx {
    left: 60rpx;
}

.left-72 {
    left: 72rpx;
}

.left-78 {
    left: 78rpx;
}

.left-7rpx {
    left: 7rpx;
}

.left-8,.left-8rpx {
    left: 8rpx;
}

.left-85_p_ {
    left: 85%;
}

.left-90 {
    left: 90rpx;
}

.left-93rpx {
    left: 93rpx;
}

.left-auto {
    left: auto;
}

.right--100rpx {
    right: -100rpx;
}

.right--10px {
    right: -10px;
}

.right--15 {
    right: -15rpx;
}

.right--16,.right--16rpx {
    right: -16rpx;
}

.right--1rpx {
    right: -1rpx;
}

.right--22rpx {
    right: -22rpx;
}

.right--26,.right--26rpx {
    right: -26rpx;
}

.right--30 {
    right: -30rpx;
}

.right--33rpx {
    right: -33rpx;
}

.right--40rpx,.right-_bl_-40rpx_br_ {
    right: -40rpx;
}

.right--50rpx {
    right: -50rpx;
}

.right--5rpx {
    right: -5rpx;
}

.right--8rpx {
    right: -8rpx;
}

.right-_bl_-50_p__br_ {
    right: -50%;
}

.right-_bl_20_p__br_ {
    right: 20%;
}

.right-_bl_50_p__br_ {
    right: 50%;
}

.right-0,.right-0rpx {
    right: 0;
}

.right-1 {
    right: 1rpx;
}

.right-10,.right-10rpx {
    right: 10rpx;
}

.right-105rpx {
    right: 105rpx;
}

.right-12,.right-12rpx {
    right: 12rpx;
}

.right-14 {
    right: 14rpx;
}

.right-140 {
    right: 140rpx;
}

.right-16,.right-16rpx {
    right: 16rpx;
}

.right-180rpx {
    right: 180rpx;
}

.right-18rpx {
    right: 18rpx;
}

.right-19 {
    right: 19rpx;
}

.right-2,.right-2rpx {
    right: 2rpx;
}

.right-20,.right-20rpx {
    right: 20rpx;
}

.right-21 {
    right: 21rpx;
}

.right-22 {
    right: 22rpx;
}

.right-24,.right-24rpx {
    right: 24rpx;
}

.right-28 {
    right: 28rpx;
}

.right-30,.right-30rpx {
    right: 30rpx;
}

.right-32,.right-32rpx {
    right: 32rpx;
}

.right-34rpx {
    right: 34rpx;
}

.right-35rpx {
    right: 35rpx;
}

.right-36 {
    right: 36rpx;
}

.right-4,.right-4rpx {
    right: 4rpx;
}

.right-40,.right-40rpx {
    right: 40rpx;
}

.right-43 {
    right: 43rpx;
}

.right-44rpx {
    right: 44rpx;
}

.right-48 {
    right: 48rpx;
}

.right-50rpx {
    right: 50rpx;
}

.right-56rpx {
    right: 56rpx;
}

.right-6,.right-6rpx {
    right: 6rpx;
}

.right-60,.right-60rpx {
    right: 60rpx;
}

.right-72 {
    right: 72rpx;
}

.right-78rpx {
    right: 78rpx;
}

.right-8,.right-8rpx {
    right: 8rpx;
}

.right-80rpx {
    right: 80rpx;
}

.right-94rpx {
    right: 94rpx;
}

.right-auto {
    right: auto;
}

.top--100rpx {
    top: -100rpx;
}

.top--104 {
    top: -104rpx;
}

.top--10px {
    top: -10px;
}

.top--130rpx {
    top: -130rpx;
}

.top--13rpx {
    top: -13rpx;
}

.top--16,.top--16rpx {
    top: -16rpx;
}

.top--2,.top--2rpx {
    top: -2rpx;
}

.top--20,.top--20rpx,.top-_bl_-20rpx_br_ {
    top: -20rpx;
}

.top--22 {
    top: -22rpx;
}

.top--26 {
    top: -26rpx;
}

.top--32rpx {
    top: -32rpx;
}

.top--34,.top--34rpx,.top-_bl_-34rpx_br_ {
    top: -34rpx;
}

.top--39rpx {
    top: -39rpx;
}

.top--43rpx {
    top: -43rpx;
}

.top--46rpx {
    top: -46rpx;
}

.top--4px {
    top: -4px;
}

.top--600 {
    top: -600rpx;
}

.top--60rpx {
    top: -60rpx;
}

.top--624rpx,.top-_bl_-624rpx_br_ {
    top: -624rpx;
}

.top--68,.top--68rpx {
    top: -68rpx;
}

.top--70rpx {
    top: -70rpx;
}

.top--74rpx {
    top: -74rpx;
}

.top--80rpx {
    top: -80rpx;
}

.top--8rpx {
    top: -8rpx;
}

.top--94rpx {
    top: -94rpx;
}

.top--9rpx {
    top: -9rpx;
}

.top-_bl_-25rpx_br_ {
    top: -25rpx;
}

.top-_bl_-50_p__br_ {
    top: -50%;
}

.top-_bl_10_p__br_ {
    top: 10%;
}

.top-_bl_100_p__br_,.top-full {
    top: 100%;
}

.top-_bl_130_p__br_ {
    top: 130%;
}

.top-_bl_24_p__br_ {
    top: 24%;
}

.top-_bl_30_p__br_ {
    top: 30%;
}

.top-_bl_40_p__br_ {
    top: 40%;
}

.top-_bl_50_p__br_,.top-1_s_2,.top-50_p_ {
    top: 50%;
}

.top-_bl_calc_pl_100_p__a_20rpx_pr__br_ {
    top: calc(100% + 20rpx);
}

.top-_bl_calc_pl_100vh_-_496rpx_pr__br_ {
    top: calc(100vh - 496rpx);
}

.top-_bl_calc_pl_100vh_-_640rpx_pr__br_ {
    top: calc(100vh - 640rpx);
}

.top-_bl_calc_pl_150rpx-2rpx_pr__br_ {
    top: calc(150rpx - 2rpx);
}

.top-0,.top-0rpx {
    top: 0;
}

.top-1 {
    top: 1rpx;
}

.top-10,.top-10rpx {
    top: 10rpx;
}

.top-100 {
    top: 100rpx;
}

.top-1000000px {
    top: 1000000px;
}

.top-103 {
    top: 103rpx;
}

.top-110 {
    top: 110rpx;
}

.top-12,.top-12rpx {
    top: 12rpx;
}

.top-120 {
    top: 120rpx;
}

.top-130rpx {
    top: 130rpx;
}

.top-136rpx {
    top: 136rpx;
}

.top-14,.top-14rpx {
    top: 14rpx;
}

.top-150rpx {
    top: 150rpx;
}

.top-16,.top-16rpx {
    top: 16rpx;
}

.top-166rpx {
    top: 166rpx;
}

.top-172,.top-172rpx {
    top: 172rpx;
}

.top-178rpx {
    top: 178rpx;
}

.top-19 {
    top: 19rpx;
}

.top-190rpx {
    top: 190rpx;
}

.top-2,.top-2rpx {
    top: 2rpx;
}

.top-20,.top-20rpx {
    top: 20rpx;
}

.top-200 {
    top: 200rpx;
}

.top-203rpx {
    top: 203rpx;
}

.top-204rpx {
    top: 204rpx;
}

.top-210rpx {
    top: 210rpx;
}

.top-22,.top-22rpx {
    top: 22rpx;
}

.top-23rpx {
    top: 23rpx;
}

.top-24,.top-24rpx {
    top: 24rpx;
}

.top-25rpx {
    top: 25rpx;
}

.top-26,.top-26rpx {
    top: 26rpx;
}

.top-290 {
    top: 290rpx;
}

.top-294 {
    top: 294rpx;
}

.top-296 {
    top: 296rpx;
}

.top-3 {
    top: 3rpx;
}

.top-30,.top-30rpx {
    top: 30rpx;
}

.top-31 {
    top: 31rpx;
}

.top-32,.top-32rpx {
    top: 32rpx;
}

.top-320rpx {
    top: 320rpx;
}

.top-334 {
    top: 334rpx;
}

.top-34rpx {
    top: 34rpx;
}

.top-36,.top-36rpx {
    top: 36rpx;
}

.top-4 {
    top: 4rpx;
}

.top-40,.top-40rpx {
    top: 40rpx;
}

.top-400 {
    top: 400rpx;
}

.top-44rpx {
    top: 44rpx;
}

.top-45_p_ {
    top: 45%;
}

.top-46,.top-46rpx {
    top: 46rpx;
}

.top-48 {
    top: 48rpx;
}

.top-55_p_ {
    top: 55%;
}

.top-6,.top-6rpx {
    top: 6rpx;
}

.top-61px {
    top: 61px;
}

.top-620 {
    top: 620rpx;
}

.top-62rpx {
    top: 62rpx;
}

.top-7rpx {
    top: 7rpx;
}

.top-8rpx {
    top: 8rpx;
}

.top-90rpx {
    top: 90rpx;
}

.top-94rpx {
    top: 94rpx;
}

.top-95rpx {
    top: 95rpx;
}

.top-auto {
    top: auto;
}

.before-left--50rpx::before {
    left: -50rpx;
}

.before-left-_bl_50_p__br_::before {
    left: 50%;
}

.before-left-0::before {
    left: 0;
}

.before-left-0rpx::before {
    left: 0;
}

.before-top-_bl_50_p__br_::before {
    top: 50%;
}

.before-top-10rpx::before {
    top: 10rpx;
}

.before-top-18rpx::before {
    top: 18rpx;
}

.before-top-8rpx::before {
    top: 8rpx;
}

.after-bottom--10px::after {
    bottom: -10px;
}

.after-bottom-0::after {
    bottom: 0;
}

.after-left--10px::after {
    left: -10px;
}

.after-left-_bl_-50_p__br_::after {
    left: -50%;
}

.after-left-0::after {
    left: 0;
}

.after-left-auto::after {
    left: auto;
}

.after-right--10px::after {
    right: -10px;
}

.after-right--50rpx::after {
    right: -50rpx;
}

.after-right-0::after {
    right: 0;
}

.after-top--10px::after {
    top: -10px;
}

.after-top-_bl_-50_p__br_::after {
    top: -50%;
}

.after-top-_bl_50_p__br_::after {
    top: 50%;
}

.after-top-0::after {
    top: 0;
}

.after-top-18rpx::after {
    top: 18rpx;
}

.after-top-auto::after {
    top: auto;
}

.not-last-after-bottom-0:not(:last-child)::after {
    bottom: 0;
}

.not-last-after-left-_bl_100_p__br_:not(:last-child)::after {
    left: 100%;
}

.not-last-after-left-0:not(:last-child)::after {
    left: 0;
}

.not-last-after-right-0:not(:last-child)::after {
    right: 0;
}

.not-last-after-top-25rpx:not(:last-child)::after {
    top: 25rpx;
}

.line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    line-clamp: 1;
}

.line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
}

.line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    line-clamp: 3;
}

.z-1,.z1 {
    z-index: 1;
}

.-z-1,.z--1 {
    z-index: -1;
}

.z-0 {
    z-index: 0;
}

.z-10 {
    z-index: 10;
}

.z-100 {
    z-index: 100;
}

.z-1000 {
    z-index: 1000;
}

.z-10000 {
    z-index: 10000;
}

.z-1000000 {
    z-index: 1000000;
}

.z-1000001 {
    z-index: 1000001;
}

.z-10001 {
    z-index: 10001;
}

.z-10002 {
    z-index: 10002;
}

.z-10003 {
    z-index: 10003;
}

.z-10009 {
    z-index: 10009;
}

.z-1001 {
    z-index: 1001;
}

.z-1002 {
    z-index: 1002;
}

.z-101 {
    z-index: 101;
}

.z-102 {
    z-index: 102;
}

.z-103 {
    z-index: 103;
}

.z-10801 {
    z-index: 10801;
}

.z-10802 {
    z-index: 10802;
}

.z-10803 {
    z-index: 10803;
}

.z-11 {
    z-index: 11;
}

.z-110 {
    z-index: 110;
}

.z-1100 {
    z-index: 1100;
}

.z-11000 {
    z-index: 11000;
}

.z-1111111 {
    z-index: 1111111;
}

.z-12 {
    z-index: 12;
}

.z-13 {
    z-index: 13;
}

.z-15 {
    z-index: 15;
}

.z-19 {
    z-index: 19;
}

.z-199 {
    z-index: 199;
}

.z-2 {
    z-index: 2;
}

.z-20 {
    z-index: 20;
}

.z-200 {
    z-index: 200;
}

.z-2000 {
    z-index: 2000;
}

.z-20000 {
    z-index: 20000;
}

.z-20001 {
    z-index: 20001;
}

.z-2001 {
    z-index: 2001;
}

.z-201 {
    z-index: 201;
}

.z-21 {
    z-index: 21;
}

.z-220 {
    z-index: 220;
}

.z-23 {
    z-index: 23;
}

.z-240 {
    z-index: 240;
}

.z-25 {
    z-index: 25;
}

.z-250 {
    z-index: 250;
}

.z-26 {
    z-index: 26;
}

.z-296 {
    z-index: 296;
}

.z-3 {
    z-index: 3;
}

.z-300 {
    z-index: 300;
}

.z-301 {
    z-index: 301;
}

.z-4 {
    z-index: 4;
}

.z-40 {
    z-index: 40;
}

.z-410 {
    z-index: 410;
}

.z-420 {
    z-index: 420;
}

.z-430 {
    z-index: 430;
}

.z-5 {
    z-index: 5;
}

.z-50 {
    z-index: 50;
}

.z-500 {
    z-index: 500;
}

.z-5000 {
    z-index: 5000;
}

.z-8 {
    z-index: 8;
}

.z-800 {
    z-index: 800;
}

.z-801 {
    z-index: 801;
}

.z-888 {
    z-index: 888;
}

.z-9 {
    z-index: 9;
}

.z-900 {
    z-index: 900;
}

.z-98 {
    z-index: 98;
}

.z-99 {
    z-index: 99;
}

.z-990 {
    z-index: 990;
}

.z-997 {
    z-index: 997;
}

.z-998 {
    z-index: 998;
}

.z-999 {
    z-index: 999;
}

.z-9998 {
    z-index: 9998;
}

.z-9999 {
    z-index: 9999;
}

.z-99999 {
    z-index: 99999;
}

.z-999999 {
    z-index: 999999;
}

.z-9999999_i_ {
    z-index: 9999999 !important;
}

.after-z--1::after {
    z-index: -1;
}

.after-z-1::after {
    z-index: 1;
}

.not-last-after-z-1:not(:last-child)::after {
    z-index: 1;
}

.grid {
    display: grid;
}

.grid-cols-2 {
    grid-template-columns: repeat(2,minmax(0,1fr));
}

.grid-cols-3 {
    grid-template-columns: repeat(3,minmax(0,1fr));
}

.grid-cols-4 {
    grid-template-columns: repeat(4,minmax(0,1fr));
}

.float-right {
    float: right;
}

.-m-24,.m--24 {
    margin: -24rpx;
}

.-m-8 {
    margin: -8rpx;
}

.-m12,.m--12 {
    margin: -12rpx;
}

._i_m-0 {
    margin: 0 !important;
}

.m-0,.m-0rpx,.m0 {
    margin: 0;
}

.m-10rpx {
    margin: 10rpx;
}

.m-20 {
    margin: 20rpx;
}

.m-24,.m-24rpx,.m24 {
    margin: 24rpx;
}

.m-30rpx {
    margin: 30rpx;
}

.m-32rpx {
    margin: 32rpx;
}

.m-36rpx {
    margin: 36rpx;
}

.m-4,.m-4rpx {
    margin: 4rpx;
}

.m-6 {
    margin: 6rpx;
}

.m-auto {
    margin: auto;
}

.last-m-0:last-child {
    margin: 0;
}

.-mx-14 {
    margin-left: -14rpx;
    margin-right: -14rpx;
}

.-mx-20,.-mx-20rpx,.mx--20 {
    margin-left: -20rpx;
    margin-right: -20rpx;
}

.-mx-24,.mx--24rpx {
    margin-left: -24rpx;
    margin-right: -24rpx;
}

.-mx-30rpx {
    margin-left: -30rpx;
    margin-right: -30rpx;
}

.-my-16 {
    margin-top: -16rpx;
    margin-bottom: -16rpx;
}

.-my-24 {
    margin-top: -24rpx;
    margin-bottom: -24rpx;
}

.m-x-0,.mx-0 {
    margin-left: 0;
    margin-right: 0;
}

.m-x-16rpx,.mx-16,.mx-16rpx {
    margin-left: 16rpx;
    margin-right: 16rpx;
}

.m-x-4rpx,.mx-4,.mx-4rpx {
    margin-left: 4rpx;
    margin-right: 4rpx;
}

.m-x-auto,.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

.m-y-24rpx,.my-24,.my-24rpx,.my24 {
    margin-top: 24rpx;
    margin-bottom: 24rpx;
}

.mx--100 {
    margin-left: -100rpx;
    margin-right: -100rpx;
}

.mx--8rpx {
    margin-left: -8rpx;
    margin-right: -8rpx;
}

.mx-_bl_48rpx_br_ {
    margin-left: 48rpx;
    margin-right: 48rpx;
}

.mx-10,.mx-10rpx,.mx10 {
    margin-left: 10rpx;
    margin-right: 10rpx;
}

.mx-12,.mx-12rpx {
    margin-left: 12rpx;
    margin-right: 12rpx;
}

.mx-15rpx {
    margin-left: 15rpx;
    margin-right: 15rpx;
}

.mx-18rpx {
    margin-left: 18rpx;
    margin-right: 18rpx;
}

.mx-2 {
    margin-left: 2rpx;
    margin-right: 2rpx;
}

.mx-20,.mx-20rpx,.mx20 {
    margin-left: 20rpx;
    margin-right: 20rpx;
}

.mx-22rpx {
    margin-left: 22rpx;
    margin-right: 22rpx;
}

.mx-23rpx {
    margin-left: 23rpx;
    margin-right: 23rpx;
}

.mx-24,.mx-24rpx,.mx24 {
    margin-left: 24rpx;
    margin-right: 24rpx;
}

.mx-25rpx {
    margin-left: 25rpx;
    margin-right: 25rpx;
}

.mx-26rpx {
    margin-left: 26rpx;
    margin-right: 26rpx;
}

.mx-27rpx {
    margin-left: 27rpx;
    margin-right: 27rpx;
}

.mx-28rpx {
    margin-left: 28rpx;
    margin-right: 28rpx;
}

.mx-30,.mx-30rpx {
    margin-left: 30rpx;
    margin-right: 30rpx;
}

.mx-32,.mx-32rpx {
    margin-left: 32rpx;
    margin-right: 32rpx;
}

.mx-34 {
    margin-left: 34rpx;
    margin-right: 34rpx;
}

.mx-36rpx {
    margin-left: 36rpx;
    margin-right: 36rpx;
}

.mx-38 {
    margin-left: 38rpx;
    margin-right: 38rpx;
}

.mx-3rpx {
    margin-left: 3rpx;
    margin-right: 3rpx;
}

.mx-40 {
    margin-left: 40rpx;
    margin-right: 40rpx;
}

.mx-45 {
    margin-left: 45rpx;
    margin-right: 45rpx;
}

.mx-5 {
    margin-left: 5rpx;
    margin-right: 5rpx;
}

.mx-6,.mx-6rpx {
    margin-left: 6rpx;
    margin-right: 6rpx;
}

.mx-7 {
    margin-left: 7rpx;
    margin-right: 7rpx;
}

.mx-8,.mx-8rpx,.mx8 {
    margin-left: 8rpx;
    margin-right: 8rpx;
}

.mx-auto_i_ {
    margin-left: auto !important;
    margin-right: auto !important;
}

.my--10,.my--10rpx {
    margin-top: -10rpx;
    margin-bottom: -10rpx;
}

.my--20 {
    margin-top: -20rpx;
    margin-bottom: -20rpx;
}

.my-_bl_0_p__br_ {
    margin-top: 0%;
    margin-bottom: 0%;
}

.my-0 {
    margin-top: 0;
    margin-bottom: 0;
}

.my-10,.my-10rpx {
    margin-top: 10rpx;
    margin-bottom: 10rpx;
}

.my-100 {
    margin-top: 100rpx;
    margin-bottom: 100rpx;
}

.my-10px {
    margin-top: 10px;
    margin-bottom: 10px;
}

.my-12rpx {
    margin-top: 12rpx;
    margin-bottom: 12rpx;
}

.my-13 {
    margin-top: 13rpx;
    margin-bottom: 13rpx;
}

.my-14rpx {
    margin-top: 14rpx;
    margin-bottom: 14rpx;
}

.my-15rpx {
    margin-top: 15rpx;
    margin-bottom: 15rpx;
}

.my-16,.my-16rpx,.my16 {
    margin-top: 16rpx;
    margin-bottom: 16rpx;
}

.my-18 {
    margin-top: 18rpx;
    margin-bottom: 18rpx;
}

.my-1px {
    margin-top: 1px;
    margin-bottom: 1px;
}

.my-2 {
    margin-top: 2rpx;
    margin-bottom: 2rpx;
}

.my-20,.my-20rpx,.my20 {
    margin-top: 20rpx;
    margin-bottom: 20rpx;
}

.my-22,.my-22rpx {
    margin-top: 22rpx;
    margin-bottom: 22rpx;
}

.my-28 {
    margin-top: 28rpx;
    margin-bottom: 28rpx;
}

.my-30,.my-30rpx {
    margin-top: 30rpx;
    margin-bottom: 30rpx;
}

.my-32,.my-32rpx {
    margin-top: 32rpx;
    margin-bottom: 32rpx;
}

.my-34rpx {
    margin-top: 34rpx;
    margin-bottom: 34rpx;
}

.my-4 {
    margin-top: 4rpx;
    margin-bottom: 4rpx;
}

.my-40,.my-40rpx {
    margin-top: 40rpx;
    margin-bottom: 40rpx;
}

.my-42rpx {
    margin-top: 42rpx;
    margin-bottom: 42rpx;
}

.my-50rpx {
    margin-top: 50rpx;
    margin-bottom: 50rpx;
}

.my-5px {
    margin-top: 5px;
    margin-bottom: 5px;
}

.my-6 {
    margin-top: 6rpx;
    margin-bottom: 6rpx;
}

.my-60rpx {
    margin-top: 60rpx;
    margin-bottom: 60rpx;
}

.my-8rpx {
    margin-top: 8rpx;
    margin-bottom: 8rpx;
}

.-m-r-11rpx {
    margin-right: -11rpx;
}

.-mb-12 {
    margin-bottom: -12rpx;
}

.-mb-14 {
    margin-bottom: -14rpx;
}

.-mb-16 {
    margin-bottom: -16rpx;
}

.-mb-20,.mb--20 {
    margin-bottom: -20rpx;
}

.-mb-28 {
    margin-bottom: -28rpx;
}

.-mb-32 {
    margin-bottom: -32rpx;
}

.-mb-48,.mb--48 {
    margin-bottom: -48rpx;
}

.-mb-8,.mb--8rpx {
    margin-bottom: -8rpx;
}

.-ml-24,.ml--24rpx {
    margin-left: -24rpx;
}

.-ml-25 {
    margin-left: -25rpx;
}

.-ml-70 {
    margin-left: -70rpx;
}

.-ml20,.ml--20rpx {
    margin-left: -20rpx;
}

.-mr-12 {
    margin-right: -12rpx;
}

.-mr-24 {
    margin-right: -24rpx;
}

.-mr-25 {
    margin-right: -25rpx;
}

.-mr10,.mr--10,.mr--10rpx {
    margin-right: -10rpx;
}

.-mr20 {
    margin-right: -20rpx;
}

.-mt-10rpx,.mt--10,.mt--10rpx {
    margin-top: -10rpx;
}

.-mt-12,.mt--12 {
    margin-top: -12rpx;
}

.-mt-20,.mt--20rpx {
    margin-top: -20rpx;
}

.-mt-24,.-mt24,.mt--24,.mt--24rpx,.mt-_bl_-24rpx_br_ {
    margin-top: -24rpx;
}

.-mt-25 {
    margin-top: -25rpx;
}

.-mt-26 {
    margin-top: -26rpx;
}

.-mt-40rpx,.mt--40rpx {
    margin-top: -40rpx;
}

.-mt-50,.mt--50rpx,.mt-_bl_-50rpx_br_ {
    margin-top: -50rpx;
}

.-mt-70 {
    margin-top: -70rpx;
}

.-mt-8,.mt--8rpx {
    margin-top: -8rpx;
}

.-mt2,.mt--2,.mt--2rpx {
    margin-top: -2rpx;
}

._i_ml-24 {
    margin-left: 24rpx !important;
}

._i_mt-24,.mt-24_i_ {
    margin-top: 24rpx !important;
}

._bl__u__c_nth-child_pl_1_pr__br__c_mt-0:nth-child(1),.mt-0,.mt-0rpx {
    margin-top: 0;
}

._bl__u__c_nth-child_pl_2_pr__br__c_ml-40:nth-child(2),.ml-40 {
    margin-left: 40rpx;
}

._bl__u__c_nth-child_pl_2n-1_pr__br__c_mr-16rpx:nth-child(2n-1),.m-r-16,.mr-16,.mr-16rpx,.not-last-mr-16rpx:not(:last-child) {
    margin-right: 16rpx;
}

._bl__u__c_nth-child_pl_2n_pr__br__c_mr-0:nth-child(2n),._bl__u__c_nth-child_pl_3n_a_3_pr__br__c_mr-0:nth-child(3n+3),.mr-0 {
    margin-right: 0;
}

._bl__u__c_nth-last-child_pl_1_pr__br__c_mr-60rpx:nth-last-child(1),.mr-60,.mr60 {
    margin-right: 60rpx;
}

._bl__u__c_nth-last-of-type_pl_-n_a_3_pr__br__c_mb-0:nth-last-of-type(-n+3),.mb-0,.mb-0rpx {
    margin-bottom: 0;
}

.m-b-20,.mb-20,.mb-20rpx {
    margin-bottom: 20rpx;
}

.mb--10 {
    margin-bottom: -10rpx;
}

.mb--22px_i_ {
    margin-bottom: -22px !important;
}

.mb--40rpx {
    margin-bottom: -40rpx;
}

.mb--52 {
    margin-bottom: -52rpx;
}

.mb--72rpx {
    margin-bottom: -72rpx;
}

.mb-_bl_1em_br_ {
    margin-bottom: 1em;
}

.mb-_bl_60rpx_br_,.mb-60,.mb-60rpx {
    margin-bottom: 60rpx;
}

.mb-_bl_calc_pl_env_pl_safe-area-inset-bottom_pr__u_-1_s_2_pr__br_ {
    margin-bottom: calc(env(safe-area-inset-bottom) * -1 / 2);
}

.mb-1 {
    margin-bottom: 1rpx;
}

.mb-10,.mb-10rpx,.mb10 {
    margin-bottom: 10rpx;
}

.mb-10_i_ {
    margin-bottom: 10rpx !important;
}

.mb-110 {
    margin-bottom: 110rpx;
}

.mb-12,.mb-12rpx {
    margin-bottom: 12rpx;
}

.mb-14,.mb-14rpx {
    margin-bottom: 14rpx;
}

.mb-15,.mb-15rpx {
    margin-bottom: 15rpx;
}

.mb-15px {
    margin-bottom: 15px;
}

.mb-16,.mb-16rpx,.mb16,.not-last-mb-16rpx:not(:last-child) {
    margin-bottom: 16rpx;
}

.mb-18,.mb-18rpx,.mb18 {
    margin-bottom: 18rpx;
}

.mb-22,.mb-22rpx {
    margin-bottom: 22rpx;
}

.mb-23 {
    margin-bottom: 23rpx;
}

.mb-24,.mb-24rpx,.mb24 {
    margin-bottom: 24rpx;
}

.mb-25rpx {
    margin-bottom: 25rpx;
}

.mb-26,.mb-26rpx {
    margin-bottom: 26rpx;
}

.mb-28,.mb-28rpx {
    margin-bottom: 28rpx;
}

.mb-2rpx {
    margin-bottom: 2rpx;
}

.mb-3 {
    margin-bottom: 3rpx;
}

.mb-30,.mb-30rpx {
    margin-bottom: 30rpx;
}

.mb-32,.mb-32rpx,.mb32 {
    margin-bottom: 32rpx;
}

.mb-34,.mb-34rpx {
    margin-bottom: 34rpx;
}

.mb-36rpx {
    margin-bottom: 36rpx;
}

.mb-38,.mb-38rpx {
    margin-bottom: 38rpx;
}

.mb-4,.mb-4rpx,.mb4 {
    margin-bottom: 4rpx;
}

.mb-40,.mb-40rpx {
    margin-bottom: 40rpx;
}

.mb-43rpx {
    margin-bottom: 43rpx;
}

.mb-44,.mb-44rpx {
    margin-bottom: 44rpx;
}

.mb-45rpx {
    margin-bottom: 45rpx;
}

.mb-48,.mb-48rpx,.not-last-mb-48rpx:not(:last-child) {
    margin-bottom: 48rpx;
}

.mb-4px {
    margin-bottom: 4px;
}

.mb-5,.mb-5rpx {
    margin-bottom: 5rpx;
}

.mb-50rpx {
    margin-bottom: 50rpx;
}

.mb-53rpx {
    margin-bottom: 53rpx;
}

.mb-58rpx {
    margin-bottom: 58rpx;
}

.mb-6,.mb-6rpx {
    margin-bottom: 6rpx;
}

.mb-64rpx {
    margin-bottom: 64rpx;
}

.mb-65rpx {
    margin-bottom: 65rpx;
}

.mb-70rpx {
    margin-bottom: 70rpx;
}

.mb-74rpx {
    margin-bottom: 74rpx;
}

.mb-8,.mb-8rpx {
    margin-bottom: 8rpx;
}

.mb-80,.mb-80rpx {
    margin-bottom: 80rpx;
}

.mb-83rpx {
    margin-bottom: 83rpx;
}

.mb-9 {
    margin-bottom: 9rpx;
}

.mb130 {
    margin-bottom: 130rpx;
}

.ml--10,.ml--10rpx {
    margin-left: -10rpx;
}

.ml--15rpx {
    margin-left: -15rpx;
}

.ml--16 {
    margin-left: -16rpx;
}

.ml--210rpx {
    margin-left: -210rpx;
}

.ml--22rpx {
    margin-left: -22rpx;
}

.ml--295rpx {
    margin-left: -295rpx;
}

.ml--29rpx {
    margin-left: -29rpx;
}

.ml--2rpx {
    margin-left: -2rpx;
}

.ml--30rpx {
    margin-left: -30rpx;
}

.ml--335rpx {
    margin-left: -335rpx;
}

.ml--50rpx {
    margin-left: -50rpx;
}

.ml--5rpx {
    margin-left: -5rpx;
}

.ml--62rpx {
    margin-left: -62rpx;
}

.ml--80 {
    margin-left: -80rpx;
}

.ml-0 {
    margin-left: 0;
}

.ml-10,.ml-10rpx,.ml10 {
    margin-left: 10rpx;
}

.ml-10_i_ {
    margin-left: 10rpx !important;
}

.ml-114rpx {
    margin-left: 114rpx;
}

.ml-12,.ml-12rpx,.ml12 {
    margin-left: 12rpx;
}

.ml-14,.ml-14rpx {
    margin-left: 14rpx;
}

.ml-15,.ml-15rpx {
    margin-left: 15rpx;
}

.ml-16,.ml-16rpx,.ml16,.not-first-ml-16rpx:not(:first-child) {
    margin-left: 16rpx;
}

.ml-16_i_ {
    margin-left: 16rpx !important;
}

.ml-18rpx {
    margin-left: 18rpx;
}

.ml-2,.ml-2rpx {
    margin-left: 2rpx;
}

.ml-20,.ml-20rpx,.ml20 {
    margin-left: 20rpx;
}

.ml-204 {
    margin-left: 204rpx;
}

.ml-22,.ml-22rpx {
    margin-left: 22rpx;
}

.ml-24,.ml-24rpx {
    margin-left: 24rpx;
}

.ml-25rpx {
    margin-left: 25rpx;
}

.ml-26rpx {
    margin-left: 26rpx;
}

.ml-27rpx {
    margin-left: 27rpx;
}

.ml-28rpx,.not-first-ml-28rpx:not(:first-child) {
    margin-left: 28rpx;
}

.ml-30,.ml-30rpx,.ml30 {
    margin-left: 30rpx;
}

.ml-32,.ml-32rpx {
    margin-left: 32rpx;
}

.ml-36,.ml-36rpx {
    margin-left: 36rpx;
}

.ml-38,.ml-38rpx {
    margin-left: 38rpx;
}

.ml-4,.ml-4rpx {
    margin-left: 4rpx;
}

.ml-46 {
    margin-left: 46rpx;
}

.ml-48rpx {
    margin-left: 48rpx;
}

.ml-5,.ml-5rpx {
    margin-left: 5rpx;
}

.ml-50,.ml-50rpx {
    margin-left: 50rpx;
}

.ml-52rpx {
    margin-left: 52rpx;
}

.ml-6,.ml-6rpx,.ml6,.ml6rpx {
    margin-left: 6rpx;
}

.ml-60rpx {
    margin-left: 60rpx;
}

.ml-7,.ml-7rpx {
    margin-left: 7rpx;
}

.ml-8,.ml-8rpx,.ml8 {
    margin-left: 8rpx;
}

.ml-80,.ml-80rpx {
    margin-left: 80rpx;
}

.ml-9,.ml-9rpx {
    margin-left: 9rpx;
}

.ml-auto {
    margin-left: auto;
}

.ml13 {
    margin-left: 13rpx;
}

.mr--23 {
    margin-right: -23rpx;
}

.mr--2rpx {
    margin-right: -2rpx;
}

.mr--8 {
    margin-right: -8rpx;
}

.mr-_bl_calc_pl__pl_100_p_-480rpx_pr__s_2_pr__br_ {
    margin-right: calc((100% - 480rpx) / 2);
}

.mr-10,.mr-10rpx,.mr10 {
    margin-right: 10rpx;
}

.mr-10px {
    margin-right: 10px;
}

.mr-11 {
    margin-right: 11rpx;
}

.mr-12,.mr-12rpx,.mr12 {
    margin-right: 12rpx;
}

.mr-14,.mr-14rpx {
    margin-right: 14rpx;
}

.mr-15,.mr-15rpx {
    margin-right: 15rpx;
}

.mr-16_i_ {
    margin-right: 16rpx !important;
}

.mr-17rpx {
    margin-right: 17rpx;
}

.mr-18,.mr-18rpx {
    margin-right: 18rpx;
}

.mr-1rpx {
    margin-right: 1rpx;
}

.mr-20,.mr-20rpx,.mr20 {
    margin-right: 20rpx;
}

.mr-21rpx {
    margin-right: 21rpx;
}

.mr-22rpx {
    margin-right: 22rpx;
}

.mr-23 {
    margin-right: 23rpx;
}

.mr-24,.mr-24rpx,.mr24 {
    margin-right: 24rpx;
}

.mr-24_i_ {
    margin-right: 24rpx !important;
}

.mr-25rpx {
    margin-right: 25rpx;
}

.mr-26rpx {
    margin-right: 26rpx;
}

.mr-27 {
    margin-right: 27rpx;
}

.mr-28,.mr-28rpx {
    margin-right: 28rpx;
}

.mr-30,.mr-30rpx {
    margin-right: 30rpx;
}

.mr-32,.mr-32rpx,.not-last-mr-32rpx:not(:last-child) {
    margin-right: 32rpx;
}

.mr-38 {
    margin-right: 38rpx;
}

.mr-3rpx {
    margin-right: 3rpx;
}

.mr-4,.mr-4rpx {
    margin-right: 4rpx;
}

.mr-40,.mr-40rpx {
    margin-right: 40rpx;
}

.mr-42 {
    margin-right: 42rpx;
}

.mr-43rpx {
    margin-right: 43rpx;
}

.mr-5,.mr-5rpx {
    margin-right: 5rpx;
}

.mr-50,.not-last-mr-50rpx:not(:last-child) {
    margin-right: 50rpx;
}

.mr-6,.mr-6rpx,.mr6 {
    margin-right: 6rpx;
}

.mr-7 {
    margin-right: 7rpx;
}

.mr-8,.mr-8rpx,.mr8 {
    margin-right: 8rpx;
}

.mr-80rpx,.not-last-mr-80rpx:not(:last-child) {
    margin-right: 80rpx;
}

.mr-86rpx {
    margin-right: 86rpx;
}

.mr-9rpx {
    margin-right: 9rpx;
}

.mr-auto {
    margin-right: auto;
}

.mt--15 {
    margin-top: -15rpx;
}

.mt--150rpx {
    margin-top: -150rpx;
}

.mt--16,.mt--16rpx {
    margin-top: -16rpx;
}

.mt--18 {
    margin-top: -18rpx;
}

.mt--195rpx {
    margin-top: -195rpx;
}

.mt--250rpx {
    margin-top: -250rpx;
}

.mt--30rpx {
    margin-top: -30rpx;
}

.mt--45rpx {
    margin-top: -45rpx;
}

.mt--4rpx {
    margin-top: -4rpx;
}

.mt--60rpx {
    margin-top: -60rpx;
}

.mt--6rpx {
    margin-top: -6rpx;
}

.mt--72rpx {
    margin-top: -72rpx;
}

.mt--74rpx {
    margin-top: -74rpx;
}

.mt--80 {
    margin-top: -80rpx;
}

.mt--9rpx {
    margin-top: -9rpx;
}

.mt-_bl_40rpx_br_,.mt-40,.mt-40rpx,.not-first-mt-40rpx:not(:first-child) {
    margin-top: 40rpx;
}

.mt-1 {
    margin-top: 1rpx;
}

.mt-10,.mt-10rpx,.mt10,.mt10rpx {
    margin-top: 10rpx;
}

.mt-100,.mt-100rpx {
    margin-top: 100rpx;
}

.mt-100_i_ {
    margin-top: 100rpx !important;
}

.mt-102rpx {
    margin-top: 102rpx;
}

.mt-108rpx {
    margin-top: 108rpx;
}

.mt-10px {
    margin-top: 10px;
}

.mt-11 {
    margin-top: 11rpx;
}

.mt-12,.mt-12rpx,.mt12 {
    margin-top: 12rpx;
}

.mt-120,.mt-120rpx {
    margin-top: 120rpx;
}

.mt-13,.mt-13rpx {
    margin-top: 13rpx;
}

.mt-130rpx {
    margin-top: 130rpx;
}

.mt-132 {
    margin-top: 132rpx;
}

.mt-14,.mt-14rpx,.mt14 {
    margin-top: 14rpx;
}

.mt-140rpx {
    margin-top: 140rpx;
}

.mt-15,.mt-15rpx {
    margin-top: 15rpx;
}

.mt-150rpx {
    margin-top: 150rpx;
}

.mt-155 {
    margin-top: 155rpx;
}

.mt-158rpx {
    margin-top: 158rpx;
}

.mt-16,.mt-16rpx,.mt16 {
    margin-top: 16rpx;
}

.mt-167rpx {
    margin-top: 167rpx;
}

.mt-17,.mt-17rpx {
    margin-top: 17rpx;
}

.mt-174,.mt-174rpx {
    margin-top: 174rpx;
}

.mt-176 {
    margin-top: 176rpx;
}

.mt-178rpx {
    margin-top: 178rpx;
}

.mt-18,.mt-18rpx,.mt18 {
    margin-top: 18rpx;
}

.mt-192 {
    margin-top: 192rpx;
}

.mt-19rpx {
    margin-top: 19rpx;
}

.mt-2,.mt-2rpx {
    margin-top: 2rpx;
}

.mt-20,.mt-20rpx,.mt20,.not-first-mt-20rpx:not(:first-child) {
    margin-top: 20rpx;
}

.mt-200,.mt-200rpx {
    margin-top: 200rpx;
}

.mt-206 {
    margin-top: 206rpx;
}

.mt-21 {
    margin-top: 21rpx;
}

.mt-22,.mt-22rpx {
    margin-top: 22rpx;
}

.mt-24,.mt-24rpx,.mt24,.not-first-mt-24rpx:not(:first-child) {
    margin-top: 24rpx;
}

.mt-25,.mt-25rpx {
    margin-top: 25rpx;
}

.mt-250rpx {
    margin-top: 250rpx;
}

.mt-260,.mt-260rpx {
    margin-top: 260rpx;
}

.mt-26rpx {
    margin-top: 26rpx;
}

.mt-27rpx {
    margin-top: 27rpx;
}

.mt-28rpx {
    margin-top: 28rpx;
}

.mt-29,.mt-29rpx {
    margin-top: 29rpx;
}

.mt-30,.mt-30rpx,.not-first-mt-30rpx:not(:first-child) {
    margin-top: 30rpx;
}

.mt-31rpx {
    margin-top: 31rpx;
}

.mt-32,.mt-32rpx {
    margin-top: 32rpx;
}

.mt-34rpx {
    margin-top: 34rpx;
}

.mt-35rpx {
    margin-top: 35rpx;
}

.mt-36,.mt-36rpx,.mt36 {
    margin-top: 36rpx;
}

.mt-38,.mt-38rpx {
    margin-top: 38rpx;
}

.mt-386 {
    margin-top: 386rpx;
}

.mt-4,.mt-4rpx,.mt4 {
    margin-top: 4rpx;
}

.mt-42,.mt-42rpx {
    margin-top: 42rpx;
}

.mt-43rpx,.not-first-mt-43rpx:not(:first-child) {
    margin-top: 43rpx;
}

.mt-44,.mt-44rpx {
    margin-top: 44rpx;
}

.mt-440rpx_i_ {
    margin-top: 440rpx !important;
}

.mt-46,.mt-46rpx {
    margin-top: 46rpx;
}

.mt-48,.mt-48rpx,.mt48 {
    margin-top: 48rpx;
}

.mt-50,.mt-50rpx {
    margin-top: 50rpx;
}

.mt-54rpx {
    margin-top: 54rpx;
}

.mt-56rpx {
    margin-top: 56rpx;
}

.mt-58rpx {
    margin-top: 58rpx;
}

.mt-59rpx {
    margin-top: 59rpx;
}

.mt-5rpx {
    margin-top: 5rpx;
}

.mt-6,.mt-6rpx {
    margin-top: 6rpx;
}

.mt-60,.mt-60rpx {
    margin-top: 60rpx;
}

.mt-61 {
    margin-top: 61rpx;
}

.mt-62rpx {
    margin-top: 62rpx;
}

.mt-64rpx {
    margin-top: 64rpx;
}

.mt-66,.mt-66rpx {
    margin-top: 66rpx;
}

.mt-66_i_ {
    margin-top: 66rpx !important;
}

.mt-6px {
    margin-top: 6px;
}

.mt-7 {
    margin-top: 7rpx;
}

.mt-70rpx {
    margin-top: 70rpx;
}

.mt-76rpx {
    margin-top: 76rpx;
}

.mt-8,.mt-8rpx,.mt8 {
    margin-top: 8rpx;
}

.mt-80rpx {
    margin-top: 80rpx;
}

.mt-82 {
    margin-top: 82rpx;
}

.mt-86 {
    margin-top: 86rpx;
}

.mt-9 {
    margin-top: 9rpx;
}

.mt-90,.mt-90rpx {
    margin-top: 90rpx;
}

.mt-92rpx {
    margin-top: 92rpx;
}

.even-mb-0:nth-child(even) {
    margin-bottom: 0;
}

.even-ml-16:nth-child(even) {
    margin-left: 16rpx;
}

.first-ml-0:first-child {
    margin-left: 0;
}

.first-ml-22:first-child {
    margin-left: 22rpx;
}

.first-ml-24rpx:first-child {
    margin-left: 24rpx;
}

.first-ml-40:first-child {
    margin-left: 40rpx;
}

.first-ml0:first-child {
    margin-left: 0;
}

.first-mt-0:first-child {
    margin-top: 0;
}

.first-mt0:first-child {
    margin-top: 0;
}

.last-mb-0:last-child {
    margin-bottom: 0;
}

.last-mb-16rpx:last-child {
    margin-bottom: 16rpx;
}

.last-ml-20rpx:last-child {
    margin-left: 20rpx;
}

.last-mr-0:last-child {
    margin-right: 0;
}

.last-mr-0rpx:last-child {
    margin-right: 0;
}

.last-mr-16rpx:last-child {
    margin-right: 16rpx;
}

.last-mr-22:last-child {
    margin-right: 22rpx;
}

.last-mr-24rpx:last-child {
    margin-right: 24rpx;
}

.last_c_mb-0:last-child {
    margin-bottom: 0;
}

.after-mt--14rpx::after {
    margin-top: -14rpx;
}

.box-border {
    box-sizing: border-box;
}

.after-box-border::after {
    box-sizing: border-box;
}

.box-content {
    box-sizing: content-box;
}

.inline {
    display: inline;
}

.block {
    display: block;
}

.placeholder-block::placeholder {
    display: block;
}

.before-block::before {
    display: block;
}

.after-block::after {
    display: block;
}

.after_c_block::after {
    display: block;
}

.inline-block {
    display: inline-block;
}

.after-inline-block::after {
    display: inline-block;
}

.list-item {
    display: list-item;
}

.hidden {
    display: none;
}

.first-before-hidden:first-child::before {
    display: none;
}

.last-after-hidden:last-child::after {
    display: none;
}

.last_c_after_c_hidden:last-child::after {
    display: none;
}

.before-hidden::before {
    display: none;
}

.after-hidden::after {
    display: none;
}

._i_h-750 {
    height: 750rpx !important;
}

._i_h-902 {
    height: 902rpx !important;
}

._i_h-fit {
    height: fit-content !important;
}

._i_min-h-170 {
    min-height: 170rpx !important;
}

._i_min-w-120 {
    min-width: 120rpx !important;
}

._i_w-_bl_50_p__br_,.w-_bl_50_p__br__i_ {
    width: 50% !important;
}

._i_w-_bl_70_p__br_,.w-_bl_70_p__br__i_ {
    width: 70% !important;
}

._i_w-full,.w-_bl_100_p__br__i_ {
    width: 100% !important;
}

.h-_bl_1_d_2em_br_ {
    height: 1.2em;
}

.h-_bl_100_p__br_,.h-100_p_,.h-full {
    height: 100%;
}

.h-_bl_108rpx_br_,.h-108rpx {
    height: 108rpx;
}

.h-_bl_200_p__br_ {
    height: 200%;
}

.h-_bl_3_d_2em_br_ {
    height: 3.2em;
}

.h-_bl_40rpx_br_,.h-40,.h-40rpx {
    height: 40rpx;
}

.h-_bl_55vh_br_ {
    height: 55vh;
}

.h-_bl_98rpx_br_,.h-98,.h-98rpx {
    height: 98rpx;
}

.h-_bl_calc_pl_100_p_-200rpx_pr__br_ {
    height: calc(100% - 200rpx);
}

.h-_bl_calc_pl_100_p_-220rpx_pr__br_ {
    height: calc(100% - 220rpx);
}

.h-_bl_calc_pl_100_p_-30rpx_pr__br_ {
    height: calc(100% - 30rpx);
}

.h-_bl_calc_pl_100_p_-60rpx_pr__br_ {
    height: calc(100% - 60rpx);
}

.h-_bl_calc_pl_100_p_-90rpx_pr__br_ {
    height: calc(100% - 90rpx);
}

.h-_bl_calc_pl_100rpx_a_constant_pl_safe-area-inset-bottom_pr__pr__br_ {
    height: calc(100rpx + constant(safe-area-inset-bottom));
}

.h-_bl_calc_pl_100rpx_a_env_pl_safe-area-inset-bottom_pr__pr__br_ {
    height: calc(100rpx + env(safe-area-inset-bottom));
}

.h-_bl_calc_pl_100vh-_pl_110rpx_a_constant_pl_safe-area-inset-bottom_pr__pr__pr__br_ {
    height: calc(100vh - (110rpx + constant(safe-area-inset-bottom)));
}

.h-_bl_calc_pl_100vh-_pl_110rpx_a_env_pl_safe-area-inset-bottom_pr__pr__pr__br_ {
    height: calc(100vh - (110rpx + env(safe-area-inset-bottom)));
}

.h-_bl_calc_pl_100vh-_pl_240rpx_a_constant_pl_safe-area-inset-bottom_pr__pr__pr__br_ {
    height: calc(100vh - (240rpx + constant(safe-area-inset-bottom)));
}

.h-_bl_calc_pl_100vh-_pl_240rpx_a_env_pl_safe-area-inset-bottom_pr__pr__pr__br_ {
    height: calc(100vh - (240rpx + env(safe-area-inset-bottom)));
}

.h-_bl_calc_pl_100vh-580rpx_pr__br_ {
    height: calc(100vh - 580rpx);
}

.h-_bl_calc_pl_100vh-598rpx_pr__br_ {
    height: calc(100vh - 598rpx);
}

.h-_bl_calc_pl_100vh-598rpx_a_104rpx_pr__br_ {
    height: calc(100vh - 598rpx + 104rpx);
}

.h-_bl_calc_pl_100vh-700rpx_pr__br_ {
    height: calc(100vh - 700rpx);
}

.h-_bl_calc_pl_100vh-92rpx_pr__br_ {
    height: calc(100vh - 92rpx);
}

.h-_bl_calc_pl_124rpx_a_constant_pl_safe-area-inset-bottom_pr__pr__br_ {
    height: calc(124rpx + constant(safe-area-inset-bottom));
}

.h-_bl_calc_pl_124rpx_a_env_pl_safe-area-inset-bottom_pr__pr__br_ {
    height: calc(124rpx + env(safe-area-inset-bottom));
}

.h-_bl_calc_pl_68vh_a_132rpx_pr__br_ {
    height: calc(68vh + 132rpx);
}

.h-_bl_calc_pl_75vh-108rpx_pr__br_ {
    height: calc(75vh - 108rpx);
}

.h-_bl_calc_pl_802rpx-110rpx_pr__br_ {
    height: calc(802rpx - 110rpx);
}

.h-_bl_calc_pl_env_pl_safe-area-inset-bottom_pr__a_268rpx_pr__br__i_ {
    height: calc(env(safe-area-inset-bottom) + 268rpx) !important;
}

.h-_bl_calc_pl_env_pl_safe-area-inset-bottom_pr__a_308rpx_pr__br__i_ {
    height: calc(env(safe-area-inset-bottom) + 308rpx) !important;
}

.h-_bl_constant_pl_safe-area-inset-bottom_pr__br_ {
    height: constant(safe-area-inset-bottom);
}

.h-_bl_env_pl_safe-area-inset-bottom_pr__br_ {
    height: env(safe-area-inset-bottom);
}

.h-0 {
    height: 0;
}

.h-1,.h-1rpx {
    height: 1rpx;
}

.h-1_s_2,.h-50_p_ {
    height: 50%;
}

.h-10,.h-10rpx {
    height: 10rpx;
}

.h-100,.h-100rpx {
    height: 100rpx;
}

.h-100vh,.h-screen {
    height: 100vh;
}

.h-100vw {
    height: 100vw;
}

.h-102,.h-102rpx {
    height: 102rpx;
}

.h-104,.h-104rpx {
    height: 104rpx;
}

.h-105rpx {
    height: 105rpx;
}

.h-106,.h-106rpx {
    height: 106rpx;
}

.h-1062rpx {
    height: 1062rpx;
}

.h-110,.h-110rpx {
    height: 110rpx;
}

.h-112,.h-112rpx {
    height: 112rpx;
}

.h-114,.h-114rpx {
    height: 114rpx;
}

.h-115rpx {
    height: 115rpx;
}

.h-116,.h-116rpx {
    height: 116rpx;
}

.h-118,.h-118rpx {
    height: 118rpx;
}

.h-11rpx {
    height: 11rpx;
}

.h-12,.h-12rpx {
    height: 12rpx;
}

.h-120,.h-120rpx {
    height: 120rpx;
}

.h-120rpx_i_ {
    height: 120rpx !important;
}

.h-124rpx {
    height: 124rpx;
}

.h-126,.h-126rpx {
    height: 126rpx;
}

.h-128,.h-128rpx {
    height: 128rpx;
}

.h-130,.h-130rpx {
    height: 130rpx;
}

.h-134rpx {
    height: 134rpx;
}

.h-135rpx {
    height: 135rpx;
}

.h-136,.h-136rpx {
    height: 136rpx;
}

.h-138,.h-138rpx {
    height: 138rpx;
}

.h-140,.h-140rpx {
    height: 140rpx;
}

.h-142,.h-142rpx {
    height: 142rpx;
}

.h-143rpx {
    height: 143rpx;
}

.h-144,.h-144rpx {
    height: 144rpx;
}

.h-145rpx {
    height: 145rpx;
}

.h-146,.h-146rpx {
    height: 146rpx;
}

.h-148,.h-148rpx {
    height: 148rpx;
}

.h-14rpx {
    height: 14rpx;
}

.h-150,.h-150rpx {
    height: 150rpx;
}

.h-154 {
    height: 154rpx;
}

.h-158,.h-158rpx {
    height: 158rpx;
}

.h-15rpx {
    height: 15rpx;
}

.h-16,.h-16rpx {
    height: 16rpx;
}

.h-160,.h-160rpx {
    height: 160rpx;
}

.h-162,.h-162rpx {
    height: 162rpx;
}

.h-1624rpx {
    height: 1624rpx;
}

.h-163 {
    height: 163rpx;
}

.h-164,.h-164rpx {
    height: 164rpx;
}

.h-166rpx {
    height: 166rpx;
}

.h-168,.h-168rpx {
    height: 168rpx;
}

.h-170,.h-170rpx {
    height: 170rpx;
}

.h-172rpx {
    height: 172rpx;
}

.h-174rpx {
    height: 174rpx;
}

.h-176rpx {
    height: 176rpx;
}

.h-178rpx {
    height: 178rpx;
}

.h-18,.h-18rpx {
    height: 18rpx;
}

.h-180,.h-180rpx {
    height: 180rpx;
}

.h-182rpx {
    height: 182rpx;
}

.h-184,.h-184rpx {
    height: 184rpx;
}

.h-188 {
    height: 188rpx;
}

.h-1908 {
    height: 1908rpx;
}

.h-190px {
    height: 190px;
}

.h-190rpx {
    height: 190rpx;
}

.h-192rpx {
    height: 192rpx;
}

.h-194rpx {
    height: 194rpx;
}

.h-196rpx {
    height: 196rpx;
}

.h-1px {
    height: 1px;
}

.h-2,.h-2rpx {
    height: 2rpx;
}

.h-20,.h-20rpx {
    height: 20rpx;
}

.h-200,.h-200rpx {
    height: 200rpx;
}

.h-206_i_ {
    height: 206rpx !important;
}

.h-20px {
    height: 20px;
}

.h-210,.h-210rpx {
    height: 210rpx;
}

.h-210_i_ {
    height: 210rpx !important;
}

.h-214rpx {
    height: 214rpx;
}

.h-216,.h-216rpx {
    height: 216rpx;
}

.h-218rpx {
    height: 218rpx;
}

.h-21rpx {
    height: 21rpx;
}

.h-22,.h-22rpx {
    height: 22rpx;
}

.h-220,.h-220rpx {
    height: 220rpx;
}

.h-224rpx {
    height: 224rpx;
}

.h-225 {
    height: 225rpx;
}

.h-226rpx {
    height: 226rpx;
}

.h-22vh {
    height: 22vh;
}

.h-230,.h-230rpx {
    height: 230rpx;
}

.h-234 {
    height: 234rpx;
}

.h-24,.h-24rpx {
    height: 24rpx;
}

.h-240,.h-240rpx {
    height: 240rpx;
}

.h-242rpx {
    height: 242rpx;
}

.h-244 {
    height: 244rpx;
}

.h-246rpx {
    height: 246rpx;
}

.h-250,.h-250rpx {
    height: 250rpx;
}

.h-256rpx {
    height: 256rpx;
}

.h-258rpx {
    height: 258rpx;
}

.h-25rpx {
    height: 25rpx;
}

.h-26,.h-26rpx {
    height: 26rpx;
}

.h-260,.h-260rpx {
    height: 260rpx;
}

.h-264rpx {
    height: 264rpx;
}

.h-268rpx {
    height: 268rpx;
}

.h-27,.h-27rpx {
    height: 27rpx;
}

.h-278 {
    height: 278rpx;
}

.h-28,.h-28rpx {
    height: 28rpx;
}

.h-280,.h-280rpx {
    height: 280rpx;
}

.h-282rpx {
    height: 282rpx;
}

.h-284 {
    height: 284rpx;
}

.h-286rpx {
    height: 286rpx;
}

.h-29,.h-29rpx {
    height: 29rpx;
}

.h-292rpx {
    height: 292rpx;
}

.h-30,.h-30rpx,.h30 {
    height: 30rpx;
}

.h-300,.h-300rpx {
    height: 300rpx;
}

.h-300rpx_i_ {
    height: 300rpx !important;
}

.h-302rpx {
    height: 302rpx;
}

.h-31 {
    height: 31rpx;
}

.h-312,.h-312rpx {
    height: 312rpx;
}

.h-315rpx {
    height: 315rpx;
}

.h-32,.h-32rpx {
    height: 32rpx;
}

.h-320,.h-320rpx {
    height: 320rpx;
}

.h-322 {
    height: 322rpx;
}

.h-33,.h-33rpx {
    height: 33rpx;
}

.h-330,.h-330rpx {
    height: 330rpx;
}

.h-34,.h-34rpx {
    height: 34rpx;
}

.h-340,.h-340rpx {
    height: 340rpx;
}

.h-344rpx {
    height: 344rpx;
}

.h-348 {
    height: 348rpx;
}

.h-350,.h-350rpx {
    height: 350rpx;
}

.h-351rpx {
    height: 351rpx;
}

.h-356rpx {
    height: 356rpx;
}

.h-357 {
    height: 357rpx;
}

.h-358rpx {
    height: 358rpx;
}

.h-35rpx {
    height: 35rpx;
}

.h-35vh {
    height: 35vh;
}

.h-36,.h-36rpx {
    height: 36rpx;
}

.h-36_i_ {
    height: 36rpx !important;
}

.h-360,.h-360rpx {
    height: 360rpx;
}

.h-370 {
    height: 370rpx;
}

.h-374rpx {
    height: 374rpx;
}

.h-378rpx {
    height: 378rpx;
}

.h-38,.h-38rpx {
    height: 38rpx;
}

.h-380,.h-380rpx {
    height: 380rpx;
}

.h-382 {
    height: 382rpx;
}

.h-39,.h-39rpx {
    height: 39rpx;
}

.h-390rpx {
    height: 390rpx;
}

.h-396,.h-396rpx {
    height: 396rpx;
}

.h-39px {
    height: 39px;
}

.h-3rpx {
    height: 3rpx;
}

.h-4,.h-4rpx {
    height: 4rpx;
}

.h-400,.h-400rpx {
    height: 400rpx;
}

.h-402rpx {
    height: 402rpx;
}

.h-406rpx {
    height: 406rpx;
}

.h-42,.h-42rpx {
    height: 42rpx;
}

.h-420rpx {
    height: 420rpx;
}

.h-430,.h-430rpx {
    height: 430rpx;
}

.h-44,.h-44rpx {
    height: 44rpx;
}

.h-440rpx {
    height: 440rpx;
}

.h-444rpx {
    height: 444rpx;
}

.h-44px {
    height: 44px;
}

.h-450rpx {
    height: 450rpx;
}

.h-452rpx {
    height: 452rpx;
}

.h-45rpx {
    height: 45rpx;
}

.h-46,.h-46rpx {
    height: 46rpx;
}

.h-460rpx {
    height: 460rpx;
}

.h-462rpx {
    height: 462rpx;
}

.h-478 {
    height: 478rpx;
}

.h-47rpx {
    height: 47rpx;
}

.h-48,.h-48rpx,.h48 {
    height: 48rpx;
}

.h-480,.h-480rpx {
    height: 480rpx;
}

.h-494rpx {
    height: 494rpx;
}

.h-50,.h-50rpx {
    height: 50rpx;
}

.h-500,.h-500rpx {
    height: 500rpx;
}

.h-50px {
    height: 50px;
}

.h-512rpx {
    height: 512rpx;
}

.h-514,.h-514rpx {
    height: 514rpx;
}

.h-52,.h-52rpx {
    height: 52rpx;
}

.h-52_i_ {
    height: 52rpx !important;
}

.h-520,.h-520rpx {
    height: 520rpx;
}

.h-524rpx {
    height: 524rpx;
}

.h-53rpx {
    height: 53rpx;
}

.h-54,.h-54rpx {
    height: 54rpx;
}

.h-550rpx {
    height: 550rpx;
}

.h-56,.h-56rpx {
    height: 56rpx;
}

.h-560rpx {
    height: 560rpx;
}

.h-57 {
    height: 57rpx;
}

.h-58,.h-58rpx {
    height: 58rpx;
}

.h-580rpx {
    height: 580rpx;
}

.h-590rpx {
    height: 590rpx;
}

.h-6,.h-6rpx {
    height: 6rpx;
}

.h-60,.h-60rpx,.h60 {
    height: 60rpx;
}

.h-60_p_ {
    height: 60%;
}

.h-600rpx {
    height: 600rpx;
}

.h-60px {
    height: 60px;
}

.h-612rpx {
    height: 612rpx;
}

.h-62,.h-62rpx {
    height: 62rpx;
}

.h-620rpx {
    height: 620rpx;
}

.h-62vh {
    height: 62vh;
}

.h-63 {
    height: 63rpx;
}

.h-630rpx {
    height: 630rpx;
}

.h-64,.h-64rpx {
    height: 64rpx;
}

.h-640rpx {
    height: 640rpx;
}

.h-650rpx {
    height: 650rpx;
}

.h-656rpx {
    height: 656rpx;
}

.h-658rpx {
    height: 658rpx;
}

.h-66,.h-66rpx {
    height: 66rpx;
}

.h-660,.h-660rpx {
    height: 660rpx;
}

.h-672 {
    height: 672rpx;
}

.h-68,.h-68rpx {
    height: 68rpx;
}

.h-680,.h-680rpx {
    height: 680rpx;
}

.h-683 {
    height: 683rpx;
}

.h-690rpx {
    height: 690rpx;
}

.h-70,.h-70rpx,.h70 {
    height: 70rpx;
}

.h-70_i_ {
    height: 70rpx !important;
}

.h-700rpx {
    height: 700rpx;
}

.h-702rpx {
    height: 702rpx;
}

.h-72,.h-72rpx {
    height: 72rpx;
}

.h-720,.h-720rpx {
    height: 720rpx;
}

.h-74,.h-74rpx {
    height: 74rpx;
}

.h-740rpx {
    height: 740rpx;
}

.h-750,.h-750rpx {
    height: 750rpx;
}

.h-75rpx {
    height: 75rpx;
}

.h-76,.h-76rpx {
    height: 76rpx;
}

.h-760,.h-760rpx {
    height: 760rpx;
}

.h-77,.h-77rpx {
    height: 77rpx;
}

.h-78 {
    height: 78rpx;
}

.h-7rpx {
    height: 7rpx;
}

.h-8,.h-8rpx {
    height: 8rpx;
}

.h-80,.h-80rpx,.h80 {
    height: 80rpx;
}

.h-80_i_ {
    height: 80rpx !important;
}

.h-800,.h-800rpx {
    height: 800rpx;
}

.h-802rpx {
    height: 802rpx;
}

.h-80px {
    height: 80px;
}

.h-80vh {
    height: 80vh;
}

.h-81rpx {
    height: 81rpx;
}

.h-82,.h-82rpx {
    height: 82rpx;
}

.h-820,.h-820rpx {
    height: 820rpx;
}

.h-828rpx {
    height: 828rpx;
}

.h-84,.h-84rpx {
    height: 84rpx;
}

.h-850rpx {
    height: 850rpx;
}

.h-85rpx {
    height: 85rpx;
}

.h-86,.h-86rpx {
    height: 86rpx;
}

.h-87rpx {
    height: 87rpx;
}

.h-88,.h-88rpx {
    height: 88rpx;
}

.h-89rpx {
    height: 89rpx;
}

.h-90,.h-90rpx {
    height: 90rpx;
}

.h-900,.h-900rpx {
    height: 900rpx;
}

.h-92,.h-92rpx {
    height: 92rpx;
}

.h-94,.h-94rpx {
    height: 94rpx;
}

.h-96,.h-96rpx {
    height: 96rpx;
}

.h-960rpx_i_ {
    height: 960rpx !important;
}

.h-987rpx {
    height: 987rpx;
}

.h-99rpx {
    height: 99rpx;
}

.h-auto {
    height: auto;
}

.h-auto_i_ {
    height: auto !important;
}

.h-fit {
    height: fit-content;
}

.h-full_i_ {
    height: 100% !important;
}

.h-initial_i_ {
    height: initial !important;
}

.h-max {
    height: max-content;
}

.h206 {
    height: 206rpx;
}

.max-h-_bl_100_p__br_ {
    max-height: 100%;
}

.max-h-_bl_1000vh_br_ {
    max-height: 1000vh;
}

.max-h-_bl_80rpx_br_ {
    max-height: 80rpx;
}

.max-h-_bl_calc_pl_100vh-118rpx_pr__br_ {
    max-height: calc(100vh - 118rpx);
}

.max-h-_bl_calc_pl_62vh-116rpx_pr__br_ {
    max-height: calc(62vh - 116rpx);
}

.max-h-_bl_calc_pl_75vh-164rpx_a_constant_pl_safe-area-inset-bottom_pr__pr__br_ {
    max-height: calc(75vh - 164rpx + constant(safe-area-inset-bottom));
}

.max-h-_bl_calc_pl_75vh-164rpx_a_env_pl_safe-area-inset-bottom_pr__pr__br_ {
    max-height: calc(75vh - 164rpx + env(safe-area-inset-bottom));
}

.max-h-_bl_calc_pl_75vh-90rpx_pr__br_ {
    max-height: calc(75vh - 90rpx);
}

.max-h-_bl_calc_pl_802rpx-110rpx_pr__br_ {
    max-height: calc(802rpx - 110rpx);
}

.max-h-_bl_calc_pl_85vh-90rpx_pr__br_ {
    max-height: calc(85vh - 90rpx);
}

.max-h-0 {
    max-height: 0;
}

.max-h-1000 {
    max-height: 1000rpx;
}

.max-h-1000px {
    max-height: 1000px;
}

.max-h-100vh {
    max-height: 100vh;
}

.max-h-104 {
    max-height: 104rpx;
}

.max-h-30vh {
    max-height: 30vh;
}

.max-h-320rpx {
    max-height: 320rpx;
}

.max-h-370rpx {
    max-height: 370rpx;
}

.max-h-400px {
    max-height: 400px;
}

.max-h-400rpx {
    max-height: 400rpx;
}

.max-h-42rpx {
    max-height: 42rpx;
}

.max-h-456rpx {
    max-height: 456rpx;
}

.max-h-466 {
    max-height: 466rpx;
}

.max-h-475,.max-h-475rpx {
    max-height: 475rpx;
}

.max-h-490rpx {
    max-height: 490rpx;
}

.max-h-492rpx {
    max-height: 492rpx;
}

.max-h-500rpx {
    max-height: 500rpx;
}

.max-h-508rpx {
    max-height: 508rpx;
}

.max-h-50vh {
    max-height: 50vh;
}

.max-h-520rpx {
    max-height: 520rpx;
}

.max-h-600rpx {
    max-height: 600rpx;
}

.max-h-60vh {
    max-height: 60vh;
}

.max-h-622rpx {
    max-height: 622rpx;
}

.max-h-640 {
    max-height: 640rpx;
}

.max-h-65vh {
    max-height: 65vh;
}

.max-h-66vh {
    max-height: 66vh;
}

.max-h-680rpx {
    max-height: 680rpx;
}

.max-h-68vh {
    max-height: 68vh;
}

.max-h-700rpx {
    max-height: 700rpx;
}

.max-h-70vh {
    max-height: 70vh;
}

.max-h-720 {
    max-height: 720rpx;
}

.max-h-750rpx {
    max-height: 750rpx;
}

.max-h-75vh {
    max-height: 75vh;
}

.max-h-800,.max-h-800rpx {
    max-height: 800rpx;
}

.max-h-802rpx {
    max-height: 802rpx;
}

.max-h-80vh {
    max-height: 80vh;
}

.max-h-938rpx {
    max-height: 938rpx;
}

.max-w-_bl_100_p__br_,.max-w-100_p_,.max-w-full {
    max-width: 100%;
}

.max-w-_bl_70_p__br_ {
    max-width: 70%;
}

.max-w-_bl_85_p__br_ {
    max-width: 85%;
}

.max-w-_bl_90_p__br_ {
    max-width: 90%;
}

.max-w-_bl_98_p__br_ {
    max-width: 98%;
}

.max-w-_bl_calc_pl_100_p_-110rpx_pr__br_ {
    max-width: calc(100% - 110rpx);
}

.max-w-116rpx {
    max-width: 116rpx;
}

.max-w-130rpx {
    max-width: 130rpx;
}

.max-w-150rpx {
    max-width: 150rpx;
}

.max-w-160,.max-w-160rpx {
    max-width: 160rpx;
}

.max-w-16ch {
    max-width: 16ch;
}

.max-w-178rpx {
    max-width: 178rpx;
}

.max-w-200,.max-w-200rpx {
    max-width: 200rpx;
}

.max-w-240rpx {
    max-width: 240rpx;
}

.max-w-246rpx {
    max-width: 246rpx;
}

.max-w-276rpx {
    max-width: 276rpx;
}

.max-w-284rpx {
    max-width: 284rpx;
}

.max-w-290rpx {
    max-width: 290rpx;
}

.max-w-3_s_5,.max-w-60_p_ {
    max-width: 60%;
}

.max-w-300,.max-w-300rpx {
    max-width: 300rpx;
}

.max-w-314rpx {
    max-width: 314rpx;
}

.max-w-320 {
    max-width: 320rpx;
}

.max-w-340,.max-w-340rpx {
    max-width: 340rpx;
}

.max-w-360rpx {
    max-width: 360rpx;
}

.max-w-380rpx {
    max-width: 380rpx;
}

.max-w-392rpx {
    max-width: 392rpx;
}

.max-w-400,.max-w-400rpx {
    max-width: 400rpx;
}

.max-w-430rpx {
    max-width: 430rpx;
}

.max-w-450rpx {
    max-width: 450rpx;
}

.max-w-464rpx {
    max-width: 464rpx;
}

.max-w-470rpx {
    max-width: 470rpx;
}

.max-w-480,.max-w-480rpx {
    max-width: 480rpx;
}

.max-w-500,.max-w-500rpx {
    max-width: 500rpx;
}

.max-w-506,.max-w-506rpx {
    max-width: 506rpx;
}

.max-w-540rpx {
    max-width: 540rpx;
}

.max-w-560,.max-w-560rpx {
    max-width: 560rpx;
}

.max-w-580 {
    max-width: 580rpx;
}

.max-w-590rpx {
    max-width: 590rpx;
}

.max-w-600rpx {
    max-width: 600rpx;
}

.max-w-64 {
    max-width: 64rpx;
}

.max-w-650,.max-w-650rpx {
    max-width: 650rpx;
}

.max-w-672rpx {
    max-width: 672rpx;
}

.max-w-686 {
    max-width: 686rpx;
}

.min-h-_bl_100_p__br_,.min-h-full {
    min-height: 100%;
}

.min-h-_bl_3_d_2em_br_ {
    min-height: 3.2em;
}

.min-h-_bl_35_p__br_ {
    min-height: 35%;
}

.min-h-_bl_calc_pl_30vh-164rpx_a_constant_pl_safe-area-inset-bottom_pr__pr__br_ {
    min-height: calc(30vh - 164rpx + constant(safe-area-inset-bottom));
}

.min-h-_bl_calc_pl_30vh-164rpx_a_env_pl_safe-area-inset-bottom_pr__pr__br_ {
    min-height: calc(30vh - 164rpx + env(safe-area-inset-bottom));
}

.min-h-_bl_calc_pl_35vh-90rpx_pr__br_ {
    min-height: calc(35vh - 90rpx);
}

.min-h-0 {
    min-height: 0;
}

.min-h-100,.min-h-100rpx {
    min-height: 100rpx;
}

.min-h-100vh,.min-h-screen {
    min-height: 100vh;
}

.min-h-108rpx {
    min-height: 108rpx;
}

.min-h-110 {
    min-height: 110rpx;
}

.min-h-120,.min-h-120rpx {
    min-height: 120rpx;
}

.min-h-130 {
    min-height: 130rpx;
}

.min-h-136 {
    min-height: 136rpx;
}

.min-h-140,.min-h-140rpx {
    min-height: 140rpx;
}

.min-h-147 {
    min-height: 147rpx;
}

.min-h-150rpx {
    min-height: 150rpx;
}

.min-h-158 {
    min-height: 158rpx;
}

.min-h-160,.min-h-160rpx {
    min-height: 160rpx;
}

.min-h-180,.min-h-180rpx {
    min-height: 180rpx;
}

.min-h-190rpx {
    min-height: 190rpx;
}

.min-h-200rpx {
    min-height: 200rpx;
}

.min-h-20vh {
    min-height: 20vh;
}

.min-h-210rpx {
    min-height: 210rpx;
}

.min-h-220 {
    min-height: 220rpx;
}

.min-h-224 {
    min-height: 224rpx;
}

.min-h-24rpx {
    min-height: 24rpx;
}

.min-h-250rpx {
    min-height: 250rpx;
}

.min-h-288 {
    min-height: 288rpx;
}

.min-h-30 {
    min-height: 30rpx;
}

.min-h-300rpx {
    min-height: 300rpx;
}

.min-h-309rpx {
    min-height: 309rpx;
}

.min-h-30vh {
    min-height: 30vh;
}

.min-h-320 {
    min-height: 320rpx;
}

.min-h-35vh {
    min-height: 35vh;
}

.min-h-36 {
    min-height: 36rpx;
}

.min-h-37 {
    min-height: 37rpx;
}

.min-h-400,.min-h-400rpx {
    min-height: 400rpx;
}

.min-h-42 {
    min-height: 42rpx;
}

.min-h-47rpx {
    min-height: 47rpx;
}

.min-h-500rpx {
    min-height: 500rpx;
}

.min-h-520 {
    min-height: 520rpx;
}

.min-h-548 {
    min-height: 548rpx;
}

.min-h-568rpx {
    min-height: 568rpx;
}

.min-h-600,.min-h-600rpx {
    min-height: 600rpx;
}

.min-h-60rpx {
    min-height: 60rpx;
}

.min-h-64rpx {
    min-height: 64rpx;
}

.min-h-68,.min-h-68rpx {
    min-height: 68rpx;
}

.min-h-72 {
    min-height: 72rpx;
}

.min-h-75rpx {
    min-height: 75rpx;
}

.min-h-76 {
    min-height: 76rpx;
}

.min-h-78rpx {
    min-height: 78rpx;
}

.min-h-80 {
    min-height: 80rpx;
}

.min-h-810rpx {
    min-height: 810rpx;
}

.min-h-90rpx {
    min-height: 90rpx;
}

.min-h-96rpx {
    min-height: 96rpx;
}

.min-w-0 {
    min-width: 0;
}

.min-w-1_s_3 {
    min-width: 33.3333333333%;
}

.min-w-100,.min-w-100rpx {
    min-width: 100rpx;
}

.min-w-106rpx {
    min-width: 106rpx;
}

.min-w-110,.min-w-110rpx {
    min-width: 110rpx;
}

.min-w-112 {
    min-width: 112rpx;
}

.min-w-120,.min-w-120rpx {
    min-width: 120rpx;
}

.min-w-130rpx {
    min-width: 130rpx;
}

.min-w-138 {
    min-width: 138rpx;
}

.min-w-140 {
    min-width: 140rpx;
}

.min-w-145 {
    min-width: 145rpx;
}

.min-w-148rpx {
    min-width: 148rpx;
}

.min-w-150,.min-w-150rpx {
    min-width: 150rpx;
}

.min-w-160,.min-w-160rpx {
    min-width: 160rpx;
}

.min-w-170 {
    min-width: 170rpx;
}

.min-w-180rpx {
    min-width: 180rpx;
}

.min-w-200rpx {
    min-width: 200rpx;
}

.min-w-208 {
    min-width: 208rpx;
}

.min-w-210rpx {
    min-width: 210rpx;
}

.min-w-24 {
    min-width: 24rpx;
}

.min-w-3_s_5 {
    min-width: 60%;
}

.min-w-32,.min-w-32rpx {
    min-width: 32rpx;
}

.min-w-36 {
    min-width: 36rpx;
}

.min-w-380rpx {
    min-width: 380rpx;
}

.min-w-39 {
    min-width: 39rpx;
}

.min-w-40rpx {
    min-width: 40rpx;
}

.min-w-44 {
    min-width: 44rpx;
}

.min-w-54rpx {
    min-width: 54rpx;
}

.min-w-60rpx {
    min-width: 60rpx;
}

.min-w-64 {
    min-width: 64rpx;
}

.min-w-64px {
    min-width: 64px;
}

.min-w-68rpx {
    min-width: 68rpx;
}

.min-w-72rpx {
    min-width: 72rpx;
}

.min-w-73 {
    min-width: 73rpx;
}

.min-w-76 {
    min-width: 76rpx;
}

.min-w-78rpx {
    min-width: 78rpx;
}

.min-w-80,.min-w-80rpx {
    min-width: 80rpx;
}

.min-w-86rpx {
    min-width: 86rpx;
}

.min-w-96rpx {
    min-width: 96rpx;
}

.min-w-98rpx {
    min-width: 98rpx;
}

.w-_bl_100_p__br_,.w-100_p_,.w-full {
    width: 100%;
}

.w-_bl_10000_p__br_ {
    width: 10000%;
}

.w-_bl_106vw_br_ {
    width: 106vw;
}

.w-_bl_200_p__br_ {
    width: 200%;
}

.w-_bl_25_p__br_,.w-1_s_4 {
    width: 25%;
}

.w-_bl_30_p__br__i_ {
    width: 30% !important;
}

.w-_bl_33_d_333_p__br_ {
    width: 33.333%;
}

.w-_bl_33_d_3333333_p__br_ {
    width: 33.3333333%;
}

.w-_bl_33_d_3333333333_p__br_ {
    width: 33.3333333333%;
}

.w-_bl_40_p__br_,.w-40_p_ {
    width: 40%;
}

.w-_bl_50_p__br_,.w-1_s_2 {
    width: 50%;
}

.w-_bl_580rpx_br_,.w-580,.w-580rpx {
    width: 580rpx;
}

.w-_bl_600rpx_br_,.w-600,.w-600rpx {
    width: 600rpx;
}

.w-_bl_73_p__br_ {
    width: 73%;
}

.w-_bl_80_p__br_,.w-4_s_5 {
    width: 80%;
}

.w-_bl_90_p__br_ {
    width: 90%;
}

.w-_bl_95_p__br_ {
    width: 95%;
}

.w-_bl_calc_pl__pl_100_p_-10rpx_pr__s_2_pr__br_ {
    width: calc((100% - 10rpx) / 2);
}

.w-_bl_calc_pl__pl_100_p_-20rpx_pr__s_3_pr__br_ {
    width: calc((100% - 20rpx) / 3);
}

.w-_bl_calc_pl__pl_100_p_-30rpx_pr__s_4_pr__br_ {
    width: calc((100% - 30rpx) / 4);
}

.w-_bl_calc_pl__pl_100_p_-40rpx_pr__s_5_pr__br_ {
    width: calc((100% - 40rpx) / 5);
}

.w-_bl_calc_pl_100_p_-150px_pr__br_ {
    width: calc(100% - 150px);
}

.w-_bl_calc_pl_100_p_-150rpx_pr__br_ {
    width: calc(100% - 150rpx);
}

.w-_bl_calc_pl_100_p_-24rpx_pr__br_ {
    width: calc(100% - 24rpx);
}

.w-_bl_calc_pl_100_p_-304rpx_pr__br_ {
    width: calc(100% - 304rpx);
}

.w-_bl_calc_pl_100_p_-36rpx_pr__br_ {
    width: calc(100% - 36rpx);
}

.w-_bl_calc_pl_100_p_-40rpx_pr__br_ {
    width: calc(100% - 40rpx);
}

.w-_bl_calc_pl_100_p_-56rpx_pr__br_ {
    width: calc(100% - 56rpx);
}

.w-_bl_calc_pl_100_p_-60rpx_pr__br_ {
    width: calc(100% - 60rpx);
}

.w-_bl_calc_pl_100_p_-64rpx_pr__br_ {
    width: calc(100% - 64rpx);
}

.w-_bl_calc_pl_100_p_-80rpx_pr__br_ {
    width: calc(100% - 80rpx);
}

.w-_bl_calc_pl_100_p__a_20rpx_pr__br_ {
    width: calc(100% + 20rpx);
}

.w-_bl_calc_pl_100_p__a_var_pl_--lr-padding_pr__u_2_pr__br_ {
    width: calc(100% + var(--lr-padding) * 2);
}

.w-_bl_calc_pl_100vw_-_96rpx_pr__br_ {
    width: calc(100vw - 96rpx);
}

.w-_bl_calc_pl_100vw-48rpx_pr__br_ {
    width: calc(100vw - 48rpx);
}

.w-_bl_calc_pl_100vw-60rpx_pr__br_ {
    width: calc(100vw - 60rpx);
}

.w-0 {
    width: 0;
}

.w-1,.w-1rpx {
    width: 1rpx;
}

.w-10,.w-10rpx {
    width: 10rpx;
}

.w-100,.w-100rpx {
    width: 100rpx;
}

.w-1000 {
    width: 1000rpx;
}

.w-100vh {
    width: 100vh;
}

.w-100vw,.w-screen {
    width: 100vw;
}

.w-102rpx {
    width: 102rpx;
}

.w-104rpx {
    width: 104rpx;
}

.w-106rpx {
    width: 106rpx;
}

.w-108rpx {
    width: 108rpx;
}

.w-110,.w-110rpx {
    width: 110rpx;
}

.w-110_p_ {
    width: 110%;
}

.w-112,.w-112rpx {
    width: 112rpx;
}

.w-113rpx {
    width: 113rpx;
}

.w-114,.w-114rpx {
    width: 114rpx;
}

.w-116,.w-116rpx {
    width: 116rpx;
}

.w-117rpx {
    width: 117rpx;
}

.w-118,.w-118rpx {
    width: 118rpx;
}

.w-11rpx {
    width: 11rpx;
}

.w-12,.w-12rpx {
    width: 12rpx;
}

.w-120,.w-120rpx {
    width: 120rpx;
}

.w-124rpx {
    width: 124rpx;
}

.w-126rpx {
    width: 126rpx;
}

.w-128,.w-128rpx {
    width: 128rpx;
}

.w-130,.w-130rpx {
    width: 130rpx;
}

.w-130_i_ {
    width: 130rpx !important;
}

.w-132rpx {
    width: 132rpx;
}

.w-136,.w-136rpx {
    width: 136rpx;
}

.w-138 {
    width: 138rpx;
}

.w-139rpx {
    width: 139rpx;
}

.w-13rpx {
    width: 13rpx;
}

.w-140,.w-140rpx {
    width: 140rpx;
}

.w-140_i_ {
    width: 140rpx !important;
}

.w-142,.w-142rpx {
    width: 142rpx;
}

.w-143rpx {
    width: 143rpx;
}

.w-144,.w-144rpx {
    width: 144rpx;
}

.w-146,.w-146rpx {
    width: 146rpx;
}

.w-147 {
    width: 147rpx;
}

.w-148,.w-148rpx {
    width: 148rpx;
}

.w-14rpx {
    width: 14rpx;
}

.w-15,.w-15rpx {
    width: 15rpx;
}

.w-150,.w-150rpx {
    width: 150rpx;
}

.w-150px {
    width: 150px;
}

.w-152,.w-152rpx {
    width: 152rpx;
}

.w-154rpx {
    width: 154rpx;
}

.w-155,.w-155rpx {
    width: 155rpx;
}

.w-158rpx {
    width: 158rpx;
}

.w-16,.w-16rpx {
    width: 16rpx;
}

.w-160,.w-160rpx {
    width: 160rpx;
}

.w-162rpx {
    width: 162rpx;
}

.w-163rpx {
    width: 163rpx;
}

.w-164rpx {
    width: 164rpx;
}

.w-165rpx {
    width: 165rpx;
}

.w-166rpx {
    width: 166rpx;
}

.w-168,.w-168rpx {
    width: 168rpx;
}

.w-170rpx {
    width: 170rpx;
}

.w-172rpx {
    width: 172rpx;
}

.w-175rpx {
    width: 175rpx;
}

.w-176rpx {
    width: 176rpx;
}

.w-178rpx {
    width: 178rpx;
}

.w-17rpx {
    width: 17rpx;
}

.w-18,.w-18rpx {
    width: 18rpx;
}

.w-180,.w-180rpx {
    width: 180rpx;
}

.w-184 {
    width: 184rpx;
}

.w-190,.w-190rpx {
    width: 190rpx;
}

.w-190px {
    width: 190px;
}

.w-195rpx {
    width: 195rpx;
}

.w-196rpx {
    width: 196rpx;
}

.w-199rpx {
    width: 199rpx;
}

.w-1px,.w-px {
    width: 1px;
}

.w-2,.w-2rpx {
    width: 2rpx;
}

.w-20,.w-20rpx {
    width: 20rpx;
}

.w-200,.w-200rpx,.w200 {
    width: 200rpx;
}

.w-204 {
    width: 204rpx;
}

.w-206,.w-206rpx {
    width: 206rpx;
}

.w-208_i_ {
    width: 208rpx !important;
}

.w-208rpx {
    width: 208rpx;
}

.w-20px {
    width: 20px;
}

.w-210,.w-210rpx {
    width: 210rpx;
}

.w-214rpx {
    width: 214rpx;
}

.w-216,.w-216rpx {
    width: 216rpx;
}

.w-218rpx {
    width: 218rpx;
}

.w-21rpx {
    width: 21rpx;
}

.w-220,.w-220rpx {
    width: 220rpx;
}

.w-222,.w-222rpx {
    width: 222rpx;
}

.w-226rpx {
    width: 226rpx;
}

.w-22rpx {
    width: 22rpx;
}

.w-230,.w-230rpx {
    width: 230rpx;
}

.w-234rpx {
    width: 234rpx;
}

.w-235rpx {
    width: 235rpx;
}

.w-238rpx {
    width: 238rpx;
}

.w-24,.w-24rpx {
    width: 24rpx;
}

.w-240,.w-240rpx {
    width: 240rpx;
}

.w-240_i_ {
    width: 240rpx !important;
}

.w-241rpx {
    width: 241rpx;
}

.w-242rpx {
    width: 242rpx;
}

.w-250,.w-250rpx {
    width: 250rpx;
}

.w-252rpx {
    width: 252rpx;
}

.w-254rpx {
    width: 254rpx;
}

.w-26,.w-26rpx {
    width: 26rpx;
}

.w-260,.w-260rpx {
    width: 260rpx;
}

.w-264rpx {
    width: 264rpx;
}

.w-268 {
    width: 268rpx;
}

.w-27,.w-27rpx {
    width: 27rpx;
}

.w-270rpx {
    width: 270rpx;
}

.w-280,.w-280rpx {
    width: 280rpx;
}

.w-284,.w-284rpx {
    width: 284rpx;
}

.w-287rpx {
    width: 287rpx;
}

.w-288rpx {
    width: 288rpx;
}

.w-28rpx {
    width: 28rpx;
}

.w-290,.w-290rpx {
    width: 290rpx;
}

.w-290_i_ {
    width: 290rpx !important;
}

.w-292rpx {
    width: 292rpx;
}

.w-296 {
    width: 296rpx;
}

.w-3_s_5 {
    width: 60%;
}

.w-30,.w-30rpx {
    width: 30rpx;
}

.w-300,.w-300rpx {
    width: 300rpx;
}

.w-308 {
    width: 308rpx;
}

.w-310,.w-310rpx {
    width: 310rpx;
}

.w-312,.w-312rpx {
    width: 312rpx;
}

.w-318rpx {
    width: 318rpx;
}

.w-32,.w-32rpx {
    width: 32rpx;
}

.w-320,.w-320rpx {
    width: 320rpx;
}

.w-321 {
    width: 321rpx;
}

.w-330,.w-330rpx {
    width: 330rpx;
}

.w-34,.w-34rpx {
    width: 34rpx;
}

.w-340,.w-340rpx {
    width: 340rpx;
}

.w-343 {
    width: 343rpx;
}

.w-346rpx {
    width: 346rpx;
}

.w-348 {
    width: 348rpx;
}

.w-350,.w-350rpx {
    width: 350rpx;
}

.w-358rpx {
    width: 358rpx;
}

.w-36,.w-36rpx {
    width: 36rpx;
}

.w-360,.w-360rpx {
    width: 360rpx;
}

.w-360_i_ {
    width: 360rpx !important;
}

.w-362 {
    width: 362rpx;
}

.w-364rpx {
    width: 364rpx;
}

.w-366rpx {
    width: 366rpx;
}

.w-367rpx {
    width: 367rpx;
}

.w-37rpx {
    width: 37rpx;
}

.w-38,.w-38rpx {
    width: 38rpx;
}

.w-380,.w-380rpx {
    width: 380rpx;
}

.w-386rpx {
    width: 386rpx;
}

.w-390rpx {
    width: 390rpx;
}

.w-40,.w-40rpx {
    width: 40rpx;
}

.w-400,.w-400rpx {
    width: 400rpx;
}

.w-410rpx {
    width: 410rpx;
}

.w-418rpx {
    width: 418rpx;
}

.w-42,.w-42rpx {
    width: 42rpx;
}

.w-420,.w-420rpx {
    width: 420rpx;
}

.w-422rpx {
    width: 422rpx;
}

.w-430,.w-430rpx {
    width: 430rpx;
}

.w-432rpx {
    width: 432rpx;
}

.w-436rpx {
    width: 436rpx;
}

.w-44,.w-44rpx {
    width: 44rpx;
}

.w-440 {
    width: 440rpx;
}

.w-441rpx {
    width: 441rpx;
}

.w-4440 {
    width: 4440rpx;
}

.w-450rpx {
    width: 450rpx;
}

.w-45rpx {
    width: 45rpx;
}

.w-46,.w-46rpx {
    width: 46rpx;
}

.w-460,.w-460rpx {
    width: 460rpx;
}

.w-464rpx {
    width: 464rpx;
}

.w-470rpx {
    width: 470rpx;
}

.w-48,.w-48rpx {
    width: 48rpx;
}

.w-480,.w-480rpx {
    width: 480rpx;
}

.w-484 {
    width: 484rpx;
}

.w-488,.w-488rpx {
    width: 488rpx;
}

.w-490rpx {
    width: 490rpx;
}

.w-4rpx {
    width: 4rpx;
}

.w-50,.w-50rpx {
    width: 50rpx;
}

.w-500,.w-500rpx {
    width: 500rpx;
}

.w-510rpx {
    width: 510rpx;
}

.w-51rpx {
    width: 51rpx;
}

.w-52,.w-52rpx {
    width: 52rpx;
}

.w-520,.w-520rpx {
    width: 520rpx;
}

.w-535rpx {
    width: 535rpx;
}

.w-54,.w-54rpx {
    width: 54rpx;
}

.w-540,.w-540rpx {
    width: 540rpx;
}

.w-542,.w-542rpx {
    width: 542rpx;
}

.w-545,.w-545rpx {
    width: 545rpx;
}

.w-550rpx {
    width: 550rpx;
}

.w-55rpx {
    width: 55rpx;
}

.w-56,.w-56rpx {
    width: 56rpx;
}

.w-560,.w-560rpx {
    width: 560rpx;
}

.w-568 {
    width: 568rpx;
}

.w-570,.w-570rpx {
    width: 570rpx;
}

.w-572rpx {
    width: 572rpx;
}

.w-574,.w-574rpx {
    width: 574rpx;
}

.w-584 {
    width: 584rpx;
}

.w-58rpx {
    width: 58rpx;
}

.w-590,.w-590rpx {
    width: 590rpx;
}

.w-595rpx {
    width: 595rpx;
}

.w-60,.w-60rpx {
    width: 60rpx;
}

.w-602rpx {
    width: 602rpx;
}

.w-60px {
    width: 60px;
}

.w-610rpx {
    width: 610rpx;
}

.w-62,.w-62rpx {
    width: 62rpx;
}

.w-620,.w-620rpx {
    width: 620rpx;
}

.w-624rpx {
    width: 624rpx;
}

.w-628 {
    width: 628rpx;
}

.w-630rpx {
    width: 630rpx;
}

.w-634rpx {
    width: 634rpx;
}

.w-64,.w-64rpx {
    width: 64rpx;
}

.w-640rpx {
    width: 640rpx;
}

.w-642rpx {
    width: 642rpx;
}

.w-644rpx {
    width: 644rpx;
}

.w-645rpx {
    width: 645rpx;
}

.w-646 {
    width: 646rpx;
}

.w-649 {
    width: 649rpx;
}

.w-650rpx {
    width: 650rpx;
}

.w-654rpx {
    width: 654rpx;
}

.w-66,.w-66rpx {
    width: 66rpx;
}

.w-660rpx {
    width: 660rpx;
}

.w-666rpx {
    width: 666rpx;
}

.w-668rpx {
    width: 668rpx;
}

.w-670rpx {
    width: 670rpx;
}

.w-68,.w-68rpx {
    width: 68rpx;
}

.w-680,.w-680rpx {
    width: 680rpx;
}

.w-682rpx {
    width: 682rpx;
}

.w-685rpx {
    width: 685rpx;
}

.w-686,.w-686rpx {
    width: 686rpx;
}

.w-688rpx {
    width: 688rpx;
}

.w-690rpx {
    width: 690rpx;
}

.w-694rpx {
    width: 694rpx;
}

.w-6rpx {
    width: 6rpx;
}

.w-70,.w-70rpx {
    width: 70rpx;
}

.w-700,.w-700rpx {
    width: 700rpx;
}

.w-702,.w-702rpx {
    width: 702rpx;
}

.w-70vw {
    width: 70vw;
}

.w-710 {
    width: 710rpx;
}

.w-72,.w-72rpx {
    width: 72rpx;
}

.w-731 {
    width: 731rpx;
}

.w-732rpx {
    width: 732rpx;
}

.w-74rpx {
    width: 74rpx;
}

.w-75 {
    width: 75rpx;
}

.w-750,.w-750rpx {
    width: 750rpx;
}

.w-76,.w-76rpx {
    width: 76rpx;
}

.w-79rpx {
    width: 79rpx;
}

.w-8,.w-8rpx {
    width: 8rpx;
}

.w-80,.w-80rpx {
    width: 80rpx;
}

.w-80px {
    width: 80px;
}

.w-80vw {
    width: 80vw;
}

.w-81rpx {
    width: 81rpx;
}

.w-82,.w-82rpx {
    width: 82rpx;
}

.w-84,.w-84rpx {
    width: 84rpx;
}

.w-85vw {
    width: 85vw;
}

.w-86,.w-86rpx {
    width: 86rpx;
}

.w-87rpx {
    width: 87rpx;
}

.w-88,.w-88rpx {
    width: 88rpx;
}

.w-8em {
    width: 8em;
}

.w-9 {
    width: 9rpx;
}

.w-90,.w-90rpx {
    width: 90rpx;
}

.w-900 {
    width: 900rpx;
}

.w-92,.w-92rpx {
    width: 92rpx;
}

.w-94rpx {
    width: 94rpx;
}

.w-96rpx {
    width: 96rpx;
}

.w-98rpx {
    width: 98rpx;
}

.w-auto {
    width: auto;
}

.w-fit {
    width: fit-content;
}

.before-h-_bl_50_p__br_::before {
    height: 50%;
}

.before-h-24rpx::before {
    height: 24rpx;
}

.before-h-4rpx::before {
    height: 4rpx;
}

.before-h-82rpx::before {
    height: 82rpx;
}

.before-w-_bl_50_p__br_::before {
    width: 50%;
}

.before-w-1rpx::before {
    width: 1rpx;
}

.before-w-30rpx::before {
    width: 30rpx;
}

.before-w-6rpx::before {
    width: 6rpx;
}

.after-h-_bl_200_p__br_::after {
    height: 200%;
}

.after-h-0::after {
    height: 0;
}

.after-h-1px::after {
    height: 1px;
}

.after-h-30rpx::after {
    height: 30rpx;
}

.after-h-4rpx::after {
    height: 4rpx;
}

.after-w-_bl_100_p__br_::after {
    width: 100%;
}

.after-w-_bl_200_p__br_::after {
    width: 200%;
}

.after-w-0::after {
    width: 0;
}

.after-w-1rpx::after {
    width: 1rpx;
}

.after-w-30rpx::after {
    width: 30rpx;
}

.after_c_w-100_p_::after {
    width: 100%;
}

.not-last-after-h-1px:not(:last-child)::after {
    height: 1px;
}

.not-last-after-w-_bl_200_p__br_:not(:last-child)::after {
    width: 200%;
}

.not-last-after-w-80rpx:not(:last-child)::after {
    width: 80rpx;
}

.flex {
    display: flex;
}

.inline-flex {
    display: inline-flex;
}

.flex-_bl_0_0_100rpx_br_ {
    flex: 0 0 100rpx;
}

.flex-_bl_0_0_120rpx_br_ {
    flex: 0 0 120rpx;
}

.flex-_bl_0_0_130rpx_br_ {
    flex: 0 0 130rpx;
}

.flex-_bl_0_0_140rpx_br_ {
    flex: 0 0 140rpx;
}

.flex-_bl_0_0_184rpx_br_ {
    flex: 0 0 184rpx;
}

.flex-_bl_0_0_216rpx_br_ {
    flex: 0 0 216rpx;
}

.flex-_bl_0_0_32rpx_br_ {
    flex: 0 0 32rpx;
}

.flex-_bl_0_0_33_d_3333333_p__br_ {
    flex: 0 0 33.3333333%;
}

.flex-_bl_0_0_36rpx_br_ {
    flex: 0 0 36rpx;
}

.flex-_bl_0_0_400rpx_br_ {
    flex: 0 0 400rpx;
}

.flex-_bl_0_0_50_p__br_ {
    flex: 0 0 50%;
}

.flex-_bl_0_0_50rpx_br_ {
    flex: 0 0 50rpx;
}

.flex-_bl_0_0_528rpx_br_ {
    flex: 0 0 528rpx;
}

.flex-_bl_0_0_60rpx_br_ {
    flex: 0 0 60rpx;
}

.flex-_bl_0_0_86rpx_br_ {
    flex: 0 0 86rpx;
}

.flex-_bl_0_0_90rpx_br_ {
    flex: 0 0 90rpx;
}

.flex-_bl_0_0_auto_br_ {
    flex: 0 0 auto;
}

.flex-_bl_0_1_100_p__br_ {
    flex: 0 1 100%;
}

.flex-_bl_0_1_70_p__br_ {
    flex: 0 1 70%;
}

.flex-_bl_0_50_p__br_ {
    flex: 0 50%;
}

.flex-_bl_1_0_auto_br_ {
    flex: 1 0 auto;
}

.flex-_bl_1_1_auto_br_,.flex-auto {
    flex: 1 1 auto;
}

.flex-_bl_1_1_calc_pl__pl_100_p__-_16rpx_pr__s_2_pr__br_ {
    flex: 1 1 calc((100% - 16rpx) / 2);
}

.flex-_bl_14_d_28_p__br_ {
    flex: 14.28%;
}

.flex-_bl_2_br_ {
    flex: 2;
}

.flex-1 {
    flex: 1 1 0%;
}

.flex-none {
    flex: none;
}

.flex-shrink-0,.shrink-0 {
    flex-shrink: 0;
}

.shrink,.shrink-1 {
    flex-shrink: 1;
}

.flex-grow-0,.grow-0 {
    flex-grow: 0;
}

.flex-grow-1,.grow,.grow-1 {
    flex-grow: 1;
}

.flex-grow-2,.grow-2 {
    flex-grow: 2;
}

.basis-_bl_calc_pl_100_p__s_3_pr__br_ {
    flex-basis: calc(100% / 3);
}

.basis-0 {
    flex-basis: 0;
}

.basis-100 {
    flex-basis: 100rpx;
}

.basis-120 {
    flex-basis: 120rpx;
}

.basis-138 {
    flex-basis: 138rpx;
}

.basis-180 {
    flex-basis: 180rpx;
}

.basis-50_p_ {
    flex-basis: 50%;
}

.basis-auto {
    flex-basis: auto;
}

.flex-basis-1_s_3 {
    flex-basis: 33.3333333333%;
}

.flex-basis-190 {
    flex-basis: 190rpx;
}

.flex-row {
    flex-direction: row;
}

.flex-row-reverse {
    flex-direction: row-reverse;
}

.flex-col {
    flex-direction: column;
}

.flex-col-reverse {
    flex-direction: column-reverse;
}

.flex-wrap {
    flex-wrap: wrap;
}

.flex-nowrap {
    flex-wrap: nowrap;
}

.origin-_bl_0_0_br_ {
    transform-origin: 0 0;
}

.origin-_bl_100_p__br_ {
    transform-origin: 100%;
}

.origin-center {
    transform-origin: center;
}

.origin-center-top {
    transform-origin: center top;
}

.origin-left-bottom {
    transform-origin: left bottom;
}

.origin-left-center {
    transform-origin: left center;
}

.origin-left-top {
    transform-origin: left top;
}

.origin-right {
    transform-origin: right;
}

.after-origin-_bl_0_0_br_::after {
    transform-origin: 0 0;
}

.after-origin-left-bottom::after {
    transform-origin: left bottom;
}

.after-origin-lt::after {
    transform-origin: left top;
}

.after-origin-top-left::after {
    transform-origin: top left;
}

.not-last-after-origin-_bl_0_0_br_:not(:last-child)::after {
    transform-origin: 0 0;
}

.not-last-after-origin-left-bottom:not(:last-child)::after {
    transform-origin: left bottom;
}

.translate--1_s_2,.translate--1_s_2--1_s_2 {
    --un-translate-x: -50%;
    --un-translate-y: -50%;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.translate-_bl_-50_p__2c_-30_p__br_ {
    --un-translate-x: -50%;
    --un-translate-y: -30%;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.-translate-x-1_s_2,.-translate-x-2_s_4,.-translate-x-50_p_,.transform-translate-x-_bl_-50_p__br_,.translate-x--1_s_2,.translate-x--50_p_,.translate-x-_bl_-50_p__br_ {
    --un-translate-x: -50%;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.-translate-x-full,.translate-x--100_p_ {
    --un-translate-x: -100%;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.-translate-y-_bl_50_p__br_,.-translate-y-1_s_2,.-translate-y-2_s_4,.transform-translate-y-_bl_-50_p__br_,.translate-y--1_s_2,.translate-y--50_p_,.translate-y-_bl_-50_p__br_ {
    --un-translate-y: -50%;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.-translate-y-40 {
    --un-translate-y: -40rpx;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.-translate-y-full,.translate-y--100_p_ {
    --un-translate-y: -100%;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.translate-x--40rpx {
    --un-translate-x: -40rpx;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.translate-x--40rpx_i_ {
    --un-translate-x: -40rpx !important;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z)) !important;
}

.translate-x-0 {
    --un-translate-x: 0;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.translate-x-0_i_ {
    --un-translate-x: 0 !important;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z)) !important;
}

.translate-x-1_s_2 {
    --un-translate-x: 50%;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.translate-x-10 {
    --un-translate-x: 10rpx;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.translate-x-150rpx {
    --un-translate-x: 150rpx;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.translate-x-40rpx {
    --un-translate-x: 40rpx;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.translate-x-40rpx_i_ {
    --un-translate-x: 40rpx !important;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z)) !important;
}

.translate-y--2_s_5 {
    --un-translate-y: -40%;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.translate-y--20px {
    --un-translate-y: -20px;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.translate-y-_bl_-42_p__br_ {
    --un-translate-y: -42%;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.translate-y-0 {
    --un-translate-y: 0;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.translate-y-100_p_ {
    --un-translate-y: 100%;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.translate-y-50_p_ {
    --un-translate-y: 50%;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.translate-z-0 {
    --un-translate-z: 0;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.rotate--30 {
    --un-rotate-x: 0;
    --un-rotate-y: 0;
    --un-rotate-z: 0;
    --un-rotate: -30deg;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.rotate--45 {
    --un-rotate-x: 0;
    --un-rotate-y: 0;
    --un-rotate-z: 0;
    --un-rotate: -45deg;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.rotate-0 {
    --un-rotate-x: 0;
    --un-rotate-y: 0;
    --un-rotate-z: 0;
    --un-rotate: 0;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.rotate-180 {
    --un-rotate-x: 0;
    --un-rotate-y: 0;
    --un-rotate-z: 0;
    --un-rotate: 180deg;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.rotate-45 {
    --un-rotate-x: 0;
    --un-rotate-y: 0;
    --un-rotate-z: 0;
    --un-rotate: 45deg;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.rotate-90 {
    --un-rotate-x: 0;
    --un-rotate-y: 0;
    --un-rotate-z: 0;
    --un-rotate: 90deg;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.transform-rotate-10 {
    --un-rotate-x: 0;
    --un-rotate-y: 0;
    --un-rotate-z: 0;
    --un-rotate: 10deg;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.transform-rotate-30 {
    --un-rotate-x: 0;
    --un-rotate-y: 0;
    --un-rotate-z: 0;
    --un-rotate: 30deg;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.rotate-x-180 {
    --un-rotate: 0;
    --un-rotate-x: 180deg;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.rotate-y--180 {
    --un-rotate: 0;
    --un-rotate-y: -180deg;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.rotate-y-180 {
    --un-rotate: 0;
    --un-rotate-y: 180deg;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.skew--15 {
    --un-skew-x: -15deg;
    --un-skew-y: -15deg;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.-transform-skew-x-20 {
    --un-skew-x: -20deg;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.scale-0 {
    --un-scale-x: 0;
    --un-scale-y: 0;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.scale-0_d_2 {
    --un-scale-x: 0.002;
    --un-scale-y: 0.002;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.scale-100 {
    --un-scale-x: 1;
    --un-scale-y: 1;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.scale-100_i_ {
    --un-scale-x: 1 !important;
    --un-scale-y: 1 !important;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z)) !important;
}

.scale-50 {
    --un-scale-x: 0.5;
    --un-scale-y: 0.5;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.scale-80 {
    --un-scale-x: 0.8;
    --un-scale-y: 0.8;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.scale-80_i_ {
    --un-scale-x: 0.8 !important;
    --un-scale-y: 0.8 !important;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z)) !important;
}

.after-scale-50::after {
    --un-scale-x: 0.5;
    --un-scale-y: 0.5;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.not-last-after-scale-50:not(:last-child)::after {
    --un-scale-x: 0.5;
    --un-scale-y: 0.5;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.scale-x-50 {
    --un-scale-x: 0.5;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.scale-y-50 {
    --un-scale-y: 0.5;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.transform {
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));
}

.transform-none {
    transform: none;
}

.after-transform-none::after {
    transform: none;
}

@keyframes fade-in {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

.animate-_bl_fade-in_br_,.animate-keyframes-fade-in {
    animation: fade-in;
}

@keyframes appear {
    0% {
        visibility: hidden;
    }

    100% {
        visibility: visible;
    }
}

@keyframes disappear {
    0% {
        visibility: visible;
    }

    100% {
        visibility: hidden;
    }
}

@keyframes fade-in-40 {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 0.4;
    }
}

@keyframes fade-out {
    0% {
        opacity: 1;
    }

    100% {
        opacity: 0;
    }
}

@keyframes shake-horizontal {
    10%, 90% {
        transform: translate3d(-1px, 0, 0);
    }

    20%, 80% {
        transform: translate3d(2px, 0, 0);
    }

    30%, 70% {
        transform: translate3d(-4px, 0, 0);
    }

    40%, 60% {
        transform: translate3d(4px, 0, 0);
    }

    50% {
        transform: translate3d(-4px, 0, 0);
    }
}

@keyframes slide-in-up {
    0% {
        transform: translate3d(0, 100%, 0);
    }

    100% {
        transform: translate3d(0, 0, 0);
    }
}

@keyframes slide-out-bottom {
    0% {
        bottom: 0;
    }

    100% {
        bottom: -100%;
    }
}

@keyframes slide-out-down {
    0% {
        transform: translate3d(0, 0, 0);
    }

    100% {
        transform: translate3d(0, 100%, 0);
    }
}

@keyframes tada {
    from {
        transform: scale3d(1, 1, 1);
    }

    10%, 20% {
        transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
    }

    30%, 50%, 70%, 90% {
        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    }

    40%, 60%, 80% {
        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    }

    to {
        transform: scale3d(1, 1, 1);
    }
}

.animate-_bl_800ms_br_ {
    animation: 800ms;
}

.animate-_bl_appear_br_ {
    animation: appear;
}

.animate-_bl_content-scroll-left_br_ {
    animation: content-scroll-left;
}

.animate-_bl_fade-in-50_0_d_4s_0_d_2s_ease_normal_br_ {
    animation: fade-in-50 0.4s 0.2s ease normal;
}

.animate-_bl_fade-in-80_0_d_4s_ease_0_d_2s_normal_br_ {
    animation: fade-in-80 0.4s ease 0.2s normal;
}

.animate-_bl_fade-in-down_br_ {
    animation: fade-in-down;
}

.animate-_bl_fade-in-up_br_ {
    animation: fade-in-up;
}

.animate-_bl_fade-out-up_br_ {
    animation: fade-out-up;
}

.animate-_bl_fade-out_br_ {
    animation: fade-out;
}

.animate-_bl_flash-across_br_ {
    animation: flash-across;
}

.animate-_bl_flip-back_br_ {
    animation: flip-back;
}

.animate-_bl_pop-in_br_ {
    animation: pop-in;
}

.animate-_bl_reveal-flip_br_ {
    animation: reveal-flip;
}

.animate-_bl_rise-step_br_ {
    animation: rise-step;
}

.animate-_bl_scale-fade-out_br_ {
    animation: scale-fade-out;
}

.animate-_bl_shade-in_br_ {
    animation: shade-in;
}

.animate-_bl_shade-out_br_ {
    animation: shade-out;
}

.animate-_bl_shake-horizontal_br_ {
    animation: shake-horizontal;
}

.animate-_bl_slide-in-from-top_0_d_4s_0_d_2s_ease_normal_br_ {
    animation: slide-in-from-top 0.4s 0.2s ease normal;
}

.animate-_bl_slide-in-from-top_0_d_4s_ease_0_d_2s_normal_br_ {
    animation: slide-in-from-top 0.4s ease 0.2s normal;
}

.animate-_bl_slide-in-up_br_ {
    animation: slide-in-up;
}

.animate-_bl_slide-out-down_br_ {
    animation: slide-out-down;
}

.animate-_bl_spin_br_ {
    animation: spin;
}

.animate-appear {
    animation: appear 1s linear 1;
}

.animate-disappear {
    animation: disappear 1s linear 1;
}

.animate-fade-in {
    animation: fade-in 1s linear 1;
}

.animate-fade-in-40 {
    animation: fade-in-40 1s linear 1;
}

.animate-fade-out {
    animation: fade-out 1s linear 1;
}

.animate-shake-horizontal {
    animation: shake-horizontal 1s linear 1;
}

.animate-slide-in-up {
    animation: slide-in-up 1s linear 1;
}

.animate-slide-out-bottom {
    animation: slide-out-bottom 1s linear 1;
}

.animate-slide-out-down {
    animation: slide-out-down 1s linear 1;
}

.animate-tada {
    animation: tada 1s linear 1;
}

.animate-name-_bl_fade-out_br_ {
    animation-name: fade-out;
}

.animate-name-_bl_none_br_ {
    animation-name: none;
}

.animate-name-_bl_order-collapse-2_br_ {
    animation-name: order-collapse-2;
}

.animate-name-_bl_order-collapse_br_ {
    animation-name: order-collapse;
}

.animate-name-_bl_order-expand-2_br_ {
    animation-name: order-expand-2;
}

.animate-name-_bl_order-expand_br_ {
    animation-name: order-expand;
}

.animate-name-_bl_slide-in-up_br_ {
    animation-name: slide-in-up;
}

.animate-name-_bl_slide-out-down-and-hide_br_ {
    animation-name: slide-out-down-and-hide;
}

.animate-name-_bl_tada_br_ {
    animation-name: tada;
}

.animate-name-fade-in-80 {
    animation-name: fade-in-80;
}

.animate-name-slide-in-bottom {
    animation-name: slide-in-bottom;
}

.animate-name-slide-in-from-top {
    animation-name: slide-in-from-top;
}

.animate-duration-0_d_1s {
    animation-duration: 0.1s;
}

.animate-duration-0_d_3s {
    animation-duration: 0.3s;
}

.animate-duration-0_d_4s {
    animation-duration: 0.4s;
}

.animate-duration-0s {
    animation-duration: 0s;
}

.animate-duration-12s {
    animation-duration: 12s;
}

.animate-duration-1s {
    animation-duration: 1s;
}

.animate-duration-200ms {
    animation-duration: 200ms;
}

.animate-duration-300,.animate-duration-300ms {
    animation-duration: 300ms;
}

.animate-duration-400,.animate-duration-400ms {
    animation-duration: 400ms;
}

.animate-duration-4s {
    animation-duration: 4s;
}

.animate-duration-800ms {
    animation-duration: 800ms;
}

.animate-duration-8s {
    animation-duration: 8s;
}

.animate-delay-0 {
    animation-delay: 0s;
}

.animate-delay-1 {
    animation-delay: 1ms;
}

.animate-delay-1s {
    animation-delay: 1s;
}

.animate-delay-200 {
    animation-delay: 200ms;
}

.animate-delay-2s {
    animation-delay: 2s;
}

.animate-ease,.animate-ease-in-out {
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-ease-_bl_cubic-bezier_pl_0_d_68_2c_-0_d_55_2c_0_d_27_2c_1_d_55_pr__br_ {
    animation-timing-function: cubic-bezier(0.68,-0.55,0.27,1.55);
}

.animate-ease-_bl_cubic-bezier_pl_1_2c_0_2c_0_2c_1_pr__br_ {
    animation-timing-function: cubic-bezier(1,0,0,1);
}

.animate-ease-_bl_ease-in-out_br_ {
    animation-timing-function: ease-in-out;
}

.animate-ease-_bl_linear_br_ {
    animation-timing-function: linear;
}

.animate-fill-both {
    animation-fill-mode: both;
}

.animate-fill-forwards,.animate-forwards {
    animation-fill-mode: forwards;
}

.animate-direction-normal,.animate-normal {
    animation-direction: normal;
}

.animate-reverse {
    animation-direction: reverse;
}

.animate-count-infinite {
    animation-iteration-count: infinite;
}

.cursor-pointer {
    cursor: pointer;
}

.touch-auto {
    touch-action: auto;
}

.select-none {
    -webkit-user-select: none;
    user-select: none;
}

.content-center {
    align-content: center;
}

.content-start {
    align-content: flex-start;
}

.content-between {
    align-content: space-between;
}

.items-start {
    align-items: flex-start;
}

.items-start_i_ {
    align-items: flex-start !important;
}

.items-end {
    align-items: flex-end;
}

.flex-items-center,.items-center {
    align-items: center;
}

.items-baseline {
    align-items: baseline;
}

.items-stretch {
    align-items: stretch;
}

.self-start {
    align-self: flex-start;
}

.self-end {
    align-self: flex-end;
}

.self-center {
    align-self: center;
}

.justify-start {
    justify-content: flex-start;
}

.justify-end {
    justify-content: flex-end;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.justify-between_i_ {
    justify-content: space-between !important;
}

.flex-justify-around,.justify-around {
    justify-content: space-around;
}

.justify-evenly {
    justify-content: space-evenly;
}

.justify-initial {
    justify-content: initial;
}

.justify-items-center {
    justify-items: center;
}

.gap-16 {
    gap: 16rpx;
}

.gap-20 {
    gap: 20rpx;
}

.gap-24 {
    gap: 24rpx;
}

.gap-26 {
    gap: 26rpx;
}

.gap-8 {
    gap: 8rpx;
}

.gap-x-16 {
    column-gap: 16rpx;
}

.gap-y-40 {
    row-gap: 40rpx;
}

.overflow-auto {
    overflow: auto;
}

.overflow-hidden {
    overflow: hidden;
}

.overflow-scroll {
    overflow: scroll;
}

.overflow-visible {
    overflow: visible;
}

.overflow-x-auto {
    overflow-x: auto;
}

.overflow-x-hidden {
    overflow-x: hidden;
}

.overflow-x-scroll {
    overflow-x: scroll;
}

.overflow-y-auto {
    overflow-y: auto;
}

.overflow-y-hidden {
    overflow-y: hidden;
}

.overflow-y-scroll {
    overflow-y: scroll;
}

.overflow-y-visible {
    overflow-y: visible;
}

.truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.text-ellipsis {
    text-overflow: ellipsis;
}

.whitespace-normal {
    white-space: normal;
}

.whitespace-nowrap {
    white-space: nowrap;
}

.whitespace-pre-line {
    white-space: pre-line;
}

.whitespace-pre-wrap {
    white-space: pre-wrap;
}

.break-all {
    word-break: break-all;
}

.b,.b-1,.b-1px,.border,.border-1,.border-1px {
    border-width: 1px;
}

.b-0,.border-0 {
    border-width: 0px;
}

.b-10rpx,.border-10rpx {
    border-width: 10rpx;
}

.b-1rpx,.border-1rpx {
    border-width: 1rpx;
}

.b-2,.border-2,.border-2px {
    border-width: 2px;
}

.b-2rpx,.border-_bl_2rpx_br_,.border-2rpx {
    border-width: 2rpx;
}

.b-3,.b-width-3,.border-3 {
    border-width: 3px;
}

.b-6 {
    border-width: 6px;
}

.border-_bl_18rpx_br_ {
    border-width: 18rpx;
}

.border-_bl_3rpx_br_,.border-3rpx {
    border-width: 3rpx;
}

.border-0_d_5px {
    border-width: 0.5px;
}

.border-10 {
    border-width: 10px;
}

.border-12rpx {
    border-width: 12rpx;
}

.border-4rpx {
    border-width: 4rpx;
}

.border-6rpx {
    border-width: 6rpx;
}

.active-border:active {
    border-width: 1px;
}

.first-border-0:first-child {
    border-width: 0px;
}

.last-border-0:last-child {
    border-width: 0px;
}

.before-border-4rpx::before {
    border-width: 4rpx;
}

.after-border-0::after {
    border-width: 0px;
}

.after-border-1px::after {
    border-width: 1px;
}

.after-border-1rpx::after {
    border-width: 1rpx;
}

._i_border-r {
    border-right-width: 1px !important;
}

._bl__u__c_nth-last-of-type_pl_1_pr__br__c_border-r-0:nth-last-of-type(1),.border-r-0 {
    border-right-width: 0px;
}

.b-l-2rpx,.border-l-2rpx {
    border-left-width: 2rpx;
}

.b-r,.b-r-1,.border-r,.border-r-1px {
    border-right-width: 1px;
}

.b-t,.b-t-1,.b-t-1px,.border-t,.border-t-1,.border-t-1px {
    border-top-width: 1px;
}

.border-b,.border-b-1,.border-b-1px,.not-last-border-b-1px:not(:last-child) {
    border-bottom-width: 1px;
}

.border-b-0 {
    border-bottom-width: 0px;
}

.border-b-10rpx {
    border-bottom-width: 10rpx;
}

.border-b-12rpx {
    border-bottom-width: 12rpx;
}

.border-b-15rpx {
    border-bottom-width: 15rpx;
}

.border-b-16rpx {
    border-bottom-width: 16rpx;
}

.border-b-1rpx {
    border-bottom-width: 1rpx;
}

.border-b-2 {
    border-bottom-width: 2px;
}

.border-b-20rpx {
    border-bottom-width: 20rpx;
}

.border-b-2rpx {
    border-bottom-width: 2rpx;
}

.border-b-3rpx {
    border-bottom-width: 3rpx;
}

.border-b-4,.border-b-4px {
    border-bottom-width: 4px;
}

.border-b-4rpx {
    border-bottom-width: 4rpx;
}

.border-b-6rpx {
    border-bottom-width: 6rpx;
}

.border-b-8rpx {
    border-bottom-width: 8rpx;
}

.border-l-0 {
    border-left-width: 0px;
}

.border-l-10rpx {
    border-left-width: 10rpx;
}

.border-l-12rpx {
    border-left-width: 12rpx;
}

.border-l-18rpx {
    border-left-width: 18rpx;
}

.border-l-1px {
    border-left-width: 1px;
}

.border-l-1rpx {
    border-left-width: 1rpx;
}

.border-l-20rpx {
    border-left-width: 20rpx;
}

.border-l-4px {
    border-left-width: 4px;
}

.border-l-4rpx {
    border-left-width: 4rpx;
}

.border-l-6rpx {
    border-left-width: 6rpx;
}

.border-r-_bl_1rpx_br_,.border-r-1rpx {
    border-right-width: 1rpx;
}

.border-r-0_d_5px {
    border-right-width: 0.5px;
}

.border-r-10rpx {
    border-right-width: 10rpx;
}

.border-r-12rpx {
    border-right-width: 12rpx;
}

.border-r-15rpx {
    border-right-width: 15rpx;
}

.border-r-16rpx {
    border-right-width: 16rpx;
}

.border-r-18rpx {
    border-right-width: 18rpx;
}

.border-r-20rpx {
    border-right-width: 20rpx;
}

.border-r-2rpx {
    border-right-width: 2rpx;
}

.border-r-4px {
    border-right-width: 4px;
}

.border-r-58rpx {
    border-right-width: 58rpx;
}

.border-r-6rpx {
    border-right-width: 6rpx;
}

.border-t-0 {
    border-top-width: 0px;
}

.border-t-0_d_5px {
    border-top-width: 0.5px;
}

.border-t-10rpx {
    border-top-width: 10rpx;
}

.border-t-16rpx {
    border-top-width: 16rpx;
}

.border-t-18rpx {
    border-top-width: 18rpx;
}

.border-t-1rpx {
    border-top-width: 1rpx;
}

.border-t-20rpx {
    border-top-width: 20rpx;
}

.border-t-2rpx {
    border-top-width: 2rpx;
}

.border-t-30rpx {
    border-top-width: 30rpx;
}

.border-t-58rpx {
    border-top-width: 58rpx;
}

.border-t-6rpx {
    border-top-width: 6rpx;
}

.border-t-8rpx {
    border-top-width: 8rpx;
}

.last-b-l-1px:last-child {
    border-left-width: 1px;
}

.last-border-b-0:last-child {
    border-bottom-width: 0px;
}

.after-border-b-1px::after {
    border-bottom-width: 1px;
}

.not-last-after-border-b-1px:not(:last-child)::after {
    border-bottom-width: 1px;
}

._i_border-_bl__h_E5E5E5_br_ {
    --un-border-opacity: 1 !important;
    border-color: rgba(229, 229, 229, var(--un-border-opacity)) !important;
}

._i_border-primary {
    border-color: var(--std-primary-color) !important;
}

.b-_h_d2d2d2,.border-_bl__h_d2d2d2_br_,.border-_bl_rgb_pl_210_2c_210_2c_210_pr__br_,.border-hex-d2d2d2 {
    --un-border-opacity: 1;
    border-color: rgba(210, 210, 210, var(--un-border-opacity));
}

.b-_h_e1e1e1,.border-_bl__h_e1e1e1_br_,.border-_bl__h_E1E1E1_br_,.border-hex-e1e1e1 {
    --un-border-opacity: 1;
    border-color: rgba(225, 225, 225, var(--un-border-opacity));
}

.b-_h_eee,.b-hex-eeeeee,.border-_bl__h_eee_br_,.border-_bl__h_EEE_br_,.border-_bl__h_eeeeee_br_,.border-_h_eee,.border-hex-eee,.border-hex-EEEEEE {
    --un-border-opacity: 1;
    border-color: rgba(238, 238, 238, var(--un-border-opacity));
}

.b-_h_fff,.b-hex-fff,.b-white,.border-_bl__h_fff_br_,.border-_bl__h_FFF_br_,.border-_bl__h_ffffff_br_,.border-_bl__h_FFFFFF_br_,.border-color-white,.border-hex-ffffff,.border-white {
    --un-border-opacity: 1;
    border-color: rgba(255, 255, 255, var(--un-border-opacity));
}

.b-color-primary,.b-primary,.border-_bl_var_pl_--std-primary-color_pr__br_,.border-color-primary,.border-primary {
    border-color: var(--std-primary-color);
}

.b-hex-222,.border-hex-222 {
    --un-border-opacity: 1;
    border-color: rgba(34, 34, 34, var(--un-border-opacity));
}

.b-hex-40ba5a {
    --un-border-opacity: 1;
    border-color: rgba(64, 186, 90, var(--un-border-opacity));
}

.b-hex-979797,.border-_bl__h_979797_br_ {
    --un-border-opacity: 1;
    border-color: rgba(151, 151, 151, var(--un-border-opacity));
}

.b-hex-c5c5c5,.border-_bl__h_c5c5c5_br_,.border-_bl__h_C5C5C5_br_ {
    --un-border-opacity: 1;
    border-color: rgba(197, 197, 197, var(--un-border-opacity));
}

.b-hex-c6c6c6,.border-_bl_rgb_pl_198_2c_198_2c_198_pr__br_ {
    --un-border-opacity: 1;
    border-color: rgba(198, 198, 198, var(--un-border-opacity));
}

.b-hex-ccc,.b-hex-cccccc,.border-_bl__h_ccc_br_ {
    --un-border-opacity: 1;
    border-color: rgba(204, 204, 204, var(--un-border-opacity));
}

.b-hex-dbdbdb {
    --un-border-opacity: 1;
    border-color: rgba(219, 219, 219, var(--un-border-opacity));
}

.b-hex-ddd,.border-_bl__h_ddd_br_,.border-color-hex-ddd,.border-hex-dddddd {
    --un-border-opacity: 1;
    border-color: rgba(221, 221, 221, var(--un-border-opacity));
}

.b-hex-f93a4a,.border-_bl__h_f93a4a_br_,.border-_bl__h_F93A4A_br_ {
    --un-border-opacity: 1;
    border-color: rgba(249, 58, 74, var(--un-border-opacity));
}

.b-hex-fd2c2d {
    --un-border-opacity: 1;
    border-color: rgba(253, 44, 45, var(--un-border-opacity));
}

.b-hex-FF8F0F {
    --un-border-opacity: 1;
    border-color: rgba(255, 143, 15, var(--un-border-opacity));
}

.b-transparent,.border-transparent {
    border-color: transparent;
}

.border-_bl__h_00A47C_br_ {
    --un-border-opacity: 1;
    border-color: rgba(0, 164, 124, var(--un-border-opacity));
}

.border-_bl__h_06CF6E_br_ {
    --un-border-opacity: 1;
    border-color: rgba(6, 207, 110, var(--un-border-opacity));
}

.border-_bl__h_242120_br_ {
    --un-border-opacity: 1;
    border-color: rgba(36, 33, 32, var(--un-border-opacity));
}

.border-_bl__h_737373_br__i_ {
    --un-border-opacity: 1 !important;
    border-color: rgba(115, 115, 115, var(--un-border-opacity)) !important;
}

.border-_bl__h_999_br_,.border-_bl__h_999999_br_ {
    --un-border-opacity: 1;
    border-color: rgba(153, 153, 153, var(--un-border-opacity));
}

.border-_bl__h_b5b5b5_br_ {
    --un-border-opacity: 1;
    border-color: rgba(181, 181, 181, var(--un-border-opacity));
}

.border-_bl__h_bbb_br_,.border-_bl__h_bbbbbb_br_ {
    --un-border-opacity: 1;
    border-color: rgba(187, 187, 187, var(--un-border-opacity));
}

.border-_bl__h_c1c4c9_br_ {
    --un-border-opacity: 1;
    border-color: rgba(193, 196, 201, var(--un-border-opacity));
}

.border-_bl__h_c9c9c9_br_ {
    --un-border-opacity: 1;
    border-color: rgba(201, 201, 201, var(--un-border-opacity));
}

.border-_bl__h_cccccc_br__i_ {
    --un-border-opacity: 1 !important;
    border-color: rgba(204, 204, 204, var(--un-border-opacity)) !important;
}

.border-_bl__h_cfcfcf_br_,.border-hex-cfcfcf {
    --un-border-opacity: 1;
    border-color: rgba(207, 207, 207, var(--un-border-opacity));
}

.border-_bl__h_d3d3d3_br_,.border-_h_d3d3d3 {
    --un-border-opacity: 1;
    border-color: rgba(211, 211, 211, var(--un-border-opacity));
}

.border-_bl__h_d4d4d4_br_ {
    --un-border-opacity: 1;
    border-color: rgba(212, 212, 212, var(--un-border-opacity));
}

.border-_bl__h_d8d8d8_br_,.border-hex-d8d8d8 {
    --un-border-opacity: 1;
    border-color: rgba(216, 216, 216, var(--un-border-opacity));
}

.border-_bl__h_dcdcdc_br_ {
    --un-border-opacity: 1;
    border-color: rgba(220, 220, 220, var(--un-border-opacity));
}

.border-_bl__h_dedede_br_,.border-hex-dedede {
    --un-border-opacity: 1;
    border-color: rgba(222, 222, 222, var(--un-border-opacity));
}

.border-_bl__h_e5e5e5_br_,.border-_bl__h_E5E5E5_br_,.border-_bl_rgb_pl_229_2c_229_2c_229_pr__br_,.border-_h_e5e5e5,.border-hex-e5e5e5 {
    --un-border-opacity: 1;
    border-color: rgba(229, 229, 229, var(--un-border-opacity));
}

.border-_bl__h_e64340_br_ {
    --un-border-opacity: 1;
    border-color: rgba(230, 67, 64, var(--un-border-opacity));
}

.border-_bl__h_e6e6e6_br_ {
    --un-border-opacity: 1;
    border-color: rgba(230, 230, 230, var(--un-border-opacity));
}

.border-_bl__h_e84015_br_ {
    --un-border-opacity: 1;
    border-color: rgba(232, 64, 21, var(--un-border-opacity));
}

.border-_bl__h_eae9e9_br_ {
    --un-border-opacity: 1;
    border-color: rgba(234, 233, 233, var(--un-border-opacity));
}

.border-_bl__h_eaeaea_br_ {
    --un-border-opacity: 1;
    border-color: rgba(234, 234, 234, var(--un-border-opacity));
}

.border-_bl__h_efefef_br_ {
    --un-border-opacity: 1;
    border-color: rgba(239, 239, 239, var(--un-border-opacity));
}

.border-_bl__h_F47B24_br_ {
    --un-border-opacity: 1;
    border-color: rgba(244, 123, 36, var(--un-border-opacity));
}

.border-_bl__h_f4f4f4_br_ {
    --un-border-opacity: 1;
    border-color: rgba(244, 244, 244, var(--un-border-opacity));
}

.border-_bl__h_f56c6c_br_ {
    --un-border-opacity: 1;
    border-color: rgba(245, 108, 108, var(--un-border-opacity));
}

.border-_bl__h_f5f5f5_br_,.border-_bl__h_F5F5F5_br_,.border-hex-f5f5f5 {
    --un-border-opacity: 1;
    border-color: rgba(245, 245, 245, var(--un-border-opacity));
}

.border-_bl__h_F6F6F6_br_ {
    --un-border-opacity: 1;
    border-color: rgba(246, 246, 246, var(--un-border-opacity));
}

.border-_bl__h_f84036_br_ {
    --un-border-opacity: 1;
    border-color: rgba(248, 64, 54, var(--un-border-opacity));
}

.border-_bl__h_fc4930_br_ {
    --un-border-opacity: 1;
    border-color: rgba(252, 73, 48, var(--un-border-opacity));
}

.border-_bl__h_fe3646_br_ {
    --un-border-opacity: 1;
    border-color: rgba(254, 54, 70, var(--un-border-opacity));
}

.border-_bl__h_ff2233_br_ {
    --un-border-opacity: 1;
    border-color: rgba(255, 34, 51, var(--un-border-opacity));
}

.border-_bl__h_ff5e4e_br_ {
    --un-border-opacity: 1;
    border-color: rgba(255, 94, 78, var(--un-border-opacity));
}

.border-_bl__h_FF8800_br_ {
    --un-border-opacity: 1;
    border-color: rgba(255, 136, 0, var(--un-border-opacity));
}

.border-_bl__h_ffcc30_br_ {
    --un-border-opacity: 1;
    border-color: rgba(255, 204, 48, var(--un-border-opacity));
}

.border-_bl_rgb_pl_255_2c_0_2c_0_pr__br_ {
    --un-border-opacity: 1;
    border-color: rgba(255, 0, 0, var(--un-border-opacity));
}

.border-_bl_rgba_pl_204_2c_204_2c_204_2c_0_d_8_pr__br_ {
    --un-border-opacity: 0.8;
    border-color: rgba(204, 204, 204, var(--un-border-opacity));
}

.border-_bl_rgba_pl_255_2c_233_2c_150_2c_0_d_3_pr__br_ {
    --un-border-opacity: 0.3;
    border-color: rgba(255, 233, 150, var(--un-border-opacity));
}

.border-_bl_var_pl_--color-theme_pr__br_ {
    border-color: var(--color-theme);
}

.border-_bl_var_pl_--std-primary-color_2c__h_00a47c_pr__br_ {
    border-color: var(--std-primary-color,#00a47c);
}

.border-_bl_var_pl_--theme-color_pr__br_ {
    border-color: var(--theme-color);
}

.border-_h_F53F3F {
    --un-border-opacity: 1;
    border-color: rgba(245, 63, 63, var(--un-border-opacity));
}

.border-black {
    --un-border-opacity: 1;
    border-color: rgba(0, 0, 0, var(--un-border-opacity));
}

.border-hex-ababab {
    --un-border-opacity: 1;
    border-color: rgba(171, 171, 171, var(--un-border-opacity));
}

.border-hex-ae965a {
    --un-border-opacity: 1;
    border-color: rgba(174, 150, 90, var(--un-border-opacity));
}

.border-hex-e3e3e3 {
    --un-border-opacity: 1;
    border-color: rgba(227, 227, 227, var(--un-border-opacity));
}

.border-hex-fa3423 {
    --un-border-opacity: 1;
    border-color: rgba(250, 52, 35, var(--un-border-opacity));
}

.border-hex-fa342333 {
    --un-border-opacity: 0.2;
    border-color: rgba(250, 52, 35, var(--un-border-opacity));
}

.border-primary-opacity-30 {
    border-color: var(--std-primary-color-opacity-30);
}

.before-border-_bl__h_fff_br_::before {
    --un-border-opacity: 1;
    border-color: rgba(255, 255, 255, var(--un-border-opacity));
}

.after-border-_bl__h_d2d2d2_br_::after {
    --un-border-opacity: 1;
    border-color: rgba(210, 210, 210, var(--un-border-opacity));
}

.after-border-_bl__h_ffb494_br_::after {
    --un-border-opacity: 1;
    border-color: rgba(255, 180, 148, var(--un-border-opacity));
}

.after_c_b-hex-ccc::after {
    --un-border-opacity: 1;
    border-color: rgba(204, 204, 204, var(--un-border-opacity));
}

.after_c_b-hex-eee::after {
    --un-border-opacity: 1;
    border-color: rgba(238, 238, 238, var(--un-border-opacity));
}

.border-x-transparent {
    border-left-color: transparent;
    border-right-color: transparent;
}

.b-b-_h_000,.border-b-_bl__h_000_br_ {
    --un-border-opacity: 1;
    --un-border-bottom-opacity: var(--un-border-opacity);
    border-bottom-color: rgba(0, 0, 0, var(--un-border-bottom-opacity));
}

.b-l-_h_d2d2d2 {
    --un-border-opacity: 1;
    --un-border-left-opacity: var(--un-border-opacity);
    border-left-color: rgba(210, 210, 210, var(--un-border-left-opacity));
}

.b-r-_h_f5f5f5,.border-r-_bl__h_f5f5f5_br_ {
    --un-border-opacity: 1;
    --un-border-right-opacity: var(--un-border-opacity);
    border-right-color: rgba(245, 245, 245, var(--un-border-right-opacity));
}

.b-t-_h_f5f5f5,.border-t-_bl__h_f5f5f5_br_ {
    --un-border-opacity: 1;
    --un-border-top-opacity: var(--un-border-opacity);
    border-top-color: rgba(245, 245, 245, var(--un-border-top-opacity));
}

.b-t-hex-E1E1E1,.border-t-_bl__h_e1e1e1_br_ {
    --un-border-opacity: 1;
    --un-border-top-opacity: var(--un-border-opacity);
    border-top-color: rgba(225, 225, 225, var(--un-border-top-opacity));
}

.b-t-hex-e5e5e5,.border-t-_bl__h_e5e5e5_br_,.border-t-_bl__h_E5E5E5_br_,.border-t-hex-e5e5e5 {
    --un-border-opacity: 1;
    --un-border-top-opacity: var(--un-border-opacity);
    border-top-color: rgba(229, 229, 229, var(--un-border-top-opacity));
}

.b-t-hex-eae9e9 {
    --un-border-opacity: 1;
    --un-border-top-opacity: var(--un-border-opacity);
    border-top-color: rgba(234, 233, 233, var(--un-border-top-opacity));
}

.b-t-hex-efefef {
    --un-border-opacity: 1;
    --un-border-top-opacity: var(--un-border-opacity);
    border-top-color: rgba(239, 239, 239, var(--un-border-top-opacity));
}

.border-b-_bl__h_999999_br_ {
    --un-border-opacity: 1;
    --un-border-bottom-opacity: var(--un-border-opacity);
    border-bottom-color: rgba(153, 153, 153, var(--un-border-bottom-opacity));
}

.border-b-_bl__h_c8ccd5_br_ {
    --un-border-opacity: 1;
    --un-border-bottom-opacity: var(--un-border-opacity);
    border-bottom-color: rgba(200, 204, 213, var(--un-border-bottom-opacity));
}

.border-b-_bl__h_ddd_br_ {
    --un-border-opacity: 1;
    --un-border-bottom-opacity: var(--un-border-opacity);
    border-bottom-color: rgba(221, 221, 221, var(--un-border-bottom-opacity));
}

.border-b-_bl__h_e1e1e1_br_ {
    --un-border-opacity: 1;
    --un-border-bottom-opacity: var(--un-border-opacity);
    border-bottom-color: rgba(225, 225, 225, var(--un-border-bottom-opacity));
}

.border-b-_bl__h_e5e5e5_br_,.border-b-hex-e5e5e5 {
    --un-border-opacity: 1;
    --un-border-bottom-opacity: var(--un-border-opacity);
    border-bottom-color: rgba(229, 229, 229, var(--un-border-bottom-opacity));
}

.border-b-_bl__h_ebebeb_br_ {
    --un-border-opacity: 1;
    --un-border-bottom-opacity: var(--un-border-opacity);
    border-bottom-color: rgba(235, 235, 235, var(--un-border-bottom-opacity));
}

.border-b-_bl__h_eee_br_,.border-b-_bl__h_eeeeee_br_,.border-b-hex-eee,.not-last-border-b-_bl__h_eee_br_:not(:last-child),.not-last-border-b-_bl__h_eeeeee_br_:not(:last-child) {
    --un-border-opacity: 1;
    --un-border-bottom-opacity: var(--un-border-opacity);
    border-bottom-color: rgba(238, 238, 238, var(--un-border-bottom-opacity));
}

.border-b-_bl__h_efefef_br_,.not-last-border-b-_bl__h_efefef_br_:not(:last-child) {
    --un-border-opacity: 1;
    --un-border-bottom-opacity: var(--un-border-opacity);
    border-bottom-color: rgba(239, 239, 239, var(--un-border-bottom-opacity));
}

.border-b-_bl__h_F0F0F0_br_ {
    --un-border-opacity: 1;
    --un-border-bottom-opacity: var(--un-border-opacity);
    border-bottom-color: rgba(240, 240, 240, var(--un-border-bottom-opacity));
}

.border-b-_bl__h_f2f2f2_br_ {
    --un-border-opacity: 1;
    --un-border-bottom-opacity: var(--un-border-opacity);
    border-bottom-color: rgba(242, 242, 242, var(--un-border-bottom-opacity));
}

.border-b-_bl__h_f7f7f7_br_ {
    --un-border-opacity: 1;
    --un-border-bottom-opacity: var(--un-border-opacity);
    border-bottom-color: rgba(247, 247, 247, var(--un-border-bottom-opacity));
}

.border-b-_bl__h_fff_br_,.border-b-_bl__h_ffffff_br_,.border-b-hex-fff {
    --un-border-opacity: 1;
    --un-border-bottom-opacity: var(--un-border-opacity);
    border-bottom-color: rgba(255, 255, 255, var(--un-border-bottom-opacity));
}

.border-b-_bl_rgba_pl_0_2c_0_2c_0_2c_0_d_8_pr__br_ {
    --un-border-opacity: 0.8;
    --un-border-bottom-opacity: var(--un-border-opacity);
    border-bottom-color: rgba(0, 0, 0, var(--un-border-bottom-opacity));
}

.border-b-_bl_var_pl_--light-theme-color_pr__br_ {
    border-bottom-color: var(--light-theme-color);
}

.border-b-hex-ebedf0 {
    --un-border-opacity: 1;
    --un-border-bottom-opacity: var(--un-border-opacity);
    border-bottom-color: rgba(235, 237, 240, var(--un-border-bottom-opacity));
}

.border-b-hex-fa3423 {
    --un-border-opacity: 1;
    --un-border-bottom-opacity: var(--un-border-opacity);
    border-bottom-color: rgba(250, 52, 35, var(--un-border-bottom-opacity));
}

.border-b-primary-opacity-10 {
    border-bottom-color: var(--std-primary-color-opacity-10);
}

.border-b-transparent {
    border-bottom-color: transparent;
}

.border-l-_bl__h_cbcbcb_br_ {
    --un-border-opacity: 1;
    --un-border-left-opacity: var(--un-border-opacity);
    border-left-color: rgba(203, 203, 203, var(--un-border-left-opacity));
}

.border-l-_bl__h_dcdcdc_br_ {
    --un-border-opacity: 1;
    --un-border-left-opacity: var(--un-border-opacity);
    border-left-color: rgba(220, 220, 220, var(--un-border-left-opacity));
}

.border-l-_bl__h_e1e1e1_br_ {
    --un-border-opacity: 1;
    --un-border-left-opacity: var(--un-border-opacity);
    border-left-color: rgba(225, 225, 225, var(--un-border-left-opacity));
}

.border-l-_bl__h_e5e5e5_br_ {
    --un-border-opacity: 1;
    --un-border-left-opacity: var(--un-border-opacity);
    border-left-color: rgba(229, 229, 229, var(--un-border-left-opacity));
}

.border-l-hex-d8d8d8 {
    --un-border-opacity: 1;
    --un-border-left-opacity: var(--un-border-opacity);
    border-left-color: rgba(216, 216, 216, var(--un-border-left-opacity));
}

.border-l-transparent {
    border-left-color: transparent;
}

.border-r-_bl__h_d8d8d8_br_ {
    --un-border-opacity: 1;
    --un-border-right-opacity: var(--un-border-opacity);
    border-right-color: rgba(216, 216, 216, var(--un-border-right-opacity));
}

.border-r-_bl__h_dcdcdc_br_ {
    --un-border-opacity: 1;
    --un-border-right-opacity: var(--un-border-opacity);
    border-right-color: rgba(220, 220, 220, var(--un-border-right-opacity));
}

.border-r-_bl__h_e1e1e1_br_ {
    --un-border-opacity: 1;
    --un-border-right-opacity: var(--un-border-opacity);
    border-right-color: rgba(225, 225, 225, var(--un-border-right-opacity));
}

.border-r-_bl__h_e2e5e7_br_ {
    --un-border-opacity: 1;
    --un-border-right-opacity: var(--un-border-opacity);
    border-right-color: rgba(226, 229, 231, var(--un-border-right-opacity));
}

.border-r-_bl__h_e5e5e5_br_ {
    --un-border-opacity: 1;
    --un-border-right-opacity: var(--un-border-opacity);
    border-right-color: rgba(229, 229, 229, var(--un-border-right-opacity));
}

.border-r-_bl__h_eee_br_ {
    --un-border-opacity: 1;
    --un-border-right-opacity: var(--un-border-opacity);
    border-right-color: rgba(238, 238, 238, var(--un-border-right-opacity));
}

.border-r-_bl__h_fff_br_ {
    --un-border-opacity: 1;
    --un-border-right-opacity: var(--un-border-opacity);
    border-right-color: rgba(255, 255, 255, var(--un-border-right-opacity));
}

.border-r-_bl_rgb_pl_210_2c_210_2c_210_pr__br_ {
    --un-border-opacity: 1;
    --un-border-right-opacity: var(--un-border-opacity);
    border-right-color: rgba(210, 210, 210, var(--un-border-right-opacity));
}

.border-r-transparent {
    border-right-color: transparent;
}

.border-t-_bl__h_d8d8d8_br_ {
    --un-border-opacity: 1;
    --un-border-top-opacity: var(--un-border-opacity);
    border-top-color: rgba(216, 216, 216, var(--un-border-top-opacity));
}

.border-t-_bl__h_ebebeb_br_ {
    --un-border-opacity: 1;
    --un-border-top-opacity: var(--un-border-opacity);
    border-top-color: rgba(235, 235, 235, var(--un-border-top-opacity));
}

.border-t-_bl__h_eee_br_,.border-t-_bl__h_eeeeee_br_,.border-t-hex-eee {
    --un-border-opacity: 1;
    --un-border-top-opacity: var(--un-border-opacity);
    border-top-color: rgba(238, 238, 238, var(--un-border-top-opacity));
}

.border-t-_bl__h_f2f2f2_br_ {
    --un-border-opacity: 1;
    --un-border-top-opacity: var(--un-border-opacity);
    border-top-color: rgba(242, 242, 242, var(--un-border-top-opacity));
}

.border-t-_bl__h_f5f8fa_br_ {
    --un-border-opacity: 1;
    --un-border-top-opacity: var(--un-border-opacity);
    border-top-color: rgba(245, 248, 250, var(--un-border-top-opacity));
}

.border-t-_bl__h_f7f7f7_br_ {
    --un-border-opacity: 1;
    --un-border-top-opacity: var(--un-border-opacity);
    border-top-color: rgba(247, 247, 247, var(--un-border-top-opacity));
}

.border-t-_bl__h_fc4930_br_ {
    --un-border-opacity: 1;
    --un-border-top-opacity: var(--un-border-opacity);
    border-top-color: rgba(252, 73, 48, var(--un-border-top-opacity));
}

.border-t-_bl__h_ff5948_br_ {
    --un-border-opacity: 1;
    --un-border-top-opacity: var(--un-border-opacity);
    border-top-color: rgba(255, 89, 72, var(--un-border-top-opacity));
}

.border-t-_bl__h_fff_br_,.border-t-_bl__h_ffffff_br_,.border-t-white {
    --un-border-opacity: 1;
    --un-border-top-opacity: var(--un-border-opacity);
    border-top-color: rgba(255, 255, 255, var(--un-border-top-opacity));
}

.border-t-_bl_rgba_pl_0_2c_0_2c_0_2c_0_d_6_pr__br_ {
    --un-border-opacity: 0.6;
    --un-border-top-opacity: var(--un-border-opacity);
    border-top-color: rgba(0, 0, 0, var(--un-border-top-opacity));
}

.border-t-_bl_rgba_pl_0_2c_0_2c_0_2c_0_d_8_pr__br_ {
    --un-border-opacity: 0.8;
    --un-border-top-opacity: var(--un-border-opacity);
    border-top-color: rgba(0, 0, 0, var(--un-border-top-opacity));
}

.border-t-hex-ebedf0 {
    --un-border-opacity: 1;
    --un-border-top-opacity: var(--un-border-opacity);
    border-top-color: rgba(235, 237, 240, var(--un-border-top-opacity));
}

.border-t-transparent {
    border-top-color: transparent;
}

.last-b-l-hex-e5e5e5:last-child {
    --un-border-opacity: 1;
    --un-border-left-opacity: var(--un-border-opacity);
    border-left-color: rgba(229, 229, 229, var(--un-border-left-opacity));
}

.after-border-b-_bl__h_e5e5e5_br_::after {
    --un-border-opacity: 1;
    --un-border-bottom-opacity: var(--un-border-opacity);
    border-bottom-color: rgba(229, 229, 229, var(--un-border-bottom-opacity));
}

.not-last-after-border-b-_bl__h_ddd_br_:not(:last-child)::after {
    --un-border-opacity: 1;
    --un-border-bottom-opacity: var(--un-border-opacity);
    border-bottom-color: rgba(221, 221, 221, var(--un-border-bottom-opacity));
}

.b-b-op-80 {
    --un-border-bottom-opacity: 0.8;
}

.b-rd-120,.rd-120,.rounded-120 {
    border-radius: 120rpx;
}

.b-rd-23,.border-rd-23rpx {
    border-radius: 23rpx;
}

.b-rd-30,.border-rd-30rpx,.rd-30,.rounded-30,.rounded-30rpx {
    border-radius: 30rpx;
}

.b-rd-44,.border-rd-44rpx,.rd-44,.rounded-44,.rounded-44rpx {
    border-radius: 44rpx;
}

.b-rd-55 {
    border-radius: 55rpx;
}

.b-rd-full,.rd-full,.rounded-full {
    border-radius: 9999px;
}

.border-rd-_bl_0_0_0_10rpx_br_ {
    border-radius: 0 0 0 10rpx;
}

.border-rd-_bl_0_0_0_8rpx_br_ {
    border-radius: 0 0 0 8rpx;
}

.border-rd-_bl_0_0_100_p__br_ {
    border-radius: 0 0 100%;
}

.border-rd-_bl_0_0_10rpx_0_br_ {
    border-radius: 0 0 10rpx 0;
}

.border-rd-_bl_0_0_16rpx_16rpx_br_ {
    border-radius: 0 0 16rpx 16rpx;
}

.border-rd-_bl_0_0_20rpx_20rpx_br_ {
    border-radius: 0 0 20rpx 20rpx;
}

.border-rd-_bl_0_0_30rpx_30rpx_br_ {
    border-radius: 0 0 30rpx 30rpx;
}

.border-rd-_bl_0_0_40rpx_40rpx_br_ {
    border-radius: 0 0 40rpx 40rpx;
}

.border-rd-_bl_0_0_5rpx_0_br_ {
    border-radius: 0 0 5rpx 0;
}

.border-rd-_bl_0_0_8rpx_8rpx_br_ {
    border-radius: 0 0 8rpx 8rpx;
}

.border-rd-_bl_0_100_p__0_0_br_ {
    border-radius: 0 100% 0 0;
}

.border-rd-_bl_0_16rpx_0_100_p__br_ {
    border-radius: 0 16rpx 0 100%;
}

.border-rd-_bl_0_16rpx_0_16rpx_br_ {
    border-radius: 0 16rpx 0 16rpx;
}

.border-rd-_bl_0_16rpx_0_20rpx_br_ {
    border-radius: 0 16rpx 0 20rpx;
}

.border-rd-_bl_0_18rpx_0_18rpx_br_ {
    border-radius: 0 18rpx 0 18rpx;
}

.border-rd-_bl_0_20rpx_br_ {
    border-radius: 0 20rpx;
}

.border-rd-_bl_0_30rpx_0_0_br_ {
    border-radius: 0 30rpx 0 0;
}

.border-rd-_bl_0_4rpx_4rpx_0_br_ {
    border-radius: 0 4rpx 4rpx 0;
}

.border-rd-_bl_0_5rpx_0_15rpx_br_ {
    border-radius: 0 5rpx 0 15rpx;
}

.border-rd-_bl_0_8rpx_8rpx_0_br_ {
    border-radius: 0 8rpx 8rpx 0;
}

.border-rd-_bl_0_8rpx_br_ {
    border-radius: 0 8rpx;
}

.border-rd-_bl_0px_0px_20rpx_20rpx_br_ {
    border-radius: 0px 0px 20rpx 20rpx;
}

.border-rd-_bl_0px_20rpx_0px_20rpx_br_ {
    border-radius: 0px 20rpx 0px 20rpx;
}

.border-rd-_bl_0px_8rpx_0px_8rpx_br_ {
    border-radius: 0px 8rpx 0px 8rpx;
}

.border-rd-_bl_0rpx_0rpx_10rpx_10rpx_br_ {
    border-radius: 0rpx 0rpx 10rpx 10rpx;
}

.border-rd-_bl_0rpx_0rpx_16rpx_16rpx_br_ {
    border-radius: 0rpx 0rpx 16rpx 16rpx;
}

.border-rd-_bl_0rpx_16rpx_0rpx_16rpx_br_ {
    border-radius: 0rpx 16rpx 0rpx 16rpx;
}

.border-rd-_bl_0rpx_17rpx_0px_17rpx_br_ {
    border-radius: 0rpx 17rpx 0px 17rpx;
}

.border-rd-_bl_0rpx_8rpx_0rpx_8rpx_br_ {
    border-radius: 0rpx 8rpx 0rpx 8rpx;
}

.border-rd-_bl_0rpx_8rpx_br_ {
    border-radius: 0rpx 8rpx;
}

.border-rd-_bl_100_p__br_,.rd-_bl_100_p__br_,.rounded-100_p_ {
    border-radius: 100%;
}

.border-rd-_bl_10rpx_10rpx_10rpx_0rpx_br_ {
    border-radius: 10rpx 10rpx 10rpx 0rpx;
}

.border-rd-_bl_10rpx_10rpx_10rpx_2rpx_br_ {
    border-radius: 10rpx 10rpx 10rpx 2rpx;
}

.border-rd-_bl_10rpx_10rpx_10rpx_3rpx_br_ {
    border-radius: 10rpx 10rpx 10rpx 3rpx;
}

.border-rd-_bl_12rpx_0rpx_12rpx_0rpx_br_ {
    border-radius: 12rpx 0rpx 12rpx 0rpx;
}

.border-rd-_bl_12rpx_12rpx_0_0_br_ {
    border-radius: 12rpx 12rpx 0 0;
}

.border-rd-_bl_12rpx_12rpx_12rpx_4rpx_br_ {
    border-radius: 12rpx 12rpx 12rpx 4rpx;
}

.border-rd-_bl_16rpx_0_16rpx_0_br_ {
    border-radius: 16rpx 0 16rpx 0;
}

.border-rd-_bl_16rpx_16rpx_0_0_br_ {
    border-radius: 16rpx 16rpx 0 0;
}

.border-rd-_bl_19rpx_19rpx_0rpx_0rpx_br_ {
    border-radius: 19rpx 19rpx 0rpx 0rpx;
}

.border-rd-_bl_20px_20px_0px_0px_br_ {
    border-radius: 20px 20px 0px 0px;
}

.border-rd-_bl_20rpx_0_0_20rpx_br_ {
    border-radius: 20rpx 0 0 20rpx;
}

.border-rd-_bl_20rpx_0_20rpx_0_br_ {
    border-radius: 20rpx 0 20rpx 0;
}

.border-rd-_bl_20rpx_0rpx_0rpx_20rpx_br_ {
    border-radius: 20rpx 0rpx 0rpx 20rpx;
}

.border-rd-_bl_20rpx_0rpx_20rpx_0rpx_br_ {
    border-radius: 20rpx 0rpx 20rpx 0rpx;
}

.border-rd-_bl_20rpx_20rpx_0_0_br_ {
    border-radius: 20rpx 20rpx 0 0;
}

.border-rd-_bl_20rpx_20rpx_0_0rpx_br_ {
    border-radius: 20rpx 20rpx 0 0rpx;
}

.border-rd-_bl_20rpx_6rpx_br_ {
    border-radius: 20rpx 6rpx;
}

.border-rd-_bl_22rpx_0_0_22rpx_br_ {
    border-radius: 22rpx 0 0 22rpx;
}

.border-rd-_bl_23rpx_0_0_23rpx_br_ {
    border-radius: 23rpx 0 0 23rpx;
}

.border-rd-_bl_24rpx_0_0_24rpx_br_ {
    border-radius: 24rpx 0 0 24rpx;
}

.border-rd-_bl_24rpx_24rpx_0_0_br_ {
    border-radius: 24rpx 24rpx 0 0;
}

.border-rd-_bl_24rpx_24rpx_0rpx_0rpx_br_ {
    border-radius: 24rpx 24rpx 0rpx 0rpx;
}

.border-rd-_bl_24rpx_24rpx_24rpx_24rpx_br_ {
    border-radius: 24rpx 24rpx 24rpx 24rpx;
}

.border-rd-_bl_25rpx_25rpx_25rpx_25rpx_br_ {
    border-radius: 25rpx 25rpx 25rpx 25rpx;
}

.border-rd-_bl_26rpx_0rpx_0rpx_26rpx_br_ {
    border-radius: 26rpx 0rpx 0rpx 26rpx;
}

.border-rd-_bl_29px_0px_0px_29px_br_ {
    border-radius: 29px 0px 0px 29px;
}

.border-rd-_bl_30rpx_30rpx_0_0_br_,.rounded-_bl_30rpx_30rpx_0_0_br_ {
    border-radius: 30rpx 30rpx 0 0;
}

.border-rd-_bl_30rpx_30rpx_0rpx_0rpx_br_ {
    border-radius: 30rpx 30rpx 0rpx 0rpx;
}

.border-rd-_bl_30rpx_30rpx_30rpx_30rpx_br_ {
    border-radius: 30rpx 30rpx 30rpx 30rpx;
}

.border-rd-_bl_32rpx_32rpx_0px_0px_br_ {
    border-radius: 32rpx 32rpx 0px 0px;
}

.border-rd-_bl_41rpx_41rpx_41rpx_41rpx_br_ {
    border-radius: 41rpx 41rpx 41rpx 41rpx;
}

.border-rd-_bl_4rpx_0_0_4rpx_br_ {
    border-radius: 4rpx 0 0 4rpx;
}

.border-rd-_bl_50_p__0_12rpx_0_br_ {
    border-radius: 50% 0 12rpx 0;
}

.border-rd-_bl_50_p__br_,.border-rd-50_p_,.rd-_bl_50_p__br_,.rd-1_s_2,.rd-50_p_,.rounded-_bl_50_p__br_,.rounded-50_p_ {
    border-radius: 50%;
}

.border-rd-_bl_66rpx_66rpx_66rpx_66rpx_br_ {
    border-radius: 66rpx 66rpx 66rpx 66rpx;
}

.border-rd-_bl_6rpx_0_0_6rpx_br_ {
    border-radius: 6rpx 0 0 6rpx;
}

.border-rd-_bl_6rpx_0_12rpx_6rpx_br_ {
    border-radius: 6rpx 0 12rpx 6rpx;
}

.border-rd-_bl_80rpx_80rpx_80rpx_80rpx_br_ {
    border-radius: 80rpx 80rpx 80rpx 80rpx;
}

.border-rd-_bl_88rpx_88rpx_88rpx_88rpx_br_ {
    border-radius: 88rpx 88rpx 88rpx 88rpx;
}

.border-rd-_bl_8rpx_0_0_8rpx_br_ {
    border-radius: 8rpx 0 0 8rpx;
}

.border-rd-_bl_8rpx_0_0_br_ {
    border-radius: 8rpx 0 0;
}

.border-rd-_bl_8rpx_8rpx_0rpx_0rpx_br_ {
    border-radius: 8rpx 8rpx 0rpx 0rpx;
}

.border-rd-_bl_8rpx_8rpx_8rpx_3rpx_br_ {
    border-radius: 8rpx 8rpx 8rpx 3rpx;
}

.border-rd-_bl_8rpx_8rpx_8rpx_8rpx_br_ {
    border-radius: 8rpx 8rpx 8rpx 8rpx;
}

.border-rd-0,.rd-0,.rounded-none {
    border-radius: 0;
}

.border-rd-1000rpx,.rounded-1000 {
    border-radius: 1000rpx;
}

.border-rd-100rpx,.rd-100,.rounded-100rpx {
    border-radius: 100rpx;
}

.border-rd-10px {
    border-radius: 10px;
}

.border-rd-10rpx,.rd-10,.rd-10rpx,.rounded-_bl_10rpx_br_,.rounded-10 {
    border-radius: 10rpx;
}

.border-rd-11rpx {
    border-radius: 11rpx;
}

.border-rd-124rpx {
    border-radius: 124rpx;
}

.border-rd-12px {
    border-radius: 12px;
}

.border-rd-12rpx,.rd-12,.rounded-12,.rounded-12rpx {
    border-radius: 12rpx;
}

.border-rd-13rpx,.rounded-13 {
    border-radius: 13rpx;
}

.border-rd-14rpx,.rd-14,.rounded-14 {
    border-radius: 14rpx;
}

.border-rd-15rpx {
    border-radius: 15rpx;
}

.border-rd-16px {
    border-radius: 16px;
}

.border-rd-16rpx,.rd-16,.rounded-_bl_16rpx_br_,.rounded-16,.rounded-16rpx {
    border-radius: 16rpx;
}

.border-rd-18rpx,.rd-18,.rounded-18 {
    border-radius: 18rpx;
}

.border-rd-19rpx {
    border-radius: 19rpx;
}

.border-rd-1rpx,.rounded-1 {
    border-radius: 1rpx;
}

.border-rd-20px {
    border-radius: 20px;
}

.border-rd-20rpx,.rd-20,.rounded-_bl_20rpx_br_,.rounded-20,.rounded-20rpx {
    border-radius: 20rpx;
}

.border-rd-20rpx_i_ {
    border-radius: 20rpx !important;
}

.border-rd-21rpx,.rounded-21 {
    border-radius: 21rpx;
}

.border-rd-22rpx,.rounded-22 {
    border-radius: 22rpx;
}

.border-rd-24px {
    border-radius: 24px;
}

.border-rd-24rpx,.rd-24,.rd-24rpx,.rounded-24,.rounded-24rpx {
    border-radius: 24rpx;
}

.border-rd-25rpx {
    border-radius: 25rpx;
}

.border-rd-26rpx,.rd-26 {
    border-radius: 26rpx;
}

.border-rd-28rpx,.rd-28 {
    border-radius: 28rpx;
}

.border-rd-2px,.rd-2px {
    border-radius: 2px;
}

.border-rd-2rpx,.rd-2,.rounded-2,.rounded-2rpx {
    border-radius: 2rpx;
}

.border-rd-32rpx {
    border-radius: 32rpx;
}

.border-rd-34rpx,.rd-34 {
    border-radius: 34rpx;
}

.border-rd-35px {
    border-radius: 35px;
}

.border-rd-35rpx {
    border-radius: 35rpx;
}

.border-rd-36rpx,.rd-36,.rounded-36rpx {
    border-radius: 36rpx;
}

.border-rd-37rpx {
    border-radius: 37rpx;
}

.border-rd-38rpx,.rounded-38 {
    border-radius: 38rpx;
}

.border-rd-3px {
    border-radius: 3px;
}

.border-rd-3rpx,.rounded-3 {
    border-radius: 3rpx;
}

.border-rd-40rpx,.rd-40,.rounded-40 {
    border-radius: 40rpx;
}

.border-rd-41rpx {
    border-radius: 41rpx;
}

.border-rd-42rpx,.rd-42 {
    border-radius: 42rpx;
}

.border-rd-44px {
    border-radius: 44px;
}

.border-rd-45rpx,.rd-45 {
    border-radius: 45rpx;
}

.border-rd-46rpx {
    border-radius: 46rpx;
}

.border-rd-4rpx,.rd-4,.rounded-4 {
    border-radius: 4rpx;
}

.border-rd-50rpx,.rd-50,.rounded-50,.rounded-50rpx {
    border-radius: 50rpx;
}

.border-rd-56rpx {
    border-radius: 56rpx;
}

.border-rd-5px {
    border-radius: 5px;
}

.border-rd-5rpx {
    border-radius: 5rpx;
}

.border-rd-60rpx,.rounded-60 {
    border-radius: 60rpx;
}

.border-rd-62rpx {
    border-radius: 62rpx;
}

.border-rd-6rpx,.rd-6,.rounded-6 {
    border-radius: 6rpx;
}

.border-rd-70rpx {
    border-radius: 70rpx;
}

.border-rd-7rpx {
    border-radius: 7rpx;
}

.border-rd-80rpx,.rd-80,.rounded-80rpx {
    border-radius: 80rpx;
}

.border-rd-82rpx {
    border-radius: 82rpx;
}

.border-rd-88rpx,.rounded-88 {
    border-radius: 88rpx;
}

.border-rd-8px {
    border-radius: 8px;
}

.border-rd-8rpx,.rd-8,.rounded-8,.rounded-8rpx {
    border-radius: 8rpx;
}

.border-rd-90rpx {
    border-radius: 90rpx;
}

.border-rd-999rpx,.rd-999 {
    border-radius: 999rpx;
}

.rd-0_i_ {
    border-radius: 0 !important;
}

.rd-31 {
    border-radius: 31rpx;
}

.rd-40_i_ {
    border-radius: 40rpx !important;
}

.rd-48rpx,.rounded-48 {
    border-radius: 48rpx;
}

.rd-8_i_ {
    border-radius: 8rpx !important;
}

.rd-inherit {
    border-radius: inherit;
}

.rounded-_bl_4px_br_ {
    border-radius: 4px;
}

.rounded-_bl_8rpx_0_8rpx_0_br_ {
    border-radius: 8rpx 0 8rpx 0;
}

.rounded-66 {
    border-radius: 66rpx;
}

.rounded-69 {
    border-radius: 69rpx;
}

.before-border-rd-_bl_50_p__br_::before {
    border-radius: 50%;
}

.before-border-rd-3px::before {
    border-radius: 3px;
}

.after-border-rd-0::after {
    border-radius: 0;
}

.after-border-rd-4rpx::after {
    border-radius: 4rpx;
}

.after-border-rd-8rpx::after {
    border-radius: 8rpx;
}

.after-border-rd-inherit::after {
    border-radius: inherit;
}

.rd-b-0 {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.rd-b-10 {
    border-bottom-left-radius: 10rpx;
    border-bottom-right-radius: 10rpx;
}

.rd-b-16 {
    border-bottom-left-radius: 16rpx;
    border-bottom-right-radius: 16rpx;
}

.rd-b-20,.rounded-b-20rpx {
    border-bottom-left-radius: 20rpx;
    border-bottom-right-radius: 20rpx;
}

.rd-b-8 {
    border-bottom-left-radius: 8rpx;
    border-bottom-right-radius: 8rpx;
}

.rd-l-16 {
    border-top-left-radius: 16rpx;
    border-bottom-left-radius: 16rpx;
}

.rd-l-4 {
    border-top-left-radius: 4rpx;
    border-bottom-left-radius: 4rpx;
}

.rd-l-full {
    border-top-left-radius: 9999px;
    border-bottom-left-radius: 9999px;
}

.rd-r-10,.rounded-r-_bl_10rpx_br_ {
    border-top-right-radius: 10rpx;
    border-bottom-right-radius: 10rpx;
}

.rd-r-16 {
    border-top-right-radius: 16rpx;
    border-bottom-right-radius: 16rpx;
}

.rd-r-200 {
    border-top-right-radius: 200rpx;
    border-bottom-right-radius: 200rpx;
}

.rd-r-full {
    border-top-right-radius: 9999px;
    border-bottom-right-radius: 9999px;
}

.rd-t-16 {
    border-top-left-radius: 16rpx;
    border-top-right-radius: 16rpx;
}

.rd-t-20,.rounded-t-20 {
    border-top-left-radius: 20rpx;
    border-top-right-radius: 20rpx;
}

.rd-t-24,.rounded-t-24 {
    border-top-left-radius: 24rpx;
    border-top-right-radius: 24rpx;
}

.rd-t-30,.rounded-t-30rpx {
    border-top-left-radius: 30rpx;
    border-top-right-radius: 30rpx;
}

.rd-t-32,.rounded-t-32rpx {
    border-top-left-radius: 32rpx;
    border-top-right-radius: 32rpx;
}

.rd-t-4 {
    border-top-left-radius: 4rpx;
    border-top-right-radius: 4rpx;
}

.rounded-b-24 {
    border-bottom-left-radius: 24rpx;
    border-bottom-right-radius: 24rpx;
}

.rounded-l-_bl_10rpx_br_ {
    border-top-left-radius: 10rpx;
    border-bottom-left-radius: 10rpx;
}

.border-rd-bl-10rpx,.rounded-lb-10 {
    border-bottom-left-radius: 10rpx;
}

.border-rd-bl-15rpx,.rounded-lb-15 {
    border-bottom-left-radius: 15rpx;
}

.border-rd-br-10rpx {
    border-bottom-right-radius: 10rpx;
}

.border-rd-br-15rpx,.rounded-br-15 {
    border-bottom-right-radius: 15rpx;
}

.border-rd-tl-40rpx {
    border-top-left-radius: 40rpx;
}

.border-rd-tl-46rpx {
    border-top-left-radius: 46rpx;
}

.border-rd-tr-40rpx {
    border-top-right-radius: 40rpx;
}

.border-rd-tr-46rpx {
    border-top-right-radius: 46rpx;
}

.rd-bl-0 {
    border-bottom-left-radius: 0;
}

.rd-bl-16,.rounded-bl-16 {
    border-bottom-left-radius: 16rpx;
}

.rd-bl-24 {
    border-bottom-left-radius: 24rpx;
}

.rd-br-0,.rounded-br-0 {
    border-bottom-right-radius: 0;
}

.rd-br-24 {
    border-bottom-right-radius: 24rpx;
}

.rd-br-8,.rounded-br-_bl_8rpx_br_,.rounded-br-8 {
    border-bottom-right-radius: 8rpx;
}

.rd-lt-20,.rd-tl-20,.rounded-lt-20,.rounded-tl-20 {
    border-top-left-radius: 20rpx;
}

.rd-rb-20 {
    border-bottom-right-radius: 20rpx;
}

.rd-tl-0 {
    border-top-left-radius: 0;
}

.rd-tl-10 {
    border-top-left-radius: 10rpx;
}

.rd-tl-16 {
    border-top-left-radius: 16rpx;
}

.rd-tl-19 {
    border-top-left-radius: 19rpx;
}

.rd-tl-24,.rounded-lt-24,.rounded-tl-24 {
    border-top-left-radius: 24rpx;
}

.rd-tl-30,.rounded-lt-30 {
    border-top-left-radius: 30rpx;
}

.rd-tl-8,.rounded-tl-8 {
    border-top-left-radius: 8rpx;
}

.rd-tr-0,.rounded-tr-0 {
    border-top-right-radius: 0;
}

.rd-tr-16,.rounded-tr-16 {
    border-top-right-radius: 16rpx;
}

.rd-tr-19 {
    border-top-right-radius: 19rpx;
}

.rd-tr-20,.rounded-rt-20 {
    border-top-right-radius: 20rpx;
}

.rd-tr-24,.rounded-rt-24,.rounded-tr-24 {
    border-top-right-radius: 24rpx;
}

.rd-tr-30,.rounded-rt-30 {
    border-top-right-radius: 30rpx;
}

.rd-tr-8 {
    border-top-right-radius: 8rpx;
}

.rounded-bl-_bl_8rpx_br_ {
    border-bottom-left-radius: 8rpx;
}

.rounded-bl-12rpx {
    border-bottom-left-radius: 12rpx;
}

.rounded-bl-20 {
    border-bottom-left-radius: 20rpx;
}

.rounded-bl-6 {
    border-bottom-left-radius: 6rpx;
}

.rounded-br-16 {
    border-bottom-right-radius: 16rpx;
}

.rounded-br-32 {
    border-bottom-right-radius: 32rpx;
}

.rounded-br-6 {
    border-bottom-right-radius: 6rpx;
}

.rounded-lb-4 {
    border-bottom-left-radius: 4rpx;
}

.rounded-lt-26 {
    border-top-left-radius: 26rpx;
}

.rounded-lt-4 {
    border-top-left-radius: 4rpx;
}

.rounded-rb-26 {
    border-bottom-right-radius: 26rpx;
}

.rounded-rb-4 {
    border-bottom-right-radius: 4rpx;
}

.rounded-rt-15,.rounded-tr-15 {
    border-top-right-radius: 15rpx;
}

.rounded-rt-26 {
    border-top-right-radius: 26rpx;
}

.rounded-rt-4 {
    border-top-right-radius: 4rpx;
}

.rounded-tl-6 {
    border-top-left-radius: 6rpx;
}

.rounded-tr-32 {
    border-top-right-radius: 32rpx;
}

.rounded-tr-6 {
    border-top-right-radius: 6rpx;
}

._i_border-none,.border-none_i_ {
    border-style: none !important;
}

.b-dashed,.border-dashed {
    border-style: dashed;
}

.b-none,.border-none {
    border-style: none;
}

.b-solid,.b-style-solid,.border-solid {
    border-style: solid;
}

.border-inherit {
    border-style: inherit;
}

.first-border-none:first-child {
    border-style: none;
}

.last-border-none:last-child {
    border-style: none;
}

.before-border-solid::before {
    border-style: solid;
}

.after-border-inherit::after {
    border-style: inherit;
}

.after-border-none::after {
    border-style: none;
}

.after-border-solid::after {
    border-style: solid;
}

._i_border-r-solid {
    border-right-style: solid !important;
}

.b-b-none,.border-b-none {
    border-bottom-style: none;
}

.b-l-none,.border-l-none {
    border-left-style: none;
}

.b-l-solid,.border-l-solid {
    border-left-style: solid;
}

.b-r-dashed,.border-r-dashed {
    border-right-style: dashed;
}

.b-r-none,.border-r-none {
    border-right-style: none;
}

.b-r-solid,.border-r-solid {
    border-right-style: solid;
}

.b-t-dashed,.border-t-dashed {
    border-top-style: dashed;
}

.b-t-solid,.border-t-solid {
    border-top-style: solid;
}

.border-b-dashed {
    border-bottom-style: dashed;
}

.border-b-solid,.not-last-border-b-solid:not(:last-child) {
    border-bottom-style: solid;
}

.border-l-dashed {
    border-left-style: dashed;
}

.border-l-dotted {
    border-left-style: dotted;
}

.border-t-dotted {
    border-top-style: dotted;
}

.border-t-none {
    border-top-style: none;
}

.first-b-l-none:first-child {
    border-left-style: none;
}

.last-b-l-solid:last-child {
    border-left-style: solid;
}

.last-border-b-none:last-child {
    border-bottom-style: none;
}

.last-border-r-none:last-child {
    border-right-style: none;
}

.after-border-b-solid::after {
    border-bottom-style: solid;
}

.not-last-after-border-b-dashed:not(:last-child)::after {
    border-bottom-style: dashed;
}

._i_bg-_bl__h_F5F5F5_br_ {
    --un-bg-opacity: 1 !important;
    background-color: rgba(245, 245, 245, var(--un-bg-opacity)) !important;
}

._i_bg-_bl_rgba_pl_204_2c_204_2c_204_2c_0_d_5_pr__br_ {
    --un-bg-opacity: 0.5 !important;
    background-color: rgba(204, 204, 204, var(--un-bg-opacity)) !important;
}

._i_bg-primary {
    background-color: var(--std-primary-color) !important;
}

.bg-_bl__h_000_br_,.bg-_bl__h_000000_br_,.bg-_bl_rgb_pl_0_2c_0_2c_0_pr__br_,.bg-_h_000,.bg-black,.bg-hex-000 {
    --un-bg-opacity: 1;
    background-color: rgba(0, 0, 0, var(--un-bg-opacity));
}

.bg-_bl__h_000000BF_br_,.bg-_bl_rgba_pl_0_2c_0_2c_0_2c_0_d_75_pr__br_ {
    --un-bg-opacity: 0.75;
    background-color: rgba(0, 0, 0, var(--un-bg-opacity));
}

.bg-_bl__h_00a47c_br_,.bg-_bl__h_00A47C_br_,.bg-_h_00a47c,.bg-_h_00A47C,.bg-hex-00a47c {
    --un-bg-opacity: 1;
    background-color: rgba(0, 164, 124, var(--un-bg-opacity));
}

.bg-_bl__h_00A47C1A_br_ {
    --un-bg-opacity: 0.1;
    background-color: rgba(0, 164, 124, var(--un-bg-opacity));
}

.bg-_bl__h_00a57d_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(0, 165, 125, var(--un-bg-opacity));
}

.bg-_bl__h_07c160_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(7, 193, 96, var(--un-bg-opacity));
}

.bg-_bl__h_108ee9_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(16, 142, 233, var(--un-bg-opacity));
}

.bg-_bl__h_1a1a1a_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(26, 26, 26, var(--un-bg-opacity));
}

.bg-_bl__h_1aad19_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(26, 173, 25, var(--un-bg-opacity));
}

.bg-_bl__h_31aa82_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(49, 170, 130, var(--un-bg-opacity));
}

.bg-_bl__h_322790_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(50, 39, 144, var(--un-bg-opacity));
}

.bg-_bl__h_323232_br_,.bg-hex-323232 {
    --un-bg-opacity: 1;
    background-color: rgba(50, 50, 50, var(--un-bg-opacity));
}

.bg-_bl__h_333_br_,.bg-_h_333,.bg-hex-333 {
    --un-bg-opacity: 1;
    background-color: rgba(51, 51, 51, var(--un-bg-opacity));
}

.bg-_bl__h_3E3E3E_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(62, 62, 62, var(--un-bg-opacity));
}

.bg-_bl__h_40ba5a_br_,.bg-hex-40ba5a {
    --un-bg-opacity: 1;
    background-color: rgba(64, 186, 90, var(--un-bg-opacity));
}

.bg-_bl__h_414141_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(65, 65, 65, var(--un-bg-opacity));
}

.bg-_bl__h_49c265_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(73, 194, 101, var(--un-bg-opacity));
}

.bg-_bl__h_555_br_,.bg-_h_555 {
    --un-bg-opacity: 1;
    background-color: rgba(85, 85, 85, var(--un-bg-opacity));
}

.bg-_bl__h_999_br_,.bg-_bl__h_999999_br_,.bg-_h_999,.bg-hex-999 {
    --un-bg-opacity: 1;
    background-color: rgba(153, 153, 153, var(--un-bg-opacity));
}

.bg-_bl__h_a9b910_br_,.bg-_bl_rgb_pl_169_2c_185_2c_16_pr__br_ {
    --un-bg-opacity: 1;
    background-color: rgba(169, 185, 16, var(--un-bg-opacity));
}

.bg-_bl__h_aaa_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(170, 170, 170, var(--un-bg-opacity));
}

.bg-_bl__h_adadad_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(173, 173, 173, var(--un-bg-opacity));
}

.bg-_bl__h_afafaf_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(175, 175, 175, var(--un-bg-opacity));
}

.bg-_bl__h_b4ecce_br__i_ {
    --un-bg-opacity: 1 !important;
    background-color: rgba(180, 236, 206, var(--un-bg-opacity)) !important;
}

.bg-_bl__h_bbb_br_,.bg-hex-bbb {
    --un-bg-opacity: 1;
    background-color: rgba(187, 187, 187, var(--un-bg-opacity));
}

.bg-_bl__h_bebebe_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(190, 190, 190, var(--un-bg-opacity));
}

.bg-_bl__h_c4963f_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(196, 150, 63, var(--un-bg-opacity));
}

.bg-_bl__h_ccc_br_,.bg-_bl__h_CCC_br_,.bg-_bl__h_cccccc_br_,.bg-_h_ccc,.bg-_h_CCC,.bg-hex-ccc {
    --un-bg-opacity: 1;
    background-color: rgba(204, 204, 204, var(--un-bg-opacity));
}

.bg-_bl__h_cccccc_br__i_ {
    --un-bg-opacity: 1 !important;
    background-color: rgba(204, 204, 204, var(--un-bg-opacity)) !important;
}

.bg-_bl__h_d2d2d2_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(210, 210, 210, var(--un-bg-opacity));
}

.bg-_bl__h_d8d8d8_br_,.bg-hex-d8d8d8 {
    --un-bg-opacity: 1;
    background-color: rgba(216, 216, 216, var(--un-bg-opacity));
}

.bg-_bl__h_d9a776_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(217, 167, 118, var(--un-bg-opacity));
}

.bg-_bl__h_d9a776_br__i_ {
    --un-bg-opacity: 1 !important;
    background-color: rgba(217, 167, 118, var(--un-bg-opacity)) !important;
}

.bg-_bl__h_d9f6ff_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(217, 246, 255, var(--un-bg-opacity));
}

.bg-_bl__h_ddd_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(221, 221, 221, var(--un-bg-opacity));
}

.bg-_bl__h_e1e1e1_br_,.bg-_bl__h_E1E1E1_br_,.bg-_bl_rgb_pl_225_2c_225_2c_225_pr__br_,.bg-_h_e1e1e1 {
    --un-bg-opacity: 1;
    background-color: rgba(225, 225, 225, var(--un-bg-opacity));
}

.bg-_bl__h_e2e2e2_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(226, 226, 226, var(--un-bg-opacity));
}

.bg-_bl__h_e3e3e3_br_,.bg-_h_e3e3e3 {
    --un-bg-opacity: 1;
    background-color: rgba(227, 227, 227, var(--un-bg-opacity));
}

.bg-_bl__h_e5e5e5_br_,.bg-_h_e5e5e5,.bg-hex-e5e5e5 {
    --un-bg-opacity: 1;
    background-color: rgba(229, 229, 229, var(--un-bg-opacity));
}

.bg-_bl__h_e6e6e6_br_,.bg-_bl_rgb_pl_230_2c_230_2c_230_pr__br_,.bg-hex-e6e6e6 {
    --un-bg-opacity: 1;
    background-color: rgba(230, 230, 230, var(--un-bg-opacity));
}

.bg-_bl__h_e7e7e7_br_,.bg-hex-e7e7e7 {
    --un-bg-opacity: 1;
    background-color: rgba(231, 231, 231, var(--un-bg-opacity));
}

.bg-_bl__h_eb5a53_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(235, 90, 83, var(--un-bg-opacity));
}

.bg-_bl__h_ebebeb_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(235, 235, 235, var(--un-bg-opacity));
}

.bg-_bl__h_ebefc1_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(235, 239, 193, var(--un-bg-opacity));
}

.bg-_bl__h_ededed_br_,.bg-_bl__h_EDEDED_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(237, 237, 237, var(--un-bg-opacity));
}

.bg-_bl__h_eee_br_,.bg-_bl__h_EEE_br_,.bg-_bl__h_eeeeee_br_,.bg-_h_eee,.bg-hex-eee,.bg-hex-eeeeee {
    --un-bg-opacity: 1;
    background-color: rgba(238, 238, 238, var(--un-bg-opacity));
}

.bg-_bl__h_efefef_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(239, 239, 239, var(--un-bg-opacity));
}

.bg-_bl__h_F0F0F0_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(240, 240, 240, var(--un-bg-opacity));
}

.bg-_bl__h_F1F1EF_br_,.bg-_h_F1F1EF {
    --un-bg-opacity: 1;
    background-color: rgba(241, 241, 239, var(--un-bg-opacity));
}

.bg-_bl__h_f1f1f1_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(241, 241, 241, var(--un-bg-opacity));
}

.bg-_bl__h_f2f2f2_br_,.bg-_bl_rgb_pl_242_2c_242_2c_242_pr__br_,.bg-hex-f2f2f2 {
    --un-bg-opacity: 1;
    background-color: rgba(242, 242, 242, var(--un-bg-opacity));
}

.bg-_bl__h_f2fbf9_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(242, 251, 249, var(--un-bg-opacity));
}

.bg-_bl__h_f3e7e7_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(243, 231, 231, var(--un-bg-opacity));
}

.bg-_bl__h_F3F5F9_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(243, 245, 249, var(--un-bg-opacity));
}

.bg-_bl__h_F47B24_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(244, 123, 36, var(--un-bg-opacity));
}

.bg-_bl__h_f4cd80_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(244, 205, 128, var(--un-bg-opacity));
}

.bg-_bl__h_f4f1f4_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(244, 241, 244, var(--un-bg-opacity));
}

.bg-_bl__h_f4f4f4_br_,.bg-_bl__h_F4F4F4_br_,.bg-hex-f4f4f4,.bg-hex-F4F4F4 {
    --un-bg-opacity: 1;
    background-color: rgba(244, 244, 244, var(--un-bg-opacity));
}

.bg-_bl__h_F53F3F_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(245, 63, 63, var(--un-bg-opacity));
}

.bg-_bl__h_f5f5f5_br_,.bg-_bl__h_F5F5F5_br_,.bg-_bl_rgb_pl_245_2c_245_2c_245_pr__br_,.bg-_h_f5f5f5,.bg-_h_F5F5F5,.bg-hex-f5f5f5,.bg-hex-F5F5F5,.bg-neutral-100 {
    --un-bg-opacity: 1;
    background-color: rgba(245, 245, 245, var(--un-bg-opacity));
}

.bg-_bl__h_f64934_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(246, 73, 52, var(--un-bg-opacity));
}

.bg-_bl__h_f6f6f6_br_,.bg-_bl__h_F6F6F6_br_,.bg-_h_F6F6F6,.bg-hex-f6f6f6,.bg-light {
    --un-bg-opacity: 1;
    background-color: rgba(246, 246, 246, var(--un-bg-opacity));
}

.bg-_bl__h_f76260_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(247, 98, 96, var(--un-bg-opacity));
}

.bg-_bl__h_f7f7f7_br_,.bg-_bl__h_F7F7F7_br_,.bg-_h_f7f7f7,.bg-hex-f7f7f7 {
    --un-bg-opacity: 1;
    background-color: rgba(247, 247, 247, var(--un-bg-opacity));
}

.bg-_bl__h_f7f7f7_br__i_ {
    --un-bg-opacity: 1 !important;
    background-color: rgba(247, 247, 247, var(--un-bg-opacity)) !important;
}

.bg-_bl__h_f84036_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(248, 64, 54, var(--un-bg-opacity));
}

.bg-_bl__h_f8f8f8_br_,.bg-_bl__h_F8F8F8_br_,.bg-hex-f8f8f8 {
    --un-bg-opacity: 1;
    background-color: rgba(248, 248, 248, var(--un-bg-opacity));
}

.bg-_bl__h_f95731_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(249, 87, 49, var(--un-bg-opacity));
}

.bg-_bl__h_f97d3d_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(249, 125, 61, var(--un-bg-opacity));
}

.bg-_bl__h_F9D423_br_,.bg-_h_f9d423 {
    --un-bg-opacity: 1;
    background-color: rgba(249, 212, 35, var(--un-bg-opacity));
}

.bg-_bl__h_f9f9f9_br_,.bg-hex-f9f9f9 {
    --un-bg-opacity: 1;
    background-color: rgba(249, 249, 249, var(--un-bg-opacity));
}

.bg-_bl__h_fa4c4c_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(250, 76, 76, var(--un-bg-opacity));
}

.bg-_bl__h_FAE3E2_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(250, 227, 226, var(--un-bg-opacity));
}

.bg-_bl__h_fafafa_br_,.bg-_bl__h_FAFAFA_br_,.bg-hex-fafafa,.bg-neutral-50 {
    --un-bg-opacity: 1;
    background-color: rgba(250, 250, 250, var(--un-bg-opacity));
}

.bg-_bl__h_fbf3f2_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(251, 243, 242, var(--un-bg-opacity));
}

.bg-_bl__h_fbfbfb_br_,.bg-hex-fbfbfb {
    --un-bg-opacity: 1;
    background-color: rgba(251, 251, 251, var(--un-bg-opacity));
}

.bg-_bl__h_fc4930_br_,.bg-_h_fc4930 {
    --un-bg-opacity: 1;
    background-color: rgba(252, 73, 48, var(--un-bg-opacity));
}

.bg-_bl__h_fd5e5d_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(253, 94, 93, var(--un-bg-opacity));
}

.bg-_bl__h_fdd488_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(253, 212, 136, var(--un-bg-opacity));
}

.bg-_bl__h_FDF4E6_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(253, 244, 230, var(--un-bg-opacity));
}

.bg-_bl__h_fdf9f0_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(253, 249, 240, var(--un-bg-opacity));
}

.bg-_bl__h_fe3c30_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(254, 60, 48, var(--un-bg-opacity));
}

.bg-_bl__h_FEEED3_br_,.bg-_h_FEEED3 {
    --un-bg-opacity: 1;
    background-color: rgba(254, 238, 211, var(--un-bg-opacity));
}

.bg-_bl__h_FEF7EA_br_,.bg-_h_FEF7EA {
    --un-bg-opacity: 1;
    background-color: rgba(254, 247, 234, var(--un-bg-opacity));
}

.bg-_bl__h_ff2020_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(255, 32, 32, var(--un-bg-opacity));
}

.bg-_bl__h_ff3b5b_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(255, 59, 91, var(--un-bg-opacity));
}

.bg-_bl__h_ff3d54_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(255, 61, 84, var(--un-bg-opacity));
}

.bg-_bl__h_ff4546_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(255, 69, 70, var(--un-bg-opacity));
}

.bg-_bl__h_ff4c4c_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(255, 76, 76, var(--un-bg-opacity));
}

.bg-_bl__h_ff6565_br_,.bg-hex-ff6565 {
    --un-bg-opacity: 1;
    background-color: rgba(255, 101, 101, var(--un-bg-opacity));
}

.bg-_bl__h_ff671f_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(255, 103, 31, var(--un-bg-opacity));
}

.bg-_bl__h_FF6768_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(255, 103, 104, var(--un-bg-opacity));
}

.bg-_bl__h_ff742a_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(255, 116, 42, var(--un-bg-opacity));
}

.bg-_bl__h_ff8800_br_,.bg-_h_FF8800 {
    --un-bg-opacity: 1;
    background-color: rgba(255, 136, 0, var(--un-bg-opacity));
}

.bg-_bl__h_ffa348_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(255, 163, 72, var(--un-bg-opacity));
}

.bg-_bl__h_ffc300_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(255, 195, 0, var(--un-bg-opacity));
}

.bg-_bl__h_ffc61e_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(255, 198, 30, var(--un-bg-opacity));
}

.bg-_bl__h_ffcc30_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(255, 204, 48, var(--un-bg-opacity));
}

.bg-_bl__h_ffd912_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(255, 217, 18, var(--un-bg-opacity));
}

.bg-_bl__h_ffdba6_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(255, 219, 166, var(--un-bg-opacity));
}

.bg-_bl__h_ffdc98_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(255, 220, 152, var(--un-bg-opacity));
}

.bg-_bl__h_ffe4e4_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(255, 228, 228, var(--un-bg-opacity));
}

.bg-_bl__h_ffefdc_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(255, 239, 220, var(--un-bg-opacity));
}

.bg-_bl__h_fff_br_,.bg-_bl__h_FFF_br_,.bg-_bl__h_ffffff_br_,.bg-_bl_rgb_pl_255_2c_255_2c_255_pr__br_,.bg-_h_fff,.bg-_h_ffffff,.bg-hex-fff,.bg-hex-ffffff,.bg-white {
    --un-bg-opacity: 1;
    background-color: rgba(255, 255, 255, var(--un-bg-opacity));
}

.bg-_bl__h_fff0c3_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(255, 240, 195, var(--un-bg-opacity));
}

.bg-_bl__h_fff0f0_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(255, 240, 240, var(--un-bg-opacity));
}

.bg-_bl__h_fff5eb_br_ {
    --un-bg-opacity: 1;
    background-color: rgba(255, 245, 235, var(--un-bg-opacity));
}

.bg-_bl__h_FFF5F5_br_,.bg-hex-FFF5F5 {
    --un-bg-opacity: 1;
    background-color: rgba(255, 245, 245, var(--un-bg-opacity));
}

.bg-_bl__h_fff7e1_br_,.bg-_bl__h_FFF7E1_br_,.bg-hex-fff7e1,.bg-hex-FFF7E1 {
    --un-bg-opacity: 1;
    background-color: rgba(255, 247, 225, var(--un-bg-opacity));
}

.bg-_bl_50_p__br_ {
    background-position: 50%;
}

.bg-_bl_calc_pl_100_p_-12rpx_pr_18rpx_br_ {
    background-position: calc(100% - 12rpx)18rpx;
}

.bg-_bl_FFF_br_ {
    background-color: FFF;
}

.bg-_bl_length_c_100_p__100_p__br_,.bg-full {
    background-size: 100% 100%;
}

.bg-_bl_length_c_100_p__434rpx_br_ {
    background-size: 100% 434rpx;
}

.bg-_bl_length_c_100_p__450rpx_br_ {
    background-size: 100% 450rpx;
}

.bg-_bl_length_c_100_p__br_ {
    background-size: 100%;
}

.bg-_bl_length_c_100rpx_100rpx_br_ {
    background-size: 100rpx 100rpx;
}

.bg-_bl_length_c_18px_18px_br_ {
    background-size: 18px 18px;
}

.bg-_bl_length_c_245rpx_74rpx_br_ {
    background-size: 245rpx 74rpx;
}

.bg-_bl_length_c_26rpx_26rpx_br_ {
    background-size: 26rpx 26rpx;
}

.bg-_bl_length_c_28rpx_28rpx_br_ {
    background-size: 28rpx 28rpx;
}

.bg-_bl_length_c_38rpx_38rpx_br_ {
    background-size: 38rpx 38rpx;
}

.bg-_bl_length_c_40rpx_40rpx_br_ {
    background-size: 40rpx 40rpx;
}

.bg-_bl_length_c_45_p__br_ {
    background-size: 45%;
}

.bg-_bl_length_c_60_p__br_ {
    background-size: 60%;
}

.bg-_bl_length_c_750rpx_6rpx_br_ {
    background-size: 750rpx 6rpx;
}

.bg-_bl_length_c_80_p__br_ {
    background-size: 80%;
}

.bg-_bl_length_c_82rpx_28rpx_br_ {
    background-size: 82rpx 28rpx;
}

.bg-_bl_length_c_contain_br_,.bg-contain {
    background-size: contain;
}

.bg-_bl_length_c_cover_br_,.bg-cover {
    background-size: cover;
}

.bg-_bl_linear-gradient_pl_144deg_2c__h_392d22_0_p__2c__h_120e0b_100_p__pr__2c__h_000000_br_ {
    background-color: linear-gradient(144deg,#392d22 0%,#120e0b 100%),#000000;
}

.bg-_bl_linear-gradient_pl_180deg_2c_var_pl_--light-theme-color_pr__0_p__2c__h_fff_100_p__pr__2c__h_fff_br_ {
    background-color: linear-gradient(180deg,var(--light-theme-color) 0%,#fff 100%),#fff;
}

.bg-_bl_position_c_center_br_,.bg-center {
    background-position: center;
}

.bg-_bl_rgb_pl_191_2c_191_2c_191_pr__br_ {
    --un-bg-opacity: 1;
    background-color: rgba(191, 191, 191, var(--un-bg-opacity));
}

.bg-_bl_rgb_pl_216_2c_93_2c_21_pr__br_ {
    --un-bg-opacity: 1;
    background-color: rgba(216, 93, 21, var(--un-bg-opacity));
}

.bg-_bl_rgb_pl_251_2c_69_2c_6_pr__br_ {
    --un-bg-opacity: 1;
    background-color: rgba(251, 69, 6, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_0_2c_0_2c_0_2c__d_55_pr__br_ {
    --un-bg-opacity: .55;
    background-color: rgba(0, 0, 0, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_0_2c_0_2c_0_2c__d_75_pr__br_ {
    --un-bg-opacity: .75;
    background-color: rgba(0, 0, 0, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_0_2c_0_2c_0_2c_0_d_15_pr__br_ {
    --un-bg-opacity: 0.15;
    background-color: rgba(0, 0, 0, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_0_2c_0_2c_0_2c_0_d_17_pr__br_ {
    --un-bg-opacity: 0.17;
    background-color: rgba(0, 0, 0, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_0_2c_0_2c_0_2c_0_d_2_pr__br_ {
    --un-bg-opacity: 0.2;
    background-color: rgba(0, 0, 0, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_0_2c_0_2c_0_2c_0_d_3_pr__br_,.bg-hex-0000004d {
    --un-bg-opacity: 0.3;
    background-color: rgba(0, 0, 0, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_0_2c_0_2c_0_2c_0_d_4_pr__br_,.bg-hex-00000066 {
    --un-bg-opacity: 0.4;
    background-color: rgba(0, 0, 0, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_0_2c_0_2c_0_2c_0_d_5_pr__br_,.bg-hex-0000007f,.bg-hex-00000080 {
    --un-bg-opacity: 0.5;
    background-color: rgba(0, 0, 0, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_0_2c_0_2c_0_2c_0_d_50_pr__br_ {
    --un-bg-opacity: 0.50;
    background-color: rgba(0, 0, 0, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_0_2c_0_2c_0_2c_0_d_55_pr__br_ {
    --un-bg-opacity: 0.55;
    background-color: rgba(0, 0, 0, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_0_2c_0_2c_0_2c_0_d_6_pr__br_,.bg-hex-00000099 {
    --un-bg-opacity: 0.6;
    background-color: rgba(0, 0, 0, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_0_2c_0_2c_0_2c_0_d_74_pr__br_ {
    --un-bg-opacity: 0.74;
    background-color: rgba(0, 0, 0, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_0_2c_0_2c_0_2c_0_d_8_pr__br_,.bg-hex-000000cc {
    --un-bg-opacity: 0.8;
    background-color: rgba(0, 0, 0, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_0_2c_0_2c_0_2c_0_d_88_pr__br_ {
    --un-bg-opacity: 0.88;
    background-color: rgba(0, 0, 0, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_0_2c_0_2c_0_2c_0_pr__br_ {
    --un-bg-opacity: 0;
    background-color: rgba(0, 0, 0, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_237_2c_240_2c_246_2c_0_d_6_pr__br_ {
    --un-bg-opacity: 0.6;
    background-color: rgba(237, 240, 246, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_242_2c_240_2c_236_pr__br_ {
    --un-bg-opacity: 1;
    background-color: rgba(242, 240, 236, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_255_2c_247_2c_225_2c_0_d_2_pr__br_ {
    --un-bg-opacity: 0.2;
    background-color: rgba(255, 247, 225, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_255_2c_255_2c_255_2c__d_96_pr__br_ {
    --un-bg-opacity: .96;
    background-color: rgba(255, 255, 255, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_255_2c_255_2c_255_2c_0_d_1_pr__br_ {
    --un-bg-opacity: 0.1;
    background-color: rgba(255, 255, 255, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_255_2c_255_2c_255_2c_0_d_15_pr__br_ {
    --un-bg-opacity: 0.15;
    background-color: rgba(255, 255, 255, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_255_2c_255_2c_255_2c_0_d_2_pr__br_ {
    --un-bg-opacity: 0.2;
    background-color: rgba(255, 255, 255, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_255_2c_255_2c_255_2c_0_d_3_pr__br_ {
    --un-bg-opacity: 0.3;
    background-color: rgba(255, 255, 255, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_255_2c_255_2c_255_2c_0_d_5_pr__br_ {
    --un-bg-opacity: 0.5;
    background-color: rgba(255, 255, 255, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_255_2c_255_2c_255_2c_0_d_6_pr__br_ {
    --un-bg-opacity: 0.6;
    background-color: rgba(255, 255, 255, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_255_2c_255_2c_255_2c_0_d_8_pr__br_ {
    --un-bg-opacity: 0.8;
    background-color: rgba(255, 255, 255, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_255_2c_255_2c_255_2c_0_d_80_pr__br_ {
    --un-bg-opacity: 0.80;
    background-color: rgba(255, 255, 255, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_255_2c_255_2c_255_2c_0_d_85_pr__br_,.bg-hex-ffffffd9 {
    --un-bg-opacity: 0.85;
    background-color: rgba(255, 255, 255, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_34_2c_175_2c_41_2c_0_d_1_pr__br_ {
    --un-bg-opacity: 0.1;
    background-color: rgba(34, 175, 41, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_51_2c_51_2c_51_2c_0_d_9_pr__br_ {
    --un-bg-opacity: 0.9;
    background-color: rgba(51, 51, 51, var(--un-bg-opacity));
}

.bg-_bl_rgba_pl_68_2c_68_2c_68_2c_0_d_5_pr__br_ {
    --un-bg-opacity: 0.5;
    background-color: rgba(68, 68, 68, var(--un-bg-opacity));
}

.bg-_bl_url_pl_https_c__s__s_images_d_qmai_d_cn_s_resource_s_20210824210816_s_2024_s_09_s_06_s_std-pluginUser-coupon-bg_d_png_pr__br_ {
    --un-url: url(https://images.qmai.cn/resource/20210824210816/2024/09/06/std-pluginUser-coupon-bg.png);
    background-image: var(--un-url);
}

.bg-_bl_url_pl_https_c__s__s_images_d_qmai_d_cn_s_resource_s_20210825170516_s_2024_s_07_s_23_s_20240723-155532_d_png_pr__br_ {
    --un-url: url(https://images.qmai.cn/resource/20210825170516/2024/07/23/20240723-155532.png);
    background-image: var(--un-url);
}

.bg-_bl_var_pl_--bg-1_2c__h_ffffff_pr__br_ {
    background-color: var(--bg-1,#ffffff);
}

.bg-_bl_var_pl_--color-primary-opacity-10_2c_rgba_pl_0_2c_164_2c_124_2c_0_d_1019607843_pr__pr__br_ {
    background-color: var(--color-primary-opacity-10,rgba(0,164,124,0.1019607843));
}

.bg-_bl_var_pl_--color-primary_2c__h_07c160_pr__br_ {
    background-color: var(--color-primary,#07c160);
}

.bg-_bl_var_pl_--color-theme_pr__br_ {
    background-color: var(--color-theme);
}

.bg-_bl_var_pl_--light-theme-color_pr__br_ {
    background-color: var(--light-theme-color);
}

.bg-_bl_var_pl_--std-primary-color_2c__h_00a47c_pr__br_ {
    background-color: var(--std-primary-color,#00a47c);
}

.bg-_bl_var_pl_--theme-color_pr__br_ {
    background-color: var(--theme-color);
}

.bg-_h_06CF6E {
    --un-bg-opacity: 1;
    background-color: rgba(6, 207, 110, var(--un-bg-opacity));
}

.bg-_h_1677ff {
    --un-bg-opacity: 1;
    background-color: rgba(22, 119, 255, var(--un-bg-opacity));
}

.bg-_h_5c5c5c {
    --un-bg-opacity: 1;
    background-color: rgba(92, 92, 92, var(--un-bg-opacity));
}

.bg-_h_c9c9c9,.bg-hex-c9c9c9 {
    --un-bg-opacity: 1;
    background-color: rgba(201, 201, 201, var(--un-bg-opacity));
}

.bg-_h_FC49301A {
    --un-bg-opacity: 0.1;
    background-color: rgba(252, 73, 48, var(--un-bg-opacity));
}

.bg-_h_FDC513 {
    --un-bg-opacity: 1;
    background-color: rgba(253, 197, 19, var(--un-bg-opacity));
}

.bg-_h_FFECE8 {
    --un-bg-opacity: 1;
    background-color: rgba(255, 236, 232, var(--un-bg-opacity));
}

.bg-_h_ffedea {
    --un-bg-opacity: 1;
    background-color: rgba(255, 237, 234, var(--un-bg-opacity));
}

.bg-_h_fff8e8,.bg-_h_FFF8E8 {
    --un-bg-opacity: 1;
    background-color: rgba(255, 248, 232, var(--un-bg-opacity));
}

.bg-black_s_50 {
    background-color: rgba(0, 0, 0, 0.5);
}

.bg-dark,.bg-hex-222 {
    --un-bg-opacity: 1;
    background-color: rgba(34, 34, 34, var(--un-bg-opacity));
}

.bg-gray-1 {
    --un-bg-opacity: 1;
    background-color: rgba(243, 244, 246, var(--un-bg-opacity));
}

.bg-gray-3 {
    --un-bg-opacity: 1;
    background-color: rgba(209, 213, 219, var(--un-bg-opacity));
}

.bg-hex-00000005 {
    --un-bg-opacity: 0.02;
    background-color: rgba(0, 0, 0, var(--un-bg-opacity));
}

.bg-hex-000000b3 {
    --un-bg-opacity: 0.7;
    background-color: rgba(0, 0, 0, var(--un-bg-opacity));
}

.bg-hex-2c2c2c {
    --un-bg-opacity: 1;
    background-color: rgba(44, 44, 44, var(--un-bg-opacity));
}

.bg-hex-323233 {
    --un-bg-opacity: 1;
    background-color: rgba(50, 50, 51, var(--un-bg-opacity));
}

.bg-hex-666666 {
    --un-bg-opacity: 1;
    background-color: rgba(102, 102, 102, var(--un-bg-opacity));
}

.bg-hex-7a7a7a {
    --un-bg-opacity: 1;
    background-color: rgba(122, 122, 122, var(--un-bg-opacity));
}

.bg-hex-b2dff9 {
    --un-bg-opacity: 1;
    background-color: rgba(178, 223, 249, var(--un-bg-opacity));
}

.bg-hex-cfcfcf {
    --un-bg-opacity: 1;
    background-color: rgba(207, 207, 207, var(--un-bg-opacity));
}

.bg-hex-d3b57f {
    --un-bg-opacity: 1;
    background-color: rgba(211, 181, 127, var(--un-bg-opacity));
}

.bg-hex-dbdbdb {
    --un-bg-opacity: 1;
    background-color: rgba(219, 219, 219, var(--un-bg-opacity));
}

.bg-hex-e0e0e0 {
    --un-bg-opacity: 1;
    background-color: rgba(224, 224, 224, var(--un-bg-opacity));
}

.bg-hex-ebedf0 {
    --un-bg-opacity: 1;
    background-color: rgba(235, 237, 240, var(--un-bg-opacity));
}

.bg-hex-f62e20 {
    --un-bg-opacity: 1;
    background-color: rgba(246, 46, 32, var(--un-bg-opacity));
}

.bg-hex-f7f7f780 {
    --un-bg-opacity: 0.5;
    background-color: rgba(247, 247, 247, var(--un-bg-opacity));
}

.bg-hex-f7f8fa {
    --un-bg-opacity: 1;
    background-color: rgba(247, 248, 250, var(--un-bg-opacity));
}

.bg-hex-FA330E {
    --un-bg-opacity: 1;
    background-color: rgba(250, 51, 14, var(--un-bg-opacity));
}

.bg-hex-fa3423 {
    --un-bg-opacity: 1;
    background-color: rgba(250, 52, 35, var(--un-bg-opacity));
}

.bg-hex-fa342319 {
    --un-bg-opacity: 0.1;
    background-color: rgba(250, 52, 35, var(--un-bg-opacity));
}

.bg-hex-fcd008 {
    --un-bg-opacity: 1;
    background-color: rgba(252, 208, 8, var(--un-bg-opacity));
}

.bg-hex-fd2c2d {
    --un-bg-opacity: 1;
    background-color: rgba(253, 44, 45, var(--un-bg-opacity));
}

.bg-hex-FDE1F3 {
    --un-bg-opacity: 1;
    background-color: rgba(253, 225, 243, var(--un-bg-opacity));
}

.bg-hex-ff3e37 {
    --un-bg-opacity: 1;
    background-color: rgba(255, 62, 55, var(--un-bg-opacity));
}

.bg-hex-ff5a001a {
    --un-bg-opacity: 0.1;
    background-color: rgba(255, 90, 0, var(--un-bg-opacity));
}

.bg-hex-ff8f0f,.bg-hex-FF8F0F {
    --un-bg-opacity: 1;
    background-color: rgba(255, 143, 15, var(--un-bg-opacity));
}

.bg-hex-FFFCEE {
    --un-bg-opacity: 1;
    background-color: rgba(255, 252, 238, var(--un-bg-opacity));
}

.bg-inherit {
    background-color: inherit;
}

.bg-orange {
    --un-bg-opacity: 1;
    background-color: rgba(251, 146, 60, var(--un-bg-opacity));
}

.bg-primary {
    background-color: var(--std-primary-color);
}

.bg-primary-opacity-10 {
    background-color: var(--std-primary-color-opacity-10);
}

.bg-primary-opacity-20 {
    background-color: var(--std-primary-color-opacity-20);
}

.bg-primary-opacity-8 {
    background-color: var(--std-primary-color-opacity-8);
}

.bg-primary-opacity-90 {
    background-color: var(--std-primary-color-opacity-90);
}

.bg-transparent {
    background-color: transparent;
}

.bg-transparent_i_ {
    background-color: transparent !important;
}

.first-bg-_bl__h_fff_br_:first-child {
    --un-bg-opacity: 1;
    background-color: rgba(255, 255, 255, var(--un-bg-opacity));
}

.last-after-bg-_bl__h_fff_br_:last-child::after {
    --un-bg-opacity: 1;
    background-color: rgba(255, 255, 255, var(--un-bg-opacity));
}

.last-bg-_bl__h_c84835_br_:last-child {
    --un-bg-opacity: 1;
    background-color: rgba(200, 72, 53, var(--un-bg-opacity));
}

.before-bg-_bl__h_333_br_::before {
    --un-bg-opacity: 1;
    background-color: rgba(51, 51, 51, var(--un-bg-opacity));
}

.before-bg-_bl__h_333333_br_::before {
    --un-bg-opacity: 1;
    background-color: rgba(51, 51, 51, var(--un-bg-opacity));
}

.before-bg-_bl__h_e5e5e5_br_::before {
    --un-bg-opacity: 1;
    background-color: rgba(229, 229, 229, var(--un-bg-opacity));
}

.before-bg-white::before {
    --un-bg-opacity: 1;
    background-color: rgba(255, 255, 255, var(--un-bg-opacity));
}

.after-bg-_bl__h_e1e1e1_br_::after {
    --un-bg-opacity: 1;
    background-color: rgba(225, 225, 225, var(--un-bg-opacity));
}

.after-bg-_bl__h_e5e5e5_br_::after {
    --un-bg-opacity: 1;
    background-color: rgba(229, 229, 229, var(--un-bg-opacity));
}

.after-bg-white::after {
    --un-bg-opacity: 1;
    background-color: rgba(255, 255, 255, var(--un-bg-opacity));
}

.not-last-after-bg-_bl__h_eee_br_:not(:last-child)::after {
    --un-bg-opacity: 1;
    background-color: rgba(238, 238, 238, var(--un-bg-opacity));
}

.bg-op-80,.bg-opacity-80 {
    --un-bg-opacity: 0.8;
}

.bg-opacity-25 {
    --un-bg-opacity: 0.25;
}

.bg-opacity-75 {
    --un-bg-opacity: 0.75;
}

.bg-gradient-_bl_-180deg_2c_rgba_pl_255_2c_255_2c_255_2c_0_pr__2c__h_fff_br_ {
    --un-gradient: -180deg,rgba(255,255,255,0),#fff;
}

.bg-gradient-_bl_-54deg_2c__h_ff3370_2c__h_ff5c4b_br_ {
    --un-gradient: -54deg,#ff3370,#ff5c4b;
}

.bg-gradient-_bl_-90deg_2c__h_58ca8a_2c__h_6fce85_br_ {
    --un-gradient: -90deg,#58ca8a,#6fce85;
}

.bg-gradient-_bl_-90deg_2c__h_fedfc0_2c__h_ffe8d1_br_ {
    --un-gradient: -90deg,#fedfc0,#ffe8d1;
}

.bg-gradient-_bl_-90deg_2c__h_ff7a36_2c__h_fb4607_br_ {
    --un-gradient: -90deg,#ff7a36,#fb4607;
}

.bg-gradient-_bl_-90deg_2c__h_ff7a36_2c__h_ff3c40_br_ {
    --un-gradient: -90deg,#ff7a36,#ff3c40;
}

.bg-gradient-_bl_-90deg_2c_rgb_pl_255_2c_189_2c_12_pr__2c_rgb_pl_255_2c_218_2c_44_pr__br_ {
    --un-gradient: -90deg,rgb(255,189,12),rgb(255,218,44);
}

.bg-gradient-_bl_0deg_2c_rgb_pl_3_2c_0_2c_0_pr__0_p__2c_rgba_pl_0_2c_0_2c_0_2c_0_pr__100_p__br_ {
    --un-gradient: 0deg,rgb(3,0,0) 0%,rgba(0,0,0,0) 100%;
}

.bg-gradient-_bl_123deg_2c___h_fceae294_28_d_21_p__2c___h_fff_58_d_27_p__2c___h_fff6e8_101_d_79_p__2c___h_fff_br_ {
    --un-gradient: 123deg, #fceae294 28.21%, #fff 58.27%, #fff6e8 101.79%, #fff;
}

.bg-gradient-_bl_154deg_2c__h_fef9f5_0_p__2c__h_ffd99f_89_p__br_ {
    --un-gradient: 154deg,#fef9f5 0%,#ffd99f 89%;
}

.bg-gradient-_bl_180deg_2c__h_4a32c8_2c__h_201e96_br_ {
    --un-gradient: 180deg,#4a32c8,#201e96;
}

.bg-gradient-_bl_180deg_2c__h_808080_2c__h_d1d1d1_br_ {
    --un-gradient: 180deg,#808080,#d1d1d1;
}

.bg-gradient-_bl_180deg_2c__h_a2e7fc_2c__h_462fc3_br_ {
    --un-gradient: 180deg,#a2e7fc,#462fc3;
}

.bg-gradient-_bl_180deg_2c__h_a2e7fc_2c__h_ccbeec_br_ {
    --un-gradient: 180deg,#a2e7fc,#ccbeec;
}

.bg-gradient-_bl_180deg_2c__h_f5f5f5_2c__h_d6d6d6_br_ {
    --un-gradient: 180deg,#f5f5f5,#d6d6d6;
}

.bg-gradient-_bl_180deg_2c__h_fe373f_0_p__2c__h_ff0a23_100_p__br_ {
    --un-gradient: 180deg,#fe373f 0%,#ff0a23 100%;
}

.bg-gradient-_bl_180deg_2c__h_feeebb_2c__h_feae49_br_ {
    --un-gradient: 180deg,#feeebb,#feae49;
}

.bg-gradient-_bl_180deg_2c__h_ffe174_2c__h_fff5d2_br_ {
    --un-gradient: 180deg,#ffe174,#fff5d2;
}

.bg-gradient-_bl_180deg_2c__h_ffeed9_0_p__2c__h_fecd95_100_p__br_ {
    --un-gradient: 180deg,#ffeed9 0%,#fecd95 100%;
}

.bg-gradient-_bl_180deg_2c_var_pl_--theme-color_pr__0_p__2c_var_pl_--light-theme-color_pr__100_p__br_ {
    --un-gradient: 180deg,var(--theme-color) 0%,var(--light-theme-color) 100%;
}

.bg-gradient-_bl_195deg_2c__h_ff742a_2c__h_ffa039_br_ {
    --un-gradient: 195deg,#ff742a,#ffa039;
}

.bg-gradient-_bl_214deg_2c__h_ff5d63_0_p__2c__h_fc1e34_100_p__br_ {
    --un-gradient: 214deg,#ff5d63 0%,#fc1e34 100%;
}

.bg-gradient-_bl_270deg_2c__h_ff2c00_2c__h_fe6a00_br_ {
    --un-gradient: 270deg,#ff2c00,#fe6a00;
}

.bg-gradient-_bl_270deg_2c__h_ffdc98_0_p__2c__h_fff1cb_100_p__br_ {
    --un-gradient: 270deg,#ffdc98 0%,#fff1cb 100%;
}

.bg-gradient-_bl_90deg_2c__h_f20000_2c__h_fd4e17_br_ {
    --un-gradient: 90deg,#f20000,#fd4e17;
}

.bg-gradient-_bl_90deg_2c__h_fe1936_2c__h_fe762c_br_ {
    --un-gradient: 90deg,#fe1936,#fe762c;
}

.bg-gradient-_bl_90deg_2c__h_ff4c3d_2c__h_ff5f53_br_ {
    --un-gradient: 90deg,#ff4c3d,#ff5f53;
}

.bg-gradient-_bl_90deg_2c__h_ff6d5e_2c__h_ff5948_br_ {
    --un-gradient: 90deg,#ff6d5e,#ff5948;
}

.bg-gradient-_bl_90deg_2c__h_ffffff00_0_p__2c__h_ffffff80_50_p__br_ {
    --un-gradient: 90deg,#ffffff00 0%,#ffffff80 50%;
}

.from-_bl__h_F2CE9B_br_ {
    --un-gradient-from-position: 0%;
    --un-gradient-from: rgba(242, 206, 155, var(--un-from-opacity, 1)) var(--un-gradient-from-position);
    --un-gradient-to-position: 100%;
    --un-gradient-to: rgba(242, 206, 155, 0) var(--un-gradient-to-position);
    --un-gradient-stops: var(--un-gradient-from), var(--un-gradient-to);
}

.from-_bl__h_FFF_br_,.from-white {
    --un-gradient-from-position: 0%;
    --un-gradient-from: rgba(255, 255, 255, var(--un-from-opacity, 1)) var(--un-gradient-from-position);
    --un-gradient-to-position: 100%;
    --un-gradient-to: rgba(255, 255, 255, 0) var(--un-gradient-to-position);
    --un-gradient-stops: var(--un-gradient-from), var(--un-gradient-to);
}

.from-_bl_rgba_pl_255_2c_255_2c_255_2c_0_d_1_pr__br_ {
    --un-gradient-from-position: 0%;
    --un-gradient-from: rgba(255, 255, 255, var(--un-from-opacity, 0.1)) var(--un-gradient-from-position);
    --un-gradient-to-position: 100%;
    --un-gradient-to: rgba(255, 255, 255, 0) var(--un-gradient-to-position);
    --un-gradient-stops: var(--un-gradient-from), var(--un-gradient-to);
}

.from-_bl_rgba_pl_255_2c_255_2c_255_2c_0_pr__br_ {
    --un-gradient-from-position: 0%;
    --un-gradient-from: rgba(255, 255, 255, var(--un-from-opacity, 0)) var(--un-gradient-from-position);
    --un-gradient-to-position: 100%;
    --un-gradient-to: rgba(255, 255, 255, 0) var(--un-gradient-to-position);
    --un-gradient-stops: var(--un-gradient-from), var(--un-gradient-to);
}

.from-_bl_var_pl_--light-theme-color_pr__br_ {
    --un-gradient-from-position: 0%;
    --un-gradient-from: var(--light-theme-color) var(--un-gradient-from-position);
    --un-gradient-to-position: 100%;
    --un-gradient-to: rgba(255, 255, 255, 0) var(--un-gradient-to-position);
    --un-gradient-stops: var(--un-gradient-from), var(--un-gradient-to);
}

.from-_bl_var_pl_--std-primary-color-opacity-10_pr__br_,.from-primary-opacity-10 {
    --un-gradient-from-position: 0%;
    --un-gradient-from: var(--std-primary-color-opacity-10) var(--un-gradient-from-position);
    --un-gradient-to-position: 100%;
    --un-gradient-to: rgba(255, 255, 255, 0) var(--un-gradient-to-position);
    --un-gradient-stops: var(--un-gradient-from), var(--un-gradient-to);
}

.from-_h_ff5313 {
    --un-gradient-from-position: 0%;
    --un-gradient-from: rgba(255, 83, 19, var(--un-from-opacity, 1)) var(--un-gradient-from-position);
    --un-gradient-to-position: 100%;
    --un-gradient-to: rgba(255, 83, 19, 0) var(--un-gradient-to-position);
    --un-gradient-stops: var(--un-gradient-from), var(--un-gradient-to);
}

.from-black {
    --un-gradient-from-position: 0%;
    --un-gradient-from: rgba(0, 0, 0, var(--un-from-opacity, 1)) var(--un-gradient-from-position);
    --un-gradient-to-position: 100%;
    --un-gradient-to: rgba(0, 0, 0, 0) var(--un-gradient-to-position);
    --un-gradient-stops: var(--un-gradient-from), var(--un-gradient-to);
}

.from-primary-opacity-20 {
    --un-gradient-from-position: 0%;
    --un-gradient-from: var(--std-primary-color-opacity-20) var(--un-gradient-from-position);
    --un-gradient-to-position: 100%;
    --un-gradient-to: rgba(255, 255, 255, 0) var(--un-gradient-to-position);
    --un-gradient-stops: var(--un-gradient-from), var(--un-gradient-to);
}

.to-_bl__h_DCAE6F_br_ {
    --un-gradient-to-position: 100%;
    --un-gradient-to: rgba(220, 174, 111, var(--un-to-opacity, 1)) var(--un-gradient-to-position);
}

.to-_bl__h_F5F5F5_br_ {
    --un-gradient-to-position: 100%;
    --un-gradient-to: rgba(245, 245, 245, var(--un-to-opacity, 1)) var(--un-gradient-to-position);
}

.to-_bl__h_FFF_br_,.to-_h_fff {
    --un-gradient-to-position: 100%;
    --un-gradient-to: rgba(255, 255, 255, var(--un-to-opacity, 1)) var(--un-gradient-to-position);
}

.to-_h_feaa2d {
    --un-gradient-to-position: 100%;
    --un-gradient-to: rgba(254, 170, 45, var(--un-to-opacity, 1)) var(--un-gradient-to-position);
}

.to-primary-opacity-10 {
    --un-gradient-to-position: 100%;
    --un-gradient-to: var(--std-primary-color-opacity-10) var(--un-gradient-to-position);
}

.to-primary-opacity-20 {
    --un-gradient-to-position: 100%;
    --un-gradient-to: var(--std-primary-color-opacity-20) var(--un-gradient-to-position);
}

.to-transparent {
    --un-gradient-to-position: 100%;
    --un-gradient-to: transparent var(--un-gradient-to-position);
}

.bg-gradient-linear {
    background-image: linear-gradient(var(--un-gradient, var(--un-gradient-stops, rgba(255, 255, 255, 0))));
}

.bg-gradient-to-b {
    --un-gradient-shape: to bottom;
    --un-gradient: var(--un-gradient-shape), var(--un-gradient-stops);
    background-image: linear-gradient(var(--un-gradient));
}

.bg-gradient-to-br {
    --un-gradient-shape: to bottom right;
    --un-gradient: var(--un-gradient-shape), var(--un-gradient-stops);
    background-image: linear-gradient(var(--un-gradient));
}

.bg-gradient-to-l {
    --un-gradient-shape: to left;
    --un-gradient: var(--un-gradient-shape), var(--un-gradient-stops);
    background-image: linear-gradient(var(--un-gradient));
}

.bg-gradient-to-r {
    --un-gradient-shape: to right;
    --un-gradient: var(--un-gradient-shape), var(--un-gradient-stops);
    background-image: linear-gradient(var(--un-gradient));
}

.bg-gradient-to-t {
    --un-gradient-shape: to top;
    --un-gradient: var(--un-gradient-shape), var(--un-gradient-stops);
    background-image: linear-gradient(var(--un-gradient));
}

.bg-none {
    background-image: none;
}

.bg-none_i_ {
    background-image: none !important;
}

.bg-bottom {
    background-position: bottom;
}

.bg-center-center {
    background-position: center center;
}

.bg-left-center {
    background-position: left center;
}

.bg-top-center {
    background-position: top center;
}

.bg-no-repeat {
    background-repeat: no-repeat;
}

.p-0,.p-0rpx {
    padding: 0;
}

.p-0rpx_i_ {
    padding: 0 !important;
}

.p-1,.p-1rpx,.p1 {
    padding: 1rpx;
}

.p-10,.p-10rpx,.p10 {
    padding: 10rpx;
}

.p-12,.p12 {
    padding: 12rpx;
}

.p-14rpx {
    padding: 14rpx;
}

.p-16,.p-16rpx {
    padding: 16rpx;
}

.p-20,.p-20rpx,.p20,.p20rpx {
    padding: 20rpx;
}

.p-21 {
    padding: 21rpx;
}

.p-23 {
    padding: 23rpx;
}

.p-24,.p-24rpx,.p24 {
    padding: 24rpx;
}

.p-24rpx_i_ {
    padding: 24rpx !important;
}

.p-25rpx,.p25 {
    padding: 25rpx;
}

.p-28,.p-28rpx {
    padding: 28rpx;
}

.p-30,.p-30rpx {
    padding: 30rpx;
}

.p-32,.p-32rpx {
    padding: 32rpx;
}

.p-33 {
    padding: 33rpx;
}

.p-34,.p-34rpx {
    padding: 34rpx;
}

.p-35 {
    padding: 35rpx;
}

.p-40,.p-40rpx {
    padding: 40rpx;
}

.p-44 {
    padding: 44rpx;
}

.p-5 {
    padding: 5rpx;
}

.p-54rpx {
    padding: 54rpx;
}

.p-6rpx {
    padding: 6rpx;
}

.p-8 {
    padding: 8rpx;
}

.p50 {
    padding: 50rpx;
}

._i_px-0 {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

._i_px-24 {
    padding-left: 24rpx !important;
    padding-right: 24rpx !important;
}

._i_py-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}

.p-x-20,.px-20,.px-20rpx,.px20 {
    padding-left: 20rpx;
    padding-right: 20rpx;
}

.p-x-24,.p-x-24rpx,.px-24,.px-24rpx,.px24 {
    padding-left: 24rpx;
    padding-right: 24rpx;
}

.p-x-32,.p-x-32rpx,.px-32,.px-32rpx {
    padding-left: 32rpx;
    padding-right: 32rpx;
}

.p-y-16rpx,.py-16,.py-16rpx,.py16 {
    padding-top: 16rpx;
    padding-bottom: 16rpx;
}

.p-y-4,.py-4,.py-4rpx {
    padding-top: 4rpx;
    padding-bottom: 4rpx;
}

.px-_bl_40rpx_br_,.px-40,.px-40rpx,.px40 {
    padding-left: 40rpx;
    padding-right: 40rpx;
}

.px-0,.px-0rpx {
    padding-left: 0;
    padding-right: 0;
}

.px-10,.px-10rpx,.px10 {
    padding-left: 10rpx;
    padding-right: 10rpx;
}

.px-10px {
    padding-left: 10px;
    padding-right: 10px;
}

.px-110rpx {
    padding-left: 110rpx;
    padding-right: 110rpx;
}

.px-12,.px-12rpx {
    padding-left: 12rpx;
    padding-right: 12rpx;
}

.px-14,.px-14rpx {
    padding-left: 14rpx;
    padding-right: 14rpx;
}

.px-15,.px-15rpx {
    padding-left: 15rpx;
    padding-right: 15rpx;
}

.px-15px {
    padding-left: 15px;
    padding-right: 15px;
}

.px-16,.px-16rpx,.px16 {
    padding-left: 16rpx;
    padding-right: 16rpx;
}

.px-16_i_ {
    padding-left: 16rpx !important;
    padding-right: 16rpx !important;
}

.px-18,.px-18rpx {
    padding-left: 18rpx;
    padding-right: 18rpx;
}

.px-22,.px-22rpx,.px22 {
    padding-left: 22rpx;
    padding-right: 22rpx;
}

.px-23 {
    padding-left: 23rpx;
    padding-right: 23rpx;
}

.px-25,.px-25rpx {
    padding-left: 25rpx;
    padding-right: 25rpx;
}

.px-26,.px-26rpx {
    padding-left: 26rpx;
    padding-right: 26rpx;
}

.px-27rpx {
    padding-left: 27rpx;
    padding-right: 27rpx;
}

.px-28,.px-28rpx,.px28 {
    padding-left: 28rpx;
    padding-right: 28rpx;
}

.px-29rpx {
    padding-left: 29rpx;
    padding-right: 29rpx;
}

.px-30,.px-30rpx,.px30 {
    padding-left: 30rpx;
    padding-right: 30rpx;
}

.px-31rpx {
    padding-left: 31rpx;
    padding-right: 31rpx;
}

.px-33 {
    padding-left: 33rpx;
    padding-right: 33rpx;
}

.px-34,.px-34rpx {
    padding-left: 34rpx;
    padding-right: 34rpx;
}

.px-35 {
    padding-left: 35rpx;
    padding-right: 35rpx;
}

.px-36,.px-36rpx {
    padding-left: 36rpx;
    padding-right: 36rpx;
}

.px-4,.px-4rpx,.px4 {
    padding-left: 4rpx;
    padding-right: 4rpx;
}

.px-44,.px-44rpx {
    padding-left: 44rpx;
    padding-right: 44rpx;
}

.px-46,.px-46rpx {
    padding-left: 46rpx;
    padding-right: 46rpx;
}

.px-47,.px-47rpx {
    padding-left: 47rpx;
    padding-right: 47rpx;
}

.px-48rpx {
    padding-left: 48rpx;
    padding-right: 48rpx;
}

.px-50rpx {
    padding-left: 50rpx;
    padding-right: 50rpx;
}

.px-52rpx {
    padding-left: 52rpx;
    padding-right: 52rpx;
}

.px-54rpx {
    padding-left: 54rpx;
    padding-right: 54rpx;
}

.px-55rpx {
    padding-left: 55rpx;
    padding-right: 55rpx;
}

.px-56,.px-56rpx {
    padding-left: 56rpx;
    padding-right: 56rpx;
}

.px-5rpx,.px5 {
    padding-left: 5rpx;
    padding-right: 5rpx;
}

.px-6,.px-6rpx,.px6 {
    padding-left: 6rpx;
    padding-right: 6rpx;
}

.px-60rpx {
    padding-left: 60rpx;
    padding-right: 60rpx;
}

.px-63rpx {
    padding-left: 63rpx;
    padding-right: 63rpx;
}

.px-64 {
    padding-left: 64rpx;
    padding-right: 64rpx;
}

.px-68rpx {
    padding-left: 68rpx;
    padding-right: 68rpx;
}

.px-7 {
    padding-left: 7rpx;
    padding-right: 7rpx;
}

.px-70rpx {
    padding-left: 70rpx;
    padding-right: 70rpx;
}

.px-74rpx {
    padding-left: 74rpx;
    padding-right: 74rpx;
}

.px-76rpx {
    padding-left: 76rpx;
    padding-right: 76rpx;
}

.px-8,.px-8rpx,.px8 {
    padding-left: 8rpx;
    padding-right: 8rpx;
}

.px-80 {
    padding-left: 80rpx;
    padding-right: 80rpx;
}

.px-88 {
    padding-left: 88rpx;
    padding-right: 88rpx;
}

.px-9 {
    padding-left: 9rpx;
    padding-right: 9rpx;
}

.px-auto {
    padding-left: auto;
    padding-right: auto;
}

.px17 {
    padding-left: 17rpx;
    padding-right: 17rpx;
}

.py-_bl_3vh_br_ {
    padding-top: 3vh;
    padding-bottom: 3vh;
}

.py-0,.py-0rpx {
    padding-top: 0;
    padding-bottom: 0;
}

.py-1,.py-1rpx {
    padding-top: 1rpx;
    padding-bottom: 1rpx;
}

.py-10,.py-10rpx {
    padding-top: 10rpx;
    padding-bottom: 10rpx;
}

.py-12,.py-12rpx,.py12 {
    padding-top: 12rpx;
    padding-bottom: 12rpx;
}

.py-14,.py-14rpx,.py14 {
    padding-top: 14rpx;
    padding-bottom: 14rpx;
}

.py-15 {
    padding-top: 15rpx;
    padding-bottom: 15rpx;
}

.py-18,.py-18rpx,.py18 {
    padding-top: 18rpx;
    padding-bottom: 18rpx;
}

.py-1px,.py-px {
    padding-top: 1px;
    padding-bottom: 1px;
}

.py-2,.py-2rpx {
    padding-top: 2rpx;
    padding-bottom: 2rpx;
}

.py-20,.py-20rpx {
    padding-top: 20rpx;
    padding-bottom: 20rpx;
}

.py-22,.py-22rpx,.py22 {
    padding-top: 22rpx;
    padding-bottom: 22rpx;
}

.py-24,.py-24rpx,.py24 {
    padding-top: 24rpx;
    padding-bottom: 24rpx;
}

.py-246rpx {
    padding-top: 246rpx;
    padding-bottom: 246rpx;
}

.py-25rpx {
    padding-top: 25rpx;
    padding-bottom: 25rpx;
}

.py-26,.py-26rpx {
    padding-top: 26rpx;
    padding-bottom: 26rpx;
}

.py-268 {
    padding-top: 268rpx;
    padding-bottom: 268rpx;
}

.py-28,.py-28rpx,.py28 {
    padding-top: 28rpx;
    padding-bottom: 28rpx;
}

.py-29 {
    padding-top: 29rpx;
    padding-bottom: 29rpx;
}

.py-2px {
    padding-top: 2px;
    padding-bottom: 2px;
}

.py-3,.py3 {
    padding-top: 3rpx;
    padding-bottom: 3rpx;
}

.py-30,.py-30rpx {
    padding-top: 30rpx;
    padding-bottom: 30rpx;
}

.py-32,.py-32rpx,.py32 {
    padding-top: 32rpx;
    padding-bottom: 32rpx;
}

.py-34rpx {
    padding-top: 34rpx;
    padding-bottom: 34rpx;
}

.py-35 {
    padding-top: 35rpx;
    padding-bottom: 35rpx;
}

.py-36,.py36 {
    padding-top: 36rpx;
    padding-bottom: 36rpx;
}

.py-38,.py-38rpx {
    padding-top: 38rpx;
    padding-bottom: 38rpx;
}

.py-40,.py-40rpx {
    padding-top: 40rpx;
    padding-bottom: 40rpx;
}

.py-42,.py-42rpx {
    padding-top: 42rpx;
    padding-bottom: 42rpx;
}

.py-45rpx {
    padding-top: 45rpx;
    padding-bottom: 45rpx;
}

.py-47rpx {
    padding-top: 47rpx;
    padding-bottom: 47rpx;
}

.py-48,.py-48rpx {
    padding-top: 48rpx;
    padding-bottom: 48rpx;
}

.py-50,.py-50rpx {
    padding-top: 50rpx;
    padding-bottom: 50rpx;
}

.py-5px {
    padding-top: 5px;
    padding-bottom: 5px;
}

.py-6,.py-6rpx,.py6 {
    padding-top: 6rpx;
    padding-bottom: 6rpx;
}

.py-60,.py-60rpx {
    padding-top: 60rpx;
    padding-bottom: 60rpx;
}

.py-7 {
    padding-top: 7rpx;
    padding-bottom: 7rpx;
}

.py-70rpx {
    padding-top: 70rpx;
    padding-bottom: 70rpx;
}

.py-8,.py-8rpx {
    padding-top: 8rpx;
    padding-bottom: 8rpx;
}

._i_pb-30 {
    padding-bottom: 30rpx !important;
}

._i_pt-250 {
    padding-top: 250rpx !important;
}

._i_pt-440 {
    padding-top: 440rpx !important;
}

._i_pt-660 {
    padding-top: 660rpx !important;
}

.not-first-pl-10rpx:not(:first-child),.p-l-10,.pl-10,.pl-10rpx {
    padding-left: 10rpx;
}

.p-b-16,.pb-16,.pb-16rpx {
    padding-bottom: 16rpx;
}

.p-b-18,.pb-18,.pb-18rpx {
    padding-bottom: 18rpx;
}

.p-b-22,.pb-22,.pb-22rpx {
    padding-bottom: 22rpx;
}

.p-b-40,.pb-40,.pb-40rpx {
    padding-bottom: 40rpx;
}

.p-b-42,.pb-42rpx {
    padding-bottom: 42rpx;
}

.p-b-6,.pb-6rpx {
    padding-bottom: 6rpx;
}

.p-l-13 {
    padding-left: 13rpx;
}

.p-l-24,.pl-24,.pl-24rpx {
    padding-left: 24rpx;
}

.p-l-30,.pl-30,.pl-30rpx,.pl30 {
    padding-left: 30rpx;
}

.p-l-70 {
    padding-left: 70rpx;
}

.p-r-10,.pr-10,.pr-10rpx,.pr10 {
    padding-right: 10rpx;
}

.p-r-13 {
    padding-right: 13rpx;
}

.p-r-30,.pr-30,.pr-30rpx {
    padding-right: 30rpx;
}

.p-r-70 {
    padding-right: 70rpx;
}

.p-t-18,.pt-18rpx {
    padding-top: 18rpx;
}

.p-t-36,.pt-36,.pt-36rpx {
    padding-top: 36rpx;
}

.p-t-40rpx,.pt-40,.pt-40rpx,.pt40 {
    padding-top: 40rpx;
}

.p-t-42,.pt-42,.pt-42rpx {
    padding-top: 42rpx;
}

.p-t-52,.pt-52 {
    padding-top: 52rpx;
}

.p-t-6,.pt-6,.pt-6rpx,.pt6 {
    padding-top: 6rpx;
}

.pb-_bl_calc_pl_-20rpx_a_constant_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(-20rpx + constant(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_-20rpx_a_env_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(-20rpx + env(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_100rpx_a_constant_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(100rpx + constant(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_100rpx_a_env_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_116rpx_a_constant_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(116rpx + constant(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_116rpx_a_env_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(116rpx + env(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_120rpx_a_constant_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(120rpx + constant(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_120rpx_a_env_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_128rpx_a_env_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(128rpx + env(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_130rpx_a_constant_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(130rpx + constant(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_130rpx_a_env_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(130rpx + env(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_136rpx_a_constant_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(136rpx + constant(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_136rpx_a_env_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(136rpx + env(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_150rpx_a_constant_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(150rpx + constant(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_150rpx_a_env_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(150rpx + env(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_20rpx_a_constant_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_20rpx_a_constant_pl_safe-area-inset-top_pr__pr__br_ {
    padding-bottom: calc(20rpx + constant(safe-area-inset-top));
}

.pb-_bl_calc_pl_20rpx_a_env_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_20rpx_a_env_pl_safe-area-inset-top_pr__pr__br_ {
    padding-bottom: calc(20rpx + env(safe-area-inset-top));
}

.pb-_bl_calc_pl_24rpx_a_constant_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(24rpx + constant(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_24rpx_a_env_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_30rpx_a_constant_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(30rpx + constant(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_30rpx_a_env_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_38rpx_a_constant_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(38rpx + constant(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_38rpx_a_env_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(38rpx + env(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_80rpx__a__constant_pl_safe-area-inset-bottom_pr__pr__br_,.pb-_bl_calc_pl_80rpx_a_constant_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(80rpx + constant(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_80rpx__a__env_pl_safe-area-inset-bottom_pr__pr__br_,.pb-_bl_calc_pl_80rpx_a_env_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(80rpx + env(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_90rpx_a_constant_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(90rpx + constant(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_90rpx_a_env_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(90rpx + env(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_constant_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(constant(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_constant_pl_safe-area-inset-bottom_pr__s_2_pr__br_ {
    padding-bottom: calc(constant(safe-area-inset-bottom) / 2);
}

.pb-_bl_calc_pl_env_pl_safe-area-inset-bottom_pr__pr__br_ {
    padding-bottom: calc(env(safe-area-inset-bottom));
}

.pb-_bl_calc_pl_env_pl_safe-area-inset-bottom_pr__s_2_pr__br_ {
    padding-bottom: calc(env(safe-area-inset-bottom) / 2);
}

.pb-_bl_constant_pl_safe-area-inset-bottom_pr__br_ {
    padding-bottom: constant(safe-area-inset-bottom);
}

.pb-_bl_constant_pl_safe-area-inset-bottom_pr__br__i_ {
    padding-bottom: constant(safe-area-inset-bottom) !important;
}

.pb-_bl_env_pl_safe-area-inset-bottom_pr__br_ {
    padding-bottom: env(safe-area-inset-bottom);
}

.pb-_bl_env_pl_safe-area-inset-bottom_pr__br__i_ {
    padding-bottom: env(safe-area-inset-bottom) !important;
}

.pb-0,.pb-0rpx {
    padding-bottom: 0;
}

.pb-0_i_ {
    padding-bottom: 0 !important;
}

.pb-1,.pb-1rpx {
    padding-bottom: 1rpx;
}

.pb-10,.pb-10rpx {
    padding-bottom: 10rpx;
}

.pb-100rpx {
    padding-bottom: 100rpx;
}

.pb-11 {
    padding-bottom: 11rpx;
}

.pb-114rpx {
    padding-bottom: 114rpx;
}

.pb-12,.pb-12rpx,.pb12 {
    padding-bottom: 12rpx;
}

.pb-120,.pb-120rpx {
    padding-bottom: 120rpx;
}

.pb-14rpx {
    padding-bottom: 14rpx;
}

.pb-150rpx {
    padding-bottom: 150rpx;
}

.pb-15rpx {
    padding-bottom: 15rpx;
}

.pb-160rpx {
    padding-bottom: 160rpx;
}

.pb-170 {
    padding-bottom: 170rpx;
}

.pb-185 {
    padding-bottom: 185rpx;
}

.pb-1px {
    padding-bottom: 1px;
}

.pb-2,.pb-2rpx {
    padding-bottom: 2rpx;
}

.pb-20,.pb-20rpx,.pb20 {
    padding-bottom: 20rpx;
}

.pb-200 {
    padding-bottom: 200rpx;
}

.pb-21 {
    padding-bottom: 21rpx;
}

.pb-220,.pb-220rpx {
    padding-bottom: 220rpx;
}

.pb-23,.pb-23rpx {
    padding-bottom: 23rpx;
}

.pb-24,.pb-24rpx,.pb24 {
    padding-bottom: 24rpx;
}

.pb-24rpx_i_ {
    padding-bottom: 24rpx !important;
}

.pb-25,.pb-25rpx {
    padding-bottom: 25rpx;
}

.pb-26rpx {
    padding-bottom: 26rpx;
}

.pb-27 {
    padding-bottom: 27rpx;
}

.pb-28,.pb-28rpx {
    padding-bottom: 28rpx;
}

.pb-30,.pb-30rpx,.pb30 {
    padding-bottom: 30rpx;
}

.pb-32,.pb-32rpx {
    padding-bottom: 32rpx;
}

.pb-33,.pb33 {
    padding-bottom: 33rpx;
}

.pb-34,.pb-34rpx {
    padding-bottom: 34rpx;
}

.pb-35,.pb-35rpx {
    padding-bottom: 35rpx;
}

.pb-36,.pb-36rpx,.pb36 {
    padding-bottom: 36rpx;
}

.pb-38rpx {
    padding-bottom: 38rpx;
}

.pb-38rpx_i_ {
    padding-bottom: 38rpx !important;
}

.pb-4,.pb-4rpx {
    padding-bottom: 4rpx;
}

.pb-43 {
    padding-bottom: 43rpx;
}

.pb-46rpx {
    padding-bottom: 46rpx;
}

.pb-48,.pb-48rpx {
    padding-bottom: 48rpx;
}

.pb-4px {
    padding-bottom: 4px;
}

.pb-5 {
    padding-bottom: 5rpx;
}

.pb-50rpx {
    padding-bottom: 50rpx;
}

.pb-56rpx {
    padding-bottom: 56rpx;
}

.pb-60,.pb-60rpx {
    padding-bottom: 60rpx;
}

.pb-62rpx {
    padding-bottom: 62rpx;
}

.pb-64,.pb-64rpx {
    padding-bottom: 64rpx;
}

.pb-640rpx {
    padding-bottom: 640rpx;
}

.pb-68rpx {
    padding-bottom: 68rpx;
}

.pb-72rpx {
    padding-bottom: 72rpx;
}

.pb-74rpx {
    padding-bottom: 74rpx;
}

.pb-76rpx {
    padding-bottom: 76rpx;
}

.pb-8,.pb-8rpx {
    padding-bottom: 8rpx;
}

.pb-80rpx {
    padding-bottom: 80rpx;
}

.pb-85 {
    padding-bottom: 85rpx;
}

.pb-9 {
    padding-bottom: 9rpx;
}

.pb-90rpx {
    padding-bottom: 90rpx;
}

.pl-0 {
    padding-left: 0;
}

.pl-12,.pl-12rpx {
    padding-left: 12rpx;
}

.pl-14,.pl-14rpx {
    padding-left: 14rpx;
}

.pl-15 {
    padding-left: 15rpx;
}

.pl-16,.pl-16rpx {
    padding-left: 16rpx;
}

.pl-160 {
    padding-left: 160rpx;
}

.pl-168 {
    padding-left: 168rpx;
}

.pl-18,.pl-18rpx {
    padding-left: 18rpx;
}

.pl-20,.pl-20rpx {
    padding-left: 20rpx;
}

.pl-22,.pl-22rpx {
    padding-left: 22rpx;
}

.pl-25rpx {
    padding-left: 25rpx;
}

.pl-26,.pl-26rpx {
    padding-left: 26rpx;
}

.pl-27 {
    padding-left: 27rpx;
}

.pl-28,.pl-28rpx {
    padding-left: 28rpx;
}

.pl-29 {
    padding-left: 29rpx;
}

.pl-300 {
    padding-left: 300rpx;
}

.pl-308rpx {
    padding-left: 308rpx;
}

.pl-31rpx {
    padding-left: 31rpx;
}

.pl-32,.pl-32rpx {
    padding-left: 32rpx;
}

.pl-33 {
    padding-left: 33rpx;
}

.pl-34,.pl-34rpx {
    padding-left: 34rpx;
}

.pl-35 {
    padding-left: 35rpx;
}

.pl-36rpx {
    padding-left: 36rpx;
}

.pl-38rpx {
    padding-left: 38rpx;
}

.pl-4,.pl-4rpx {
    padding-left: 4rpx;
}

.pl-40,.pl-40rpx {
    padding-left: 40rpx;
}

.pl-42,.pl-42rpx {
    padding-left: 42rpx;
}

.pl-44,.pl-44rpx {
    padding-left: 44rpx;
}

.pl-45rpx {
    padding-left: 45rpx;
}

.pl-50rpx {
    padding-left: 50rpx;
}

.pl-52rpx {
    padding-left: 52rpx;
}

.pl-69 {
    padding-left: 69rpx;
}

.pl-6rpx {
    padding-left: 6rpx;
}

.pl-8,.pl-8rpx,.pl8 {
    padding-left: 8rpx;
}

.pl-92 {
    padding-left: 92rpx;
}

.pl48 {
    padding-left: 48rpx;
}

.pr-0,.pr-0rpx,.pr0 {
    padding-right: 0;
}

.pr-100rpx {
    padding-right: 100rpx;
}

.pr-110 {
    padding-right: 110rpx;
}

.pr-12,.pr-12rpx {
    padding-right: 12rpx;
}

.pr-14rpx {
    padding-right: 14rpx;
}

.pr-150 {
    padding-right: 150rpx;
}

.pr-16,.pr-16rpx {
    padding-right: 16rpx;
}

.pr-18,.pr-18rpx {
    padding-right: 18rpx;
}

.pr-20,.pr-20rpx {
    padding-right: 20rpx;
}

.pr-24,.pr-24rpx,.pr24 {
    padding-right: 24rpx;
}

.pr-240 {
    padding-right: 240rpx;
}

.pr-25,.pr-25rpx {
    padding-right: 25rpx;
}

.pr-26 {
    padding-right: 26rpx;
}

.pr-27rpx {
    padding-right: 27rpx;
}

.pr-28,.pr-28rpx {
    padding-right: 28rpx;
}

.pr-32,.pr-32rpx {
    padding-right: 32rpx;
}

.pr-34 {
    padding-right: 34rpx;
}

.pr-35,.pr-35rpx {
    padding-right: 35rpx;
}

.pr-36,.pr-36rpx {
    padding-right: 36rpx;
}

.pr-38rpx {
    padding-right: 38rpx;
}

.pr-4,.pr-4rpx {
    padding-right: 4rpx;
}

.pr-40,.pr-40rpx {
    padding-right: 40rpx;
}

.pr-44rpx {
    padding-right: 44rpx;
}

.pr-45rpx {
    padding-right: 45rpx;
}

.pr-48 {
    padding-right: 48rpx;
}

.pr-50,.pr-50rpx {
    padding-right: 50rpx;
}

.pr-54rpx {
    padding-right: 54rpx;
}

.pr-5rpx {
    padding-right: 5rpx;
}

.pr-6,.pr-6rpx {
    padding-right: 6rpx;
}

.pr-68 {
    padding-right: 68rpx;
}

.pr-76rpx {
    padding-right: 76rpx;
}

.pr-78rpx {
    padding-right: 78rpx;
}

.pr-8,.pr-8rpx,.pr8 {
    padding-right: 8rpx;
}

.pr-82 {
    padding-right: 82rpx;
}

.pr-9 {
    padding-right: 9rpx;
}

.pr80 {
    padding-right: 80rpx;
}

.pt-_bl_60rpx_br_,.pt-60rpx {
    padding-top: 60rpx;
}

.pt-0,.pt-0rpx {
    padding-top: 0;
}

.pt-10,.pt-10rpx {
    padding-top: 10rpx;
}

.pt-100,.pt-100rpx {
    padding-top: 100rpx;
}

.pt-10vh {
    padding-top: 10vh;
}

.pt-110,.pt-110rpx {
    padding-top: 110rpx;
}

.pt-112,.pt-112rpx {
    padding-top: 112rpx;
}

.pt-115,.pt-115rpx {
    padding-top: 115rpx;
}

.pt-12,.pt12 {
    padding-top: 12rpx;
}

.pt-120,.pt-120rpx {
    padding-top: 120rpx;
}

.pt-120px {
    padding-top: 120px;
}

.pt-130 {
    padding-top: 130rpx;
}

.pt-14 {
    padding-top: 14rpx;
}

.pt-142rpx {
    padding-top: 142rpx;
}

.pt-156 {
    padding-top: 156rpx;
}

.pt-16,.pt-16rpx {
    padding-top: 16rpx;
}

.pt-170rpx_i_ {
    padding-top: 170rpx !important;
}

.pt-175rpx {
    padding-top: 175rpx;
}

.pt-180rpx {
    padding-top: 180rpx;
}

.pt-184 {
    padding-top: 184rpx;
}

.pt-198rpx {
    padding-top: 198rpx;
}

.pt-1px {
    padding-top: 1px;
}

.pt-1rpx {
    padding-top: 1rpx;
}

.pt-20,.pt-20rpx,.pt20 {
    padding-top: 20rpx;
}

.pt-200rpx {
    padding-top: 200rpx;
}

.pt-21 {
    padding-top: 21rpx;
}

.pt-220 {
    padding-top: 220rpx;
}

.pt-22rpx {
    padding-top: 22rpx;
}

.pt-23 {
    padding-top: 23rpx;
}

.pt-230 {
    padding-top: 230rpx;
}

.pt-24,.pt-24rpx,.pt24 {
    padding-top: 24rpx;
}

.pt-240 {
    padding-top: 240rpx;
}

.pt-246rpx {
    padding-top: 246rpx;
}

.pt-25 {
    padding-top: 25rpx;
}

.pt-26rpx {
    padding-top: 26rpx;
}

.pt-27 {
    padding-top: 27rpx;
}

.pt-270 {
    padding-top: 270rpx;
}

.pt-28,.pt-28rpx,.pt28 {
    padding-top: 28rpx;
}

.pt-280 {
    padding-top: 280rpx;
}

.pt-29rpx {
    padding-top: 29rpx;
}

.pt-3 {
    padding-top: 3rpx;
}

.pt-30,.pt-30rpx,.pt30 {
    padding-top: 30rpx;
}

.pt-300 {
    padding-top: 300rpx;
}

.pt-31rpx {
    padding-top: 31rpx;
}

.pt-32,.pt-32rpx,.pt32 {
    padding-top: 32rpx;
}

.pt-33rpx {
    padding-top: 33rpx;
}

.pt-34,.pt-34rpx {
    padding-top: 34rpx;
}

.pt-340rpx {
    padding-top: 340rpx;
}

.pt-35 {
    padding-top: 35rpx;
}

.pt-37rpx {
    padding-top: 37rpx;
}

.pt-38,.pt-38rpx {
    padding-top: 38rpx;
}

.pt-380,.pt-380rpx {
    padding-top: 380rpx;
}

.pt-4,.pt-4rpx {
    padding-top: 4rpx;
}

.pt-44,.pt-44rpx {
    padding-top: 44rpx;
}

.pt-46rpx {
    padding-top: 46rpx;
}

.pt-47rpx {
    padding-top: 47rpx;
}

.pt-48 {
    padding-top: 48rpx;
}

.pt-50,.pt-50rpx {
    padding-top: 50rpx;
}

.pt-54rpx {
    padding-top: 54rpx;
}

.pt-56rpx {
    padding-top: 56rpx;
}

.pt-580rpx {
    padding-top: 580rpx;
}

.pt-600,.pt-600rpx {
    padding-top: 600rpx;
}

.pt-64rpx {
    padding-top: 64rpx;
}

.pt-7 {
    padding-top: 7rpx;
}

.pt-70,.pt-70rpx {
    padding-top: 70rpx;
}

.pt-700rpx {
    padding-top: 700rpx;
}

.pt-72rpx {
    padding-top: 72rpx;
}

.pt-730rpx {
    padding-top: 730rpx;
}

.pt-78 {
    padding-top: 78rpx;
}

.pt-8,.pt-8rpx {
    padding-top: 8rpx;
}

.pt-80 {
    padding-top: 80rpx;
}

.pt-84 {
    padding-top: 84rpx;
}

.pt-90rpx {
    padding-top: 90rpx;
}

.first-pb-8rpx:first-child {
    padding-bottom: 8rpx;
}

.first-pt-28rpx:first-child {
    padding-top: 28rpx;
}

.first-pt-36rpx:first-child {
    padding-top: 36rpx;
}

.last-pb-0:last-child {
    padding-bottom: 0;
}

.last-pb-0rpx:last-child {
    padding-bottom: 0;
}

.last_c_pb-0:last-child {
    padding-bottom: 0;
}

.text-center {
    text-align: center;
}

.text-center_i_ {
    text-align: center !important;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.text-right_i_ {
    text-align: right !important;
}

.text-justify {
    text-align: justify;
}

.align-baseline,.vertical-baseline {
    vertical-align: baseline;
}

.align-bottom,.v-bottom {
    vertical-align: bottom;
}

.align-middle,.v-middle,.vertical-middle {
    vertical-align: middle;
}

.align-start,.align-top,.v-top,.vertical-top {
    vertical-align: top;
}

.v-text-bottom {
    vertical-align: text-bottom;
}

._i_text-12 {
    font-size: 12rpx !important;
}

._i_text-158 {
    font-size: 158rpx !important;
}

._i_text-16,.text-16_i_ {
    font-size: 16rpx !important;
}

._i_text-20 {
    font-size: 20rpx !important;
}

._i_text-22,.text-22rpx_i_ {
    font-size: 22rpx !important;
}

._i_text-24 {
    font-size: 24rpx !important;
}

._i_text-28,.text-28rpx_i_ {
    font-size: 28rpx !important;
}

._i_text-30,.text-30_i_,.text-30rpx_i_ {
    font-size: 30rpx !important;
}

._i_text-32,.important-text-32rpx,.text-32_i_ {
    font-size: 32rpx !important;
}

._i_text-40,.text-40_i_ {
    font-size: 40rpx !important;
}

._i_text-42 {
    font-size: 42rpx !important;
}

._i_text-44 {
    font-size: 44rpx !important;
}

._i_text-48 {
    font-size: 48rpx !important;
}

._i_text-52 {
    font-size: 52rpx !important;
}

._i_text-56 {
    font-size: 56rpx !important;
}

._i_text-60 {
    font-size: 60rpx !important;
}

._i_text-66 {
    font-size: 66rpx !important;
}

._i_text-68 {
    font-size: 68rpx !important;
}

._i_text-80,.text-80_i_ {
    font-size: 80rpx !important;
}

.important-text-26rpx {
    font-size: 26rpx !important;
}

.text--32 {
    font-size: -32rpx;
}

.text-_bl_32rpx_br_,.text-32,.text-32rpx {
    font-size: 32rpx;
}

.text-0 {
    font-size: 0;
}

.text-10 {
    font-size: 10rpx;
}

.text-100 {
    font-size: 100rpx;
}

.text-12,.text-12rpx {
    font-size: 12rpx;
}

.text-120 {
    font-size: 120rpx;
}

.text-128 {
    font-size: 128rpx;
}

.text-12px {
    font-size: 12px;
}

.text-13 {
    font-size: 13rpx;
}

.text-14,.text-14rpx {
    font-size: 14rpx;
}

.text-14px {
    font-size: 14px;
}

.text-150 {
    font-size: 150rpx;
}

.text-16,.text-16rpx {
    font-size: 16rpx;
}

.text-170 {
    font-size: 170rpx;
}

.text-171 {
    font-size: 171rpx;
}

.text-18,.text-18rpx {
    font-size: 18rpx;
}

.text-19 {
    font-size: 19rpx;
}

.text-20,.text-20rpx,.text-size-20 {
    font-size: 20rpx;
}

.text-20px {
    font-size: 20px;
}

.text-21rpx {
    font-size: 21rpx;
}

.text-22,.text-22rpx {
    font-size: 22rpx;
}

.text-22px {
    font-size: 22px;
}

.text-238 {
    font-size: 238rpx;
}

.text-24,.text-24rpx,.text-size-24 {
    font-size: 24rpx;
}

.text-24px {
    font-size: 24px;
}

.text-250rpx {
    font-size: 250rpx;
}

.text-26,.text-26rpx,.text-size-26 {
    font-size: 26rpx;
}

.text-27 {
    font-size: 27rpx;
}

.text-28,.text-28rpx {
    font-size: 28rpx;
}

.text-29 {
    font-size: 29rpx;
}

.text-30,.text-30rpx,.text-size-30 {
    font-size: 30rpx;
}

.text-33 {
    font-size: 33rpx;
}

.text-34,.text-34rpx {
    font-size: 34rpx;
}

.text-36,.text-36rpx,.text-size-36 {
    font-size: 36rpx;
}

.text-36_i_ {
    font-size: 36rpx !important;
}

.text-37rpx {
    font-size: 37rpx;
}

.text-38,.text-38rpx {
    font-size: 38rpx;
}

.text-39 {
    font-size: 39rpx;
}

.text-40,.text-40rpx {
    font-size: 40rpx;
}

.text-42,.text-42rpx {
    font-size: 42rpx;
}

.text-44,.text-44rpx {
    font-size: 44rpx;
}

.text-46,.text-46rpx {
    font-size: 46rpx;
}

.text-48,.text-48rpx {
    font-size: 48rpx;
}

.text-50,.text-50rpx {
    font-size: 50rpx;
}

.text-52 {
    font-size: 52rpx;
}

.text-54,.text-54rpx {
    font-size: 54rpx;
}

.text-56,.text-56rpx {
    font-size: 56rpx;
}

.text-58rpx {
    font-size: 58rpx;
}

.text-6 {
    font-size: 6rpx;
}

.text-60,.text-60rpx,.text-size-60 {
    font-size: 60rpx;
}

.text-64,.text-64rpx {
    font-size: 64rpx;
}

.text-66rpx {
    font-size: 66rpx;
}

.text-68,.text-68rpx {
    font-size: 68rpx;
}

.text-70,.text-70rpx {
    font-size: 70rpx;
}

.text-72rpx {
    font-size: 72rpx;
}

.text-76 {
    font-size: 76rpx;
}

.text-78 {
    font-size: 78rpx;
}

.text-8 {
    font-size: 8rpx;
}

.text-80rpx {
    font-size: 80rpx;
}

.text-84 {
    font-size: 84rpx;
}

.text-88,.text-88rpx {
    font-size: 88rpx;
}

.text-90rpx {
    font-size: 90rpx;
}

.text-92rpx {
    font-size: 92rpx;
}

.text-96 {
    font-size: 96rpx;
}

.text-98rpx {
    font-size: 98rpx;
}

.first-text-26rpx:first-child {
    font-size: 26rpx;
}

.first-text-30rpx:first-child {
    font-size: 30rpx;
}

.last-text-28rpx:last-child {
    font-size: 28rpx;
}

._i_c-hex-333333,._i_text-_bl__h_333_br_ {
    --un-text-opacity: 1 !important;
    color: rgba(51, 51, 51, var(--un-text-opacity)) !important;
}

._i_text-_bl__h_636363_br_ {
    --un-text-opacity: 1 !important;
    color: rgba(99, 99, 99, var(--un-text-opacity)) !important;
}

._i_text-_bl__h_666_br_ {
    --un-text-opacity: 1 !important;
    color: rgba(102, 102, 102, var(--un-text-opacity)) !important;
}

._i_text-_bl__h_999_br_ {
    --un-text-opacity: 1 !important;
    color: rgba(153, 153, 153, var(--un-text-opacity)) !important;
}

._i_text-_bl__h_FFF_br_,.c-white_i_,.text-_bl__h_fff_br__i_,.text-_bl__h_ffffff_br__i_,.text-white_i_ {
    --un-text-opacity: 1 !important;
    color: rgba(255, 255, 255, var(--un-text-opacity)) !important;
}

._i_text-primary,.c-primary_i_ {
    color: var(--std-primary-color) !important;
}

._i_text-primary-opacity-30 {
    color: var(--std-primary-color-opacity-30) !important;
}

.c-_h_000,.c-black,.c-hex-000,.color-black,.color-hex-000,.text-_bl__h_000_br_,.text-_bl__h_000000_br_,.text-_bl_rgb_pl_0_2c_0_2c_0_pr__br_,.text-black,.text-hex-000 {
    --un-text-opacity: 1;
    color: rgba(0, 0, 0, var(--un-text-opacity));
}

.c-_h_00a47c,.c-_h_00A47C,.c-hex-00a47c,.c-hex-00A47C,.text-_bl__h_00a47c_br_,.text-_bl__h_00A47C_br_ {
    --un-text-opacity: 1;
    color: rgba(0, 164, 124, var(--un-text-opacity));
}

.text-_bl__h_00B42A_br_ {
    --un-text-opacity: 1;
    color: rgba(0, 180, 42, var(--un-text-opacity));
}

.c-hex-010101,.text-_bl__h_010101_br_,.text-_bl_rgb_pl_1_2c_1_2c_1_pr__br_,.text-hex-010101 {
    --un-text-opacity: 1;
    color: rgba(1, 1, 1, var(--un-text-opacity));
}

.text-_bl__h_0178FA_br_ {
    --un-text-opacity: 1;
    color: rgba(1, 120, 250, var(--un-text-opacity));
}

.text-_bl__h_040404_br_ {
    --un-text-opacity: 1;
    color: rgba(4, 4, 4, var(--un-text-opacity));
}

.text-_bl__h_09bb07_br_,.text-_bl__h_09BB07_br_ {
    --un-text-opacity: 1;
    color: rgba(9, 187, 7, var(--un-text-opacity));
}

.text-_bl__h_0c0c0c_br_ {
    --un-text-opacity: 1;
    color: rgba(12, 12, 12, var(--un-text-opacity));
}

.c-_h_111,.c-hex-111,.text-_bl__h_111_br_,.text-_bl__h_111111_br_,.text-hex-111 {
    --un-text-opacity: 1;
    color: rgba(17, 17, 17, var(--un-text-opacity));
}

.text-_bl__h_121212_br_ {
    --un-text-opacity: 1;
    color: rgba(18, 18, 18, var(--un-text-opacity));
}

.text-_bl__h_161013_br_ {
    --un-text-opacity: 1;
    color: rgba(22, 16, 19, var(--un-text-opacity));
}

.text-_bl__h_1b1b1b_br_ {
    --un-text-opacity: 1;
    color: rgba(27, 27, 27, var(--un-text-opacity));
}

.c-hex-222,.color-_h_222,.color-hex-222,.text-_bl__h_222_br_,.text-_bl__h_222222_br_,.text-_bl_rgb_pl_34_2c_34_2c_34_pr__br_,.text-hex-222 {
    --un-text-opacity: 1;
    color: rgba(34, 34, 34, var(--un-text-opacity));
}

.text-_bl__h_22af29_br_ {
    --un-text-opacity: 1;
    color: rgba(34, 175, 41, var(--un-text-opacity));
}

.text-_bl__h_292929_br_,.text-_bl_rgb_pl_41_2c_41_2c_41_pr__br_ {
    --un-text-opacity: 1;
    color: rgba(41, 41, 41, var(--un-text-opacity));
}

.text-_bl__h_323232_br_ {
    --un-text-opacity: 1;
    color: rgba(50, 50, 50, var(--un-text-opacity));
}

.c-_h_333,.c-_h_333333,.c-hex-333,.c-hex-333333,.color-_bl__h_333_br_,.color-_h_333,.color-hex-333,.color-hex-333333,.text-_bl__h_333_br_,.text-_bl__h_333333_br_,.text-_bl_rgb_pl_51_2c_51_2c_51_pr__br_,.text-_h_333,.text-hex-333,.text-hex-333333 {
    --un-text-opacity: 1;
    color: rgba(51, 51, 51, var(--un-text-opacity));
}

.c-_h_343434,.color-_h_343434,.text-_bl__h_343434_br_ {
    --un-text-opacity: 1;
    color: rgba(52, 52, 52, var(--un-text-opacity));
}

.text-_bl__h_36cd18_br_ {
    --un-text-opacity: 1;
    color: rgba(54, 205, 24, var(--un-text-opacity));
}

.text-_bl__h_383838_br_ {
    --un-text-opacity: 1;
    color: rgba(56, 56, 56, var(--un-text-opacity));
}

.c-hex-3D3D3D,.color-hex-3d3d3d,.color-hex-3D3D3D,.text-_bl__h_3d3d3d_br_,.text-_bl__h_3D3D3D_br_ {
    --un-text-opacity: 1;
    color: rgba(61, 61, 61, var(--un-text-opacity));
}

.text-_bl__h_444_br_ {
    --un-text-opacity: 1;
    color: rgba(68, 68, 68, var(--un-text-opacity));
}

.text-_bl__h_454545_br_ {
    --un-text-opacity: 1;
    color: rgba(69, 69, 69, var(--un-text-opacity));
}

.c-hex-464646,.text-_bl__h_464646_br_ {
    --un-text-opacity: 1;
    color: rgba(70, 70, 70, var(--un-text-opacity));
}

.c-hex-484b4d,.text-_bl__h_484b4d_br_ {
    --un-text-opacity: 1;
    color: rgba(72, 75, 77, var(--un-text-opacity));
}

.text-_bl__h_4d1300_br_ {
    --un-text-opacity: 1;
    color: rgba(77, 19, 0, var(--un-text-opacity));
}

.text-_bl__h_512C19_br_ {
    --un-text-opacity: 1;
    color: rgba(81, 44, 25, var(--un-text-opacity));
}

.text-_bl__h_555555_br_ {
    --un-text-opacity: 1;
    color: rgba(85, 85, 85, var(--un-text-opacity));
}

.text-_bl__h_55BE69_br_ {
    --un-text-opacity: 1;
    color: rgba(85, 190, 105, var(--un-text-opacity));
}

.text-_bl__h_5d6063_br_ {
    --un-text-opacity: 1;
    color: rgba(93, 96, 99, var(--un-text-opacity));
}

.c-hex-606060,.text-_bl__h_606060_br_,.text-hex-606060 {
    --un-text-opacity: 1;
    color: rgba(96, 96, 96, var(--un-text-opacity));
}

.text-_bl__h_640f00_br_ {
    --un-text-opacity: 1;
    color: rgba(100, 15, 0, var(--un-text-opacity));
}

.c-_h_666,.c-hex-666,.c-hex-666666,.color-_h_666,.color-hex-666,.color-hex-666666,.text-_bl__h_666_br_,.text-_bl__h_666666_br_,.text-_bl_rgb_pl_102_2c_102_2c_102_pr__br_,.text-_h_666,.text-hex-666,.text-hex-666666 {
    --un-text-opacity: 1;
    color: rgba(102, 102, 102, var(--un-text-opacity));
}

.text-_bl__h_686866_br_ {
    --un-text-opacity: 1;
    color: rgba(104, 104, 102, var(--un-text-opacity));
}

.text-_bl__h_6a3a06_br_ {
    --un-text-opacity: 1;
    color: rgba(106, 58, 6, var(--un-text-opacity));
}

.c-hex-6f7275,.text-_bl__h_6f7275_br_ {
    --un-text-opacity: 1;
    color: rgba(111, 114, 117, var(--un-text-opacity));
}

.c-hex-707275,.text-_bl__h_707275_br_ {
    --un-text-opacity: 1;
    color: rgba(112, 114, 117, var(--un-text-opacity));
}

.text-_bl__h_717171_br_ {
    --un-text-opacity: 1;
    color: rgba(113, 113, 113, var(--un-text-opacity));
}

.text-_bl__h_737373_br__i_ {
    --un-text-opacity: 1 !important;
    color: rgba(115, 115, 115, var(--un-text-opacity)) !important;
}

.c-hex-767676,.text-_bl__h_767676_br_ {
    --un-text-opacity: 1;
    color: rgba(118, 118, 118, var(--un-text-opacity));
}

.c-hex-777,.color-hex-777777,.text-_bl__h_777_br_,.text-_bl__h_777777_br_,.text-hex-777,.text-hex-777777 {
    --un-text-opacity: 1;
    color: rgba(119, 119, 119, var(--un-text-opacity));
}

.text-_bl__h_7b7e80_br_ {
    --un-text-opacity: 1;
    color: rgba(123, 126, 128, var(--un-text-opacity));
}

.text-_bl__h_7C7C7C_br_ {
    --un-text-opacity: 1;
    color: rgba(124, 124, 124, var(--un-text-opacity));
}

.c-hex-7e7e7e,.text-_bl__h_7e7e7e_br_ {
    --un-text-opacity: 1;
    color: rgba(126, 126, 126, var(--un-text-opacity));
}

.text-_bl__h_80cee6_br_ {
    --un-text-opacity: 1;
    color: rgba(128, 206, 230, var(--un-text-opacity));
}

.text-_bl__h_818181_br_ {
    --un-text-opacity: 1;
    color: rgba(129, 129, 129, var(--un-text-opacity));
}

.text-_bl__h_828282_br_ {
    --un-text-opacity: 1;
    color: rgba(130, 130, 130, var(--un-text-opacity));
}

.text-_bl__h_83858b_br_ {
    --un-text-opacity: 1;
    color: rgba(131, 133, 139, var(--un-text-opacity));
}

.text-_bl__h_868686_br_ {
    --un-text-opacity: 1;
    color: rgba(134, 134, 134, var(--un-text-opacity));
}

.c-hex-888,.text-_bl__h_888_br_,.text-hex-888 {
    --un-text-opacity: 1;
    color: rgba(136, 136, 136, var(--un-text-opacity));
}

.text-_bl__h_898989_br_ {
    --un-text-opacity: 1;
    color: rgba(137, 137, 137, var(--un-text-opacity));
}

.text-_bl__h_8a8a8a_br_ {
    --un-text-opacity: 1;
    color: rgba(138, 138, 138, var(--un-text-opacity));
}

.text-_bl__h_8e8e8e_br_ {
    --un-text-opacity: 1;
    color: rgba(142, 142, 142, var(--un-text-opacity));
}

.text-_bl__h_929496_br_ {
    --un-text-opacity: 1;
    color: rgba(146, 148, 150, var(--un-text-opacity));
}

.text-_bl__h_949494_br_ {
    --un-text-opacity: 1;
    color: rgba(148, 148, 148, var(--un-text-opacity));
}

.c-hex-959595,.text-_bl__h_959595_br_ {
    --un-text-opacity: 1;
    color: rgba(149, 149, 149, var(--un-text-opacity));
}

.c-_h_999,.c-_h_999999,.c-hex-999,.c-hex-999999,.color-_h_999,.color-_h_999999,.color-hex-999,.color-hex-999999,.text-_bl__h_999_br_,.text-_bl__h_999999_br_,.text-_bl_rgb_pl_153_2c_153_2c_153_pr__br_,.text-_h_999,.text-hex-999,.text-hex-999999 {
    --un-text-opacity: 1;
    color: rgba(153, 153, 153, var(--un-text-opacity));
}

.text-_bl__h_9a9a9a_br_,.text-_bl__h_9A9A9A_br_ {
    --un-text-opacity: 1;
    color: rgba(154, 154, 154, var(--un-text-opacity));
}

.text-_bl__h_9b9b9b_br_ {
    --un-text-opacity: 1;
    color: rgba(155, 155, 155, var(--un-text-opacity));
}

.text-_bl__h_9d9d9d_br_ {
    --un-text-opacity: 1;
    color: rgba(157, 157, 157, var(--un-text-opacity));
}

.text-_bl__h_9f9f9f_br_,.text-_bl__h_9F9F9F_br_ {
    --un-text-opacity: 1;
    color: rgba(159, 159, 159, var(--un-text-opacity));
}

.c-hex-a0a0a0,.text-_bl__h_a0a0a0_br_,.text-_bl__h_A0A0A0_br_,.text-hex-a0a0a0 {
    --un-text-opacity: 1;
    color: rgba(160, 160, 160, var(--un-text-opacity));
}

.text-_bl__h_a0a4a7_br_ {
    --un-text-opacity: 1;
    color: rgba(160, 164, 167, var(--un-text-opacity));
}

.text-_bl__h_a3a3a3_br_ {
    --un-text-opacity: 1;
    color: rgba(163, 163, 163, var(--un-text-opacity));
}

.text-_bl__h_a3a9bf_br_ {
    --un-text-opacity: 1;
    color: rgba(163, 169, 191, var(--un-text-opacity));
}

.text-_bl__h_a5a098_br_ {
    --un-text-opacity: 1;
    color: rgba(165, 160, 152, var(--un-text-opacity));
}

.text-_bl__h_a6a6a6_br_ {
    --un-text-opacity: 1;
    color: rgba(166, 166, 166, var(--un-text-opacity));
}

.text-_bl__h_a9b910_br_ {
    --un-text-opacity: 1;
    color: rgba(169, 185, 16, var(--un-text-opacity));
}

.c-hex-aaa,.text-_bl__h_aaa_br_,.text-_bl__h_AAA_br_,.text-hex-aaa {
    --un-text-opacity: 1;
    color: rgba(170, 170, 170, var(--un-text-opacity));
}

.text-_bl__h_acacac_br_,.text-hex-acacac {
    --un-text-opacity: 1;
    color: rgba(172, 172, 172, var(--un-text-opacity));
}

.text-_bl__h_b2b2b2_br_ {
    --un-text-opacity: 1;
    color: rgba(178, 178, 178, var(--un-text-opacity));
}

.text-_bl__h_b5b5b5_br_ {
    --un-text-opacity: 1;
    color: rgba(181, 181, 181, var(--un-text-opacity));
}

.text-_bl__h_b7b7b7_br_,.text-hex-b7b7b7 {
    --un-text-opacity: 1;
    color: rgba(183, 183, 183, var(--un-text-opacity));
}

.text-_bl__h_ba993a_br_ {
    --un-text-opacity: 1;
    color: rgba(186, 153, 58, var(--un-text-opacity));
}

.c-hex-bbb,.text-_bl__h_bbb_br_,.text-hex-bbb {
    --un-text-opacity: 1;
    color: rgba(187, 187, 187, var(--un-text-opacity));
}

.text-_bl__h_bcbcbc_br_ {
    --un-text-opacity: 1;
    color: rgba(188, 188, 188, var(--un-text-opacity));
}

.text-_bl__h_bfbfbf_br_ {
    --un-text-opacity: 1;
    color: rgba(191, 191, 191, var(--un-text-opacity));
}

.text-_bl__h_c2c2c2_br_ {
    --un-text-opacity: 1;
    color: rgba(194, 194, 194, var(--un-text-opacity));
}

.text-_bl__h_c3b29d_br_ {
    --un-text-opacity: 1;
    color: rgba(195, 178, 157, var(--un-text-opacity));
}

.text-_bl__h_c58443_br_ {
    --un-text-opacity: 1;
    color: rgba(197, 132, 67, var(--un-text-opacity));
}

.c-_h_C5C5C5,.c-hex-c5c5c5,.c-hex-C5C5C5,.text-_bl__h_C5C5C5_br_ {
    --un-text-opacity: 1;
    color: rgba(197, 197, 197, var(--un-text-opacity));
}

.text-_bl__h_c7964e_br_ {
    --un-text-opacity: 1;
    color: rgba(199, 150, 78, var(--un-text-opacity));
}

.c-hex-ccc,.color-hex-cccccc,.text-_bl__h_ccc_br_,.text-_bl__h_CCC_br_,.text-_bl__h_cccccc_br_ {
    --un-text-opacity: 1;
    color: rgba(204, 204, 204, var(--un-text-opacity));
}

.text-_bl__h_ccc_br__i_ {
    --un-text-opacity: 1 !important;
    color: rgba(204, 204, 204, var(--un-text-opacity)) !important;
}

.c-hex-cfcfcf,.text-_bl__h_cfcfcf_br_ {
    --un-text-opacity: 1;
    color: rgba(207, 207, 207, var(--un-text-opacity));
}

.text-_bl__h_d3d3d3_br_ {
    --un-text-opacity: 1;
    color: rgba(211, 211, 211, var(--un-text-opacity));
}

.text-_bl__h_d3d3d3_br__i_ {
    --un-text-opacity: 1 !important;
    color: rgba(211, 211, 211, var(--un-text-opacity)) !important;
}

.text-_bl__h_d83000_br_ {
    --un-text-opacity: 1;
    color: rgba(216, 48, 0, var(--un-text-opacity));
}

.text-_bl__h_d9bd97_br_ {
    --un-text-opacity: 1;
    color: rgba(217, 189, 151, var(--un-text-opacity));
}

.text-_bl__h_dbdbdb_br_ {
    --un-text-opacity: 1;
    color: rgba(219, 219, 219, var(--un-text-opacity));
}

.text-_bl__h_ddd1c4_br_ {
    --un-text-opacity: 1;
    color: rgba(221, 209, 196, var(--un-text-opacity));
}

.text-_bl__h_e0dffe_br_ {
    --un-text-opacity: 1;
    color: rgba(224, 223, 254, var(--un-text-opacity));
}

.text-_bl__h_e64904_br_ {
    --un-text-opacity: 1;
    color: rgba(230, 73, 4, var(--un-text-opacity));
}

.text-_bl__h_e72909_br_ {
    --un-text-opacity: 1;
    color: rgba(231, 41, 9, var(--un-text-opacity));
}

.text-_bl__h_e84015_br_ {
    --un-text-opacity: 1;
    color: rgba(232, 64, 21, var(--un-text-opacity));
}

.text-_bl__h_ea5901_br_ {
    --un-text-opacity: 1;
    color: rgba(234, 89, 1, var(--un-text-opacity));
}

.text-_bl__h_eb5a53_br_ {
    --un-text-opacity: 1;
    color: rgba(235, 90, 83, var(--un-text-opacity));
}

.text-_bl__h_ed1b34_br_ {
    --un-text-opacity: 1;
    color: rgba(237, 27, 52, var(--un-text-opacity));
}

.text-_bl__h_ED736C_br_ {
    --un-text-opacity: 1;
    color: rgba(237, 115, 108, var(--un-text-opacity));
}

.text-_bl__h_F0F0F0_br_ {
    --un-text-opacity: 1;
    color: rgba(240, 240, 240, var(--un-text-opacity));
}

.text-_bl__h_f20f28_br_ {
    --un-text-opacity: 1;
    color: rgba(242, 15, 40, var(--un-text-opacity));
}

.text-_bl__h_f42131_br_ {
    --un-text-opacity: 1;
    color: rgba(244, 33, 49, var(--un-text-opacity));
}

.color-_h_f4c27a,.text-_bl__h_f4c27a_br_ {
    --un-text-opacity: 1;
    color: rgba(244, 194, 122, var(--un-text-opacity));
}

.c-_h_F53F3F,.color-_h_F53F3F,.text-_bl__h_F53F3F_br_,.text-hex-F53F3F {
    --un-text-opacity: 1;
    color: rgba(245, 63, 63, var(--un-text-opacity));
}

.text-_bl__h_F55247_br_ {
    --un-text-opacity: 1;
    color: rgba(245, 82, 71, var(--un-text-opacity));
}

.text-_bl__h_f56c6c_br_ {
    --un-text-opacity: 1;
    color: rgba(245, 108, 108, var(--un-text-opacity));
}

.text-_bl__h_f76260_br_ {
    --un-text-opacity: 1;
    color: rgba(247, 98, 96, var(--un-text-opacity));
}

.c-_h_F80,.text-_bl__h_f80_br_,.text-_bl__h_FF8800_br_,.text-_h_FF8800 {
    --un-text-opacity: 1;
    color: rgba(255, 136, 0, var(--un-text-opacity));
}

.text-_bl__h_f84036_br_ {
    --un-text-opacity: 1;
    color: rgba(248, 64, 54, var(--un-text-opacity));
}

.c-_h_F93A4A,.c-hex-f93a4a,.c-hex-F93A4A,.color-hex-F93A4A,.text-_bl__h_f93a4a_br_,.text-_bl__h_F93A4A_br_,.text-_h_f93a4a {
    --un-text-opacity: 1;
    color: rgba(249, 58, 74, var(--un-text-opacity));
}

.text-_bl__h_FAAD14_br_ {
    --un-text-opacity: 1;
    color: rgba(250, 173, 20, var(--un-text-opacity));
}

.text-_bl__h_fb663d_br_ {
    --un-text-opacity: 1;
    color: rgba(251, 102, 61, var(--un-text-opacity));
}

.text-_bl__h_fc2626_br_ {
    --un-text-opacity: 1;
    color: rgba(252, 38, 38, var(--un-text-opacity));
}

.text-_bl__h_fc3030_br_ {
    --un-text-opacity: 1;
    color: rgba(252, 48, 48, var(--un-text-opacity));
}

.c-_h_fc4930,.c-_h_FC4930,.c-hex-FC4930,.text-_bl__h_fc4930_br_ {
    --un-text-opacity: 1;
    color: rgba(252, 73, 48, var(--un-text-opacity));
}

.text-_bl__h_fdd488_br_ {
    --un-text-opacity: 1;
    color: rgba(253, 212, 136, var(--un-text-opacity));
}

.text-_bl__h_fe3646_br_ {
    --un-text-opacity: 1;
    color: rgba(254, 54, 70, var(--un-text-opacity));
}

.text-_bl__h_fe4521_br_ {
    --un-text-opacity: 1;
    color: rgba(254, 69, 33, var(--un-text-opacity));
}

.text-_bl__h_ff0000_br_ {
    --un-text-opacity: 1;
    color: rgba(255, 0, 0, var(--un-text-opacity));
}

.c-hex-ff0b0b,.text-_bl__h_ff0b0b_br_ {
    --un-text-opacity: 1;
    color: rgba(255, 11, 11, var(--un-text-opacity));
}

.color-hex-FF1313,.text-_bl__h_ff1313_br_ {
    --un-text-opacity: 1;
    color: rgba(255, 19, 19, var(--un-text-opacity));
}

.text-_bl__h_ff1717_br_ {
    --un-text-opacity: 1;
    color: rgba(255, 23, 23, var(--un-text-opacity));
}

.text-_bl__h_ff3441_br_ {
    --un-text-opacity: 1;
    color: rgba(255, 52, 65, var(--un-text-opacity));
}

.c-hex-ff3b30,.text-_bl__h_ff3b30_br_ {
    --un-text-opacity: 1;
    color: rgba(255, 59, 48, var(--un-text-opacity));
}

.text-_bl__h_ff3d54_br_ {
    --un-text-opacity: 1;
    color: rgba(255, 61, 84, var(--un-text-opacity));
}

.text-_bl__h_ff4546_br_ {
    --un-text-opacity: 1;
    color: rgba(255, 69, 70, var(--un-text-opacity));
}

.text-_bl__h_ff4c4c_br_ {
    --un-text-opacity: 1;
    color: rgba(255, 76, 76, var(--un-text-opacity));
}

.text-_bl__h_ff5948_br_ {
    --un-text-opacity: 1;
    color: rgba(255, 89, 72, var(--un-text-opacity));
}

.text-_bl__h_FF6768_br_ {
    --un-text-opacity: 1;
    color: rgba(255, 103, 104, var(--un-text-opacity));
}

.text-_bl__h_ff7033_br_ {
    --un-text-opacity: 1;
    color: rgba(255, 112, 51, var(--un-text-opacity));
}

.text-_bl__h_ff742a_br_ {
    --un-text-opacity: 1;
    color: rgba(255, 116, 42, var(--un-text-opacity));
}

.c-hex-ff8f0f,.color-_h_FF8F0F,.text-_bl__h_ff8f0f_br_,.text-_bl__h_FF8F0F_br_,.text-hex-FF8F0F {
    --un-text-opacity: 1;
    color: rgba(255, 143, 15, var(--un-text-opacity));
}

.text-_bl__h_ff9c00_br_ {
    --un-text-opacity: 1;
    color: rgba(255, 156, 0, var(--un-text-opacity));
}

.text-_bl__h_ffaa46_br_ {
    --un-text-opacity: 1;
    color: rgba(255, 170, 70, var(--un-text-opacity));
}

.text-_bl__h_ffae00_br_ {
    --un-text-opacity: 1;
    color: rgba(255, 174, 0, var(--un-text-opacity));
}

.text-_bl__h_ffaf7f_br_ {
    --un-text-opacity: 1;
    color: rgba(255, 175, 127, var(--un-text-opacity));
}

.text-_bl__h_ffc210_br_ {
    --un-text-opacity: 1;
    color: rgba(255, 194, 16, var(--un-text-opacity));
}

.text-_bl__h_ffcc30_br_ {
    --un-text-opacity: 1;
    color: rgba(255, 204, 48, var(--un-text-opacity));
}

.text-_bl__h_ffdba6_br_ {
    --un-text-opacity: 1;
    color: rgba(255, 219, 166, var(--un-text-opacity));
}

.text-_bl__h_ffefbd_br_ {
    --un-text-opacity: 1;
    color: rgba(255, 239, 189, var(--un-text-opacity));
}

.c-_bl__h_fff_br_,.c-_h_fff,.c-hex-fff,.c-hex-ffffff,.c-white,.color-_h_fff,.color-_h_ffffff,.color-hex-fff,.color-white,.text-_bl__h_fff_br_,.text-_bl__h_FFF_br_,.text-_bl__h_ffffff_br_,.text-hex-fff,.text-hex-ffffff,.text-white {
    --un-text-opacity: 1;
    color: rgba(255, 255, 255, var(--un-text-opacity));
}

.text-_bl__h_fff8e9_br_ {
    --un-text-opacity: 1;
    color: rgba(255, 248, 233, var(--un-text-opacity));
}

.c-hex-fffdef,.text-_bl__h_fffdef_br_ {
    --un-text-opacity: 1;
    color: rgba(255, 253, 239, var(--un-text-opacity));
}

.text-_bl__h_fffef0_br_ {
    --un-text-opacity: 1;
    color: rgba(255, 254, 240, var(--un-text-opacity));
}

.text-_bl__h_ffffb2_br_ {
    --un-text-opacity: 1;
    color: rgba(255, 255, 178, var(--un-text-opacity));
}

.c-hex-191919,.color-hex-191919,.text-_bl_rgb_pl_25_2c_25_2c_25_pr__br_ {
    --un-text-opacity: 1;
    color: rgba(25, 25, 25, var(--un-text-opacity));
}

.text-_bl_rgb_pl_251_2c_69_2c_6_pr__br_ {
    --un-text-opacity: 1;
    color: rgba(251, 69, 6, var(--un-text-opacity));
}

.text-_bl_rgb_pl_254_2c_92_2c_77_pr__br_ {
    --un-text-opacity: 1;
    color: rgba(254, 92, 77, var(--un-text-opacity));
}

.text-_bl_rgba_pl_0_2c_0_2c_0_2c_0_d_3_pr__br_ {
    --un-text-opacity: 0.3;
    color: rgba(0, 0, 0, var(--un-text-opacity));
}

.text-_bl_rgba_pl_255_2c_255_2c_255_2c_0_d_2_pr__br_ {
    --un-text-opacity: 0.2;
    color: rgba(255, 255, 255, var(--un-text-opacity));
}

.text-_bl_rgba_pl_255_2c_255_2c_255_2c_0_d_5_pr__br_ {
    --un-text-opacity: 0.5;
    color: rgba(255, 255, 255, var(--un-text-opacity));
}

.text-_bl_rgba_pl_255_2c_255_2c_255_2c_0_d_6_pr__br_ {
    --un-text-opacity: 0.6;
    color: rgba(255, 255, 255, var(--un-text-opacity));
}

.color-_bl_rgba_pl_255_2c_255_2c_255_2c_0_d_8_pr__br_,.text-_bl_rgba_pl_255_2c_255_2c_255_2c_0_d_8_pr__br_ {
    --un-text-opacity: 0.8;
    color: rgba(255, 255, 255, var(--un-text-opacity));
}

.text-_bl_rgba_pl_41_2c_41_2c_41_2c_0_d_4_pr__br_ {
    --un-text-opacity: 0.4;
    color: rgba(41, 41, 41, var(--un-text-opacity));
}

.text-_bl_rgba_pl_51_2c_51_2c_51_2c_0_d_4_pr__br_ {
    --un-text-opacity: 0.4;
    color: rgba(51, 51, 51, var(--un-text-opacity));
}

.text-_bl_var_pl_--color-theme_pr__br_ {
    color: var(--color-theme);
}

.text-_bl_var_pl_--color_2c__h_333333_pr__br_ {
    color: var(--color,#333333);
}

.text-_bl_var_pl_--pseudo-element-color_pr__br_ {
    color: var(--pseudo-element-color);
}

.c-primary,.color-primary,.text-_bl_var_pl_--std-primary-color_pr__br_,.text-primary {
    color: var(--std-primary-color);
}

.c-_bl_var_pl_--theme-color_pr__br_,.text-_bl_var_pl_--theme-color_pr__br_ {
    color: var(--theme-color);
}

.c-hex-011919,.text-hex-011919 {
    --un-text-opacity: 1;
    color: rgba(1, 25, 25, var(--un-text-opacity));
}

.text-hex-1a1a1a {
    --un-text-opacity: 1;
    color: rgba(26, 26, 26, var(--un-text-opacity));
}

.text-hex-787878 {
    --un-text-opacity: 1;
    color: rgba(120, 120, 120, var(--un-text-opacity));
}

.text-hex-858585 {
    --un-text-opacity: 1;
    color: rgba(133, 133, 133, var(--un-text-opacity));
}

.c-hex-979797,.text-hex-979797 {
    --un-text-opacity: 1;
    color: rgba(151, 151, 151, var(--un-text-opacity));
}

.text-hex-ae965a {
    --un-text-opacity: 1;
    color: rgba(174, 150, 90, var(--un-text-opacity));
}

.c-hex-c8c9cc,.text-hex-c8c9cc {
    --un-text-opacity: 1;
    color: rgba(200, 201, 204, var(--un-text-opacity));
}

.text-hex-ebc078 {
    --un-text-opacity: 1;
    color: rgba(235, 192, 120, var(--un-text-opacity));
}

.text-hex-f23 {
    --un-text-opacity: 1;
    color: rgba(255, 34, 51, var(--un-text-opacity));
}

.text-hex-fa3423,.text-hex-FA3423 {
    --un-text-opacity: 1;
    color: rgba(250, 52, 35, var(--un-text-opacity));
}

.text-primary-opacity-10 {
    color: var(--std-primary-color-opacity-10);
}

.c-primary-opacity-50,.text-primary-opacity-50 {
    color: var(--std-primary-color-opacity-50);
}

.c-red,.text-red {
    --un-text-opacity: 1;
    color: rgba(248, 113, 113, var(--un-text-opacity));
}

.first-text-_bl__h_000_br_:first-child {
    --un-text-opacity: 1;
    color: rgba(0, 0, 0, var(--un-text-opacity));
}

.first-text-_bl__h_3b3b3b_br_:first-child {
    --un-text-opacity: 1;
    color: rgba(59, 59, 59, var(--un-text-opacity));
}

.first-text-_bl__h_666666_br_:first-child {
    --un-text-opacity: 1;
    color: rgba(102, 102, 102, var(--un-text-opacity));
}

.last-text-_bl__h_333333_br_:last-child {
    --un-text-opacity: 1;
    color: rgba(51, 51, 51, var(--un-text-opacity));
}

.last-text-_bl__h_767676_br_:last-child {
    --un-text-opacity: 1;
    color: rgba(118, 118, 118, var(--un-text-opacity));
}

.last-text-_bl__h_ffffff_br_:last-child {
    --un-text-opacity: 1;
    color: rgba(255, 255, 255, var(--un-text-opacity));
}

.c-_bl__h_06CF6E_br_,.c-_h_06CF6E {
    --un-text-opacity: 1;
    color: rgba(6, 207, 110, var(--un-text-opacity));
}

.c-_bl__h_F47B24_br_ {
    --un-text-opacity: 1;
    color: rgba(244, 123, 36, var(--un-text-opacity));
}

.c-_bl_rgba_pl_0_2c_0_2c_0_2c_0_d_40_pr__br_ {
    --un-text-opacity: 0.40;
    color: rgba(0, 0, 0, var(--un-text-opacity));
}

.c-_h_02a57d {
    --un-text-opacity: 1;
    color: rgba(2, 165, 125, var(--un-text-opacity));
}

.c-_h_6f6f6f {
    --un-text-opacity: 1;
    color: rgba(111, 111, 111, var(--un-text-opacity));
}

.c-_h_e3e3e3 {
    --un-text-opacity: 1;
    color: rgba(227, 227, 227, var(--un-text-opacity));
}

.c-_h_FF5A00 {
    --un-text-opacity: 1;
    color: rgba(255, 90, 0, var(--un-text-opacity));
}

.c-_h_ffedea_i_ {
    --un-text-opacity: 1 !important;
    color: rgba(255, 237, 234, var(--un-text-opacity)) !important;
}

.c-hex-0ccd12 {
    --un-text-opacity: 1;
    color: rgba(12, 205, 18, var(--un-text-opacity));
}

.c-hex-282828 {
    --un-text-opacity: 1;
    color: rgba(40, 40, 40, var(--un-text-opacity));
}

.c-hex-2FD968 {
    --un-text-opacity: 1;
    color: rgba(47, 217, 104, var(--un-text-opacity));
}

.c-hex-344f78 {
    --un-text-opacity: 1;
    color: rgba(52, 79, 120, var(--un-text-opacity));
}

.c-hex-40ba5a {
    --un-text-opacity: 1;
    color: rgba(64, 186, 90, var(--un-text-opacity));
}

.c-hex-4d4d4d {
    --un-text-opacity: 1;
    color: rgba(77, 77, 77, var(--un-text-opacity));
}

.c-hex-50d0fe {
    --un-text-opacity: 1;
    color: rgba(80, 208, 254, var(--un-text-opacity));
}

.c-hex-535353 {
    --un-text-opacity: 1;
    color: rgba(83, 83, 83, var(--un-text-opacity));
}

.c-hex-555454 {
    --un-text-opacity: 1;
    color: rgba(85, 84, 84, var(--un-text-opacity));
}

.c-hex-646566 {
    --un-text-opacity: 1;
    color: rgba(100, 101, 102, var(--un-text-opacity));
}

.c-hex-656565 {
    --un-text-opacity: 1;
    color: rgba(101, 101, 101, var(--un-text-opacity));
}

.c-hex-969799 {
    --un-text-opacity: 1;
    color: rgba(150, 151, 153, var(--un-text-opacity));
}

.c-hex-c1c1c1 {
    --un-text-opacity: 1;
    color: rgba(193, 193, 193, var(--un-text-opacity));
}

.c-hex-cecece {
    --un-text-opacity: 1;
    color: rgba(206, 206, 206, var(--un-text-opacity));
}

.c-hex-d2d2d2_i_ {
    --un-text-opacity: 1 !important;
    color: rgba(210, 210, 210, var(--un-text-opacity)) !important;
}

.c-hex-dcd8d8,.color-_h_dcd8d8 {
    --un-text-opacity: 1;
    color: rgba(220, 216, 216, var(--un-text-opacity));
}

.c-hex-dcdcdc_i_ {
    --un-text-opacity: 1 !important;
    color: rgba(220, 220, 220, var(--un-text-opacity)) !important;
}

.c-hex-ddd,.c-hex-DDD {
    --un-text-opacity: 1;
    color: rgba(221, 221, 221, var(--un-text-opacity));
}

.c-hex-ddd_i_ {
    --un-text-opacity: 1 !important;
    color: rgba(221, 221, 221, var(--un-text-opacity)) !important;
}

.c-hex-dfdfdf {
    --un-text-opacity: 1;
    color: rgba(223, 223, 223, var(--un-text-opacity));
}

.c-hex-e5e5e5 {
    --un-text-opacity: 1;
    color: rgba(229, 229, 229, var(--un-text-opacity));
}

.c-hex-f1210f {
    --un-text-opacity: 1;
    color: rgba(241, 33, 15, var(--un-text-opacity));
}

.c-hex-f62e20 {
    --un-text-opacity: 1;
    color: rgba(246, 46, 32, var(--un-text-opacity));
}

.c-hex-F93A4A_i_ {
    --un-text-opacity: 1 !important;
    color: rgba(249, 58, 74, var(--un-text-opacity)) !important;
}

.c-hex-f96646 {
    --un-text-opacity: 1;
    color: rgba(249, 102, 70, var(--un-text-opacity));
}

.c-hex-fa4058 {
    --un-text-opacity: 1;
    color: rgba(250, 64, 88, var(--un-text-opacity));
}

.c-hex-fac907 {
    --un-text-opacity: 1;
    color: rgba(250, 201, 7, var(--un-text-opacity));
}

.c-hex-fb4d5a {
    --un-text-opacity: 1;
    color: rgba(251, 77, 90, var(--un-text-opacity));
}

.c-hex-fd2c2d {
    --un-text-opacity: 1;
    color: rgba(253, 44, 45, var(--un-text-opacity));
}

.c-hex-ff2b01 {
    --un-text-opacity: 1;
    color: rgba(255, 43, 1, var(--un-text-opacity));
}

.c-hex-ff6724 {
    --un-text-opacity: 1;
    color: rgba(255, 103, 36, var(--un-text-opacity));
}

.c-hex-FF6D6D {
    --un-text-opacity: 1;
    color: rgba(255, 109, 109, var(--un-text-opacity));
}

.c-hex-ff8000 {
    --un-text-opacity: 1;
    color: rgba(255, 128, 0, var(--un-text-opacity));
}

.c-hex-ffa300 {
    --un-text-opacity: 1;
    color: rgba(255, 163, 0, var(--un-text-opacity));
}

.c-hex-fff5f4 {
    --un-text-opacity: 1;
    color: rgba(255, 245, 244, var(--un-text-opacity));
}

.c-red-6 {
    --un-text-opacity: 1;
    color: rgba(220, 38, 38, var(--un-text-opacity));
}

.color-_bl__h_642C14_br_,.color-hex-642C14 {
    --un-text-opacity: 1;
    color: rgba(100, 44, 20, var(--un-text-opacity));
}

.color-_bl__h_832303_br_ {
    --un-text-opacity: 1;
    color: rgba(131, 35, 3, var(--un-text-opacity));
}

.color-_bl__h_A8A8A6_br_,.color-_h_A8A8A6 {
    --un-text-opacity: 1;
    color: rgba(168, 168, 166, var(--un-text-opacity));
}

.color-_bl__h_FF6300_br_ {
    --un-text-opacity: 1;
    color: rgba(255, 99, 0, var(--un-text-opacity));
}

.color-_bl_rgb_pl_162_2c_121_2c_96_pr__br_ {
    --un-text-opacity: 1;
    color: rgba(162, 121, 96, var(--un-text-opacity));
}

.color-_bl_var_pl_--std-coupon-icon-color_2c_--std-coupon-color_2c__h_ff5e51_pr__br_ {
    color: var(--std-coupon-icon-color,--std-coupon-color,#ff5e51);
}

.color-hex-1f1f1f {
    --un-text-opacity: 1;
    color: rgba(31, 31, 31, var(--un-text-opacity));
}

.color-hex-353535 {
    --un-text-opacity: 1;
    color: rgba(53, 53, 53, var(--un-text-opacity));
}

.color-hex-FF5815 {
    --un-text-opacity: 1;
    color: rgba(255, 88, 21, var(--un-text-opacity));
}

.color-hex-fffcef {
    --un-text-opacity: 1;
    color: rgba(255, 252, 239, var(--un-text-opacity));
}

.c-inherit,.text-inherit {
    color: inherit;
}

._i_font-bold {
    font-weight: 700 !important;
}

.font-400,.font-normal,.fw-400 {
    font-weight: 400;
}

.font-50 {
    font-weight: 50;
}

.font-500,.font-medium,.fw-500 {
    font-weight: 500;
}

.font-600 {
    font-weight: 600;
}

.font-700,.font-bold {
    font-weight: 700;
}

.font-800 {
    font-weight: 800;
}

.font-900 {
    font-weight: 900;
}

.first-font-500:first-child {
    font-weight: 500;
}

._i_lh-_bl_1_br_ {
    line-height: 1 !important;
}

.leading-_bl_1_d_2_br_,.lh-_bl_1_d_2_br_ {
    line-height: 1.2;
}

.leading-_bl_1_d_5em_br_,.lh-1_d_5em {
    line-height: 1.5em;
}

.leading-_bl_40rpx_br_,.leading-40,.leading-40rpx,.lh-40,.lh-40rpx {
    line-height: 40rpx;
}

.leading-100,.lh-100,.lh-100rpx {
    line-height: 100rpx;
}

.leading-100_p_,.lh-_bl_100_p__br_ {
    line-height: 100%;
}

.leading-110,.line-height-110 {
    line-height: 110rpx;
}

.leading-120_p_,.lh-_bl_120_p__br_ {
    line-height: 120%;
}

.leading-140_p_ {
    line-height: 140%;
}

.leading-24,.leading-24rpx,.lh-24,.lh-24rpx {
    line-height: 24rpx;
}

.leading-25 {
    line-height: 25rpx;
}

.leading-28,.leading-28rpx,.lh-28,.lh-28rpx {
    line-height: 28rpx;
}

.leading-30,.lh-30,.lh-30rpx {
    line-height: 30rpx;
}

.leading-31,.lh-31,.lh-31rpx {
    line-height: 31rpx;
}

.leading-32,.lh-32,.lh-32rpx {
    line-height: 32rpx;
}

.leading-33,.lh-33rpx {
    line-height: 33rpx;
}

.leading-34,.lh-34,.lh-34rpx {
    line-height: 34rpx;
}

.leading-35,.lh-35,.lh-35rpx {
    line-height: 35rpx;
}

.leading-36,.leading-36rpx,.lh-36,.lh-36rpx {
    line-height: 36rpx;
}

.leading-37,.lh-37,.lh-37rpx {
    line-height: 37rpx;
}

.leading-38,.lh-38,.lh-38rpx {
    line-height: 38rpx;
}

.leading-39 {
    line-height: 39rpx;
}

.leading-42,.leading-42rpx,.lh-42,.lh-42rpx,.line-height-42 {
    line-height: 42rpx;
}

.leading-44,.lh-44,.lh-44rpx {
    line-height: 44rpx;
}

.leading-45,.lh-45,.lh-45rpx {
    line-height: 45rpx;
}

.leading-46,.lh-46,.lh-46rpx {
    line-height: 46rpx;
}

.leading-48,.lh-48,.lh-48rpx,.line-height-48 {
    line-height: 48rpx;
}

.leading-50,.lh-50,.lh-50rpx {
    line-height: 50rpx;
}

.leading-53 {
    line-height: 53rpx;
}

.leading-56,.lh-56,.lh-56rpx {
    line-height: 56rpx;
}

.leading-58,.lh-58,.lh-58rpx {
    line-height: 58rpx;
}

.leading-59 {
    line-height: 59rpx;
}

.leading-60,.leading-60rpx,.lh-60,.lh-60rpx {
    line-height: 60rpx;
}

.leading-64,.lh-64,.lh-64rpx {
    line-height: 64rpx;
}

.leading-65 {
    line-height: 65rpx;
}

.leading-66,.leading-66rpx,.lh-66,.lh-66rpx {
    line-height: 66rpx;
}

.leading-67,.lh-67rpx {
    line-height: 67rpx;
}

.leading-68,.lh-68rpx {
    line-height: 68rpx;
}

.leading-72,.lh-72,.lh-72rpx,.line-height-72 {
    line-height: 72rpx;
}

.leading-74,.lh-74,.lh-74rpx {
    line-height: 74rpx;
}

.leading-77 {
    line-height: 77rpx;
}

.leading-80,.lh-80,.lh-80rpx {
    line-height: 80rpx;
}

.leading-84,.lh-84rpx {
    line-height: 84rpx;
}

.leading-88,.lh-88,.lh-88rpx,.line-height-88 {
    line-height: 88rpx;
}

.leading-90,.lh-90,.lh-90rpx {
    line-height: 90rpx;
}

.leading-94,.lh-94rpx {
    line-height: 94rpx;
}

.leading-96,.lh-96,.lh-96rpx {
    line-height: 96rpx;
}

.leading-inherit,.lh-inherit {
    line-height: inherit;
}

.leading-none,.lh-_bl_1_br_,.lh-none {
    line-height: 1;
}

.leading-normal,.lh-_bl_1_d_5_br_,.lh-normal {
    line-height: 1.5;
}

.leading-tight,.lh-tight {
    line-height: 1.25;
}

.lh-_bl_1_d_6_br_ {
    line-height: 1.6;
}

.lh-_bl_1_d_8_br_ {
    line-height: 1.8;
}

.lh-_bl_150_p__br_ {
    line-height: 150%;
}

.lh-_bl_180_p__br_ {
    line-height: 180%;
}

.lh-_bl_200_p__br_ {
    line-height: 200%;
}

.lh-1 {
    line-height: 1rpx;
}

.lh-102rpx {
    line-height: 102rpx;
}

.lh-106 {
    line-height: 106rpx;
}

.lh-108rpx {
    line-height: 108rpx;
}

.lh-10rpx {
    line-height: 10rpx;
}

.lh-112rpx {
    line-height: 112rpx;
}

.lh-120rpx {
    line-height: 120rpx;
}

.lh-18 {
    line-height: 18rpx;
}

.lh-20,.lh-20rpx {
    line-height: 20rpx;
}

.lh-20px {
    line-height: 20px;
}

.lh-22,.lh-22rpx {
    line-height: 22rpx;
}

.lh-26,.lh-26rpx {
    line-height: 26rpx;
}

.lh-29,.lh-29rpx {
    line-height: 29rpx;
}

.lh-43rpx {
    line-height: 43rpx;
}

.lh-52,.lh-52rpx,.line-height-52 {
    line-height: 52rpx;
}

.lh-54,.lh-54rpx {
    line-height: 54rpx;
}

.lh-62rpx {
    line-height: 62rpx;
}

.lh-70rpx {
    line-height: 70rpx;
}

.lh-82,.lh-82rpx {
    line-height: 82rpx;
}

.lh-92rpx {
    line-height: 92rpx;
}

.lh-98,.lh-98rpx {
    line-height: 98rpx;
}

.lh-initial {
    line-height: initial;
}

.lh-loose {
    line-height: 2;
}

.lh-normal_i_ {
    line-height: 1.5 !important;
}

.lh-relaxed {
    line-height: 1.625;
}

.first-lh-40rpx:first-child {
    line-height: 40rpx;
}

.first-lh-42rpx:first-child {
    line-height: 42rpx;
}

.last-lh-40rpx:last-child {
    line-height: 40rpx;
}

.tracking-0 {
    letter-spacing: 0;
}

.tracking-12 {
    letter-spacing: 12rpx;
}

.tracking-2rpx {
    letter-spacing: 2rpx;
}

.tracking-4 {
    letter-spacing: 4rpx;
}

.normal-case {
    text-transform: none;
}

.font-not-italic {
    font-style: normal;
}

.decoration-line-through,.line-through {
    text-decoration-line: line-through;
}

.decoration-underline,.underline {
    text-decoration-line: underline;
}

.decoration-inherit {
    text-decoration-style: inherit;
}

.no-underline {
    text-decoration: none;
}

.tab {
    -moz-tab-size: 4;
    -o-tab-size: 4;
    tab-size: 4;
}

.op-0,.opacity-0 {
    opacity: 0;
}

.op-10,.opacity-10 {
    opacity: 0.1;
}

.op-100,.opacity-100 {
    opacity: 1;
}

.op-20,.opacity-20 {
    opacity: 0.2;
}

.op-25 {
    opacity: 0.25;
}

.op-30,.opacity-30 {
    opacity: 0.3;
}

.op-40,.opacity-40 {
    opacity: 0.4;
}

.op-45 {
    opacity: 0.45;
}

.op-50,.opacity-50 {
    opacity: 0.5;
}

.op-60,.opacity-60 {
    opacity: 0.6;
}

.op-70,.opacity-70 {
    opacity: 0.7;
}

.op-75 {
    opacity: 0.75;
}

.op-80,.opacity-80 {
    opacity: 0.8;
}

.op-86 {
    opacity: 0.86;
}

.op-90 {
    opacity: 0.9;
}

.shadow {
    --un-shadow: var(--un-shadow-inset) 0 1px 3px 0 var(--un-shadow-color, rgba(0, 0, 0, 0.1)),var(--un-shadow-inset) 0 1px 2px -1px var(--un-shadow-color, rgba(0, 0, 0, 0.1));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_-5rpx_20rpx_0_rgba_pl_37_2c_37_2c_37_2c_0_d_1_pr__br_ {
    --un-shadow: 0 -5rpx 20rpx 0 var(--un-shadow-color, rgba(37, 37, 37, 0.1));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_-6rpx_16rpx_0_rgba_pl_50_2c_40_2c_144_2c_0_d_45_pr__inset_br_ {
    --un-shadow: inset 0 -6rpx 16rpx 0 var(--un-shadow-color, rgba(50, 40, 144, 0.45));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_-8rpx_20rpx_0_rgba_pl_37_2c_37_2c_37_2c_0_d_1_pr__br_ {
    --un-shadow: 0 -8rpx 20rpx 0 var(--un-shadow-color, rgba(37, 37, 37, 0.1));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_-8rpx_8rpx_0_rgba_pl_37_2c_37_2c_37_2c_0_d_1_pr__br_ {
    --un-shadow: 0 -8rpx 8rpx 0 var(--un-shadow-color, rgba(37, 37, 37, 0.1));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_0_0_1rpx__h_ccc_br_ {
    --un-shadow: 0 0 0 1rpx var(--un-shadow-color, rgb(204, 204, 204));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_0_10rpx__h_ccc_br_ {
    --un-shadow: 0 0 10rpx var(--un-shadow-color, rgb(204, 204, 204));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_0_10rpx_0_rgba_pl_12_2c_4_2c_7_2c_0_d_1_pr__br_ {
    --un-shadow: 0 0 10rpx 0 var(--un-shadow-color, rgba(12, 4, 7, 0.1));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_0_10rpx_rgba_pl_0_2c_0_2c_0_2c_0_d_1_pr__br_ {
    --un-shadow: 0 0 10rpx var(--un-shadow-color, rgba(0, 0, 0, 0.1));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_0_20rpx_0_rgba_pl_0_2c_0_2c_0_2c__d_08_pr__br_ {
    --un-shadow: 0 0 20rpx 0 var(--un-shadow-color, rgba(0, 0, 0, .08));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_0_5px__h_ffd13a_br_ {
    --un-shadow: 0 0 5px var(--un-shadow-color, rgb(255, 209, 58));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_0_5rpx_0_rgba_pl_0_2c_0_2c_0_2c_0_d_08_pr__br_ {
    --un-shadow: 0 0 5rpx 0 var(--un-shadow-color, rgba(0, 0, 0, 0.08));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_0_6rpx_0_rgba_pl_0_2c_0_2c_0_2c_0_d_05_pr__br_ {
    --un-shadow: 0 0 6rpx 0 var(--un-shadow-color, rgba(0, 0, 0, 0.05));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_0_8rpx_0_rgba_pl_0_2c_0_2c_0_2c_0_d_6_pr__br_ {
    --un-shadow: 0 0 8rpx 0 var(--un-shadow-color, rgba(0, 0, 0, 0.6));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_10rpx_10rpx_rgba_pl_255_2c_255_2c_255_2c_0_d_9_pr__br_ {
    --un-shadow: 0 10rpx 10rpx var(--un-shadow-color, rgba(255, 255, 255, 0.9));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_10rpx_16rpx_0_rgba_pl_0_2c_0_2c_0_2c_0_d_1_pr__br_ {
    --un-shadow: 0 10rpx 16rpx 0 var(--un-shadow-color, rgba(0, 0, 0, 0.1));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_12px_14px_0_rgba_pl_220_2c_227_2c_232_2c_0_d_3_pr__br_ {
    --un-shadow: 0 12px 14px 0 var(--un-shadow-color, rgba(220, 227, 232, 0.3));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_19rpx_86rpx_-12rpx__h_7e0d01_2c_inset_0_2rpx_2rpx_0_rgba_pl_255_2c_255_2c_255_2c_0_d_5_pr__br_ {
    --un-shadow: 0 19rpx 86rpx -12rpx #7e0d01,inset 0 2rpx 2rpx 0 rgba(255,255,255,0.5);
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_24rpx_28rpx_0_rgba_pl_220_2c_227_2c_232_2c__d_03_pr__br_ {
    --un-shadow: 0 24rpx 28rpx 0 var(--un-shadow-color, rgba(220, 227, 232, .03));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_2rpx_10rpx_0_rgba_pl_0_2c_0_2c_0_2c_0_d_17_pr__br_ {
    --un-shadow: 0 2rpx 10rpx 0 var(--un-shadow-color, rgba(0, 0, 0, 0.17));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_2rpx_10rpx_rgba_pl_0_2c_0_2c_0_2c_0_d_1_pr__br_ {
    --un-shadow: 0 2rpx 10rpx var(--un-shadow-color, rgba(0, 0, 0, 0.1));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_2rpx_4rpx_0_rgba_pl_0_2c_0_2c_0_2c__d_1_pr__br_ {
    --un-shadow: 0 2rpx 4rpx 0 var(--un-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_4rpx_14rpx_0_rgba_pl_0_2c_0_2c_0_2c_0_d_13_pr__br_ {
    --un-shadow: 0 4rpx 14rpx 0 var(--un-shadow-color, rgba(0, 0, 0, 0.13));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_4rpx_16rpx_0_rgba_pl_0_2c_0_2c_0_2c__d_08_pr__br_ {
    --un-shadow: 0 4rpx 16rpx 0 var(--un-shadow-color, rgba(0, 0, 0, .08));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_4rpx_16rpx_0_rgba_pl_0_2c_0_2c_0_2c_0_d_08_pr__br_ {
    --un-shadow: 0 4rpx 16rpx 0 var(--un-shadow-color, rgba(0, 0, 0, 0.08));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_4rpx_20rpx_0_rgba_pl_11_2c_3_2c_6_2c_0_d_27_pr__br_ {
    --un-shadow: 0 4rpx 20rpx 0 var(--un-shadow-color, rgba(11, 3, 6, 0.27));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_4rpx_20rpx_0_rgba_pl_14_2c_5_2c_10_2c_0_d_05_pr__br_ {
    --un-shadow: 0 4rpx 20rpx 0 var(--un-shadow-color, rgba(14, 5, 10, 0.05));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_4rpx_40rpx_0__h_fffcbb_br_ {
    --un-shadow: 0 4rpx 40rpx 0 var(--un-shadow-color, rgb(255, 252, 187));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_4rpx_80rpx_0__h_fffcbb_br_ {
    --un-shadow: 0 4rpx 80rpx 0 var(--un-shadow-color, rgb(255, 252, 187));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_4rpx_8rpx_0_rgba_pl_0_2c_0_2c_0_2c_0_d_05_pr__br_ {
    --un-shadow: 0 4rpx 8rpx 0 var(--un-shadow-color, rgba(0, 0, 0, 0.05));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_5px_15px_0_rgba_pl_0_2c_0_2c_0_2c_0_d_05_pr__br_ {
    --un-shadow: 0 5px 15px 0 var(--un-shadow-color, rgba(0, 0, 0, 0.05));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_5rpx_10rpx_0_rgba_pl_0_2c_0_2c_0_2c__d_1_pr__br_ {
    --un-shadow: 0 5rpx 10rpx 0 var(--un-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_5rpx_10rpx_0_rgba_pl_0_2c_0_2c_0_2c_0_d_1_pr__br_ {
    --un-shadow: 0 5rpx 10rpx 0 var(--un-shadow-color, rgba(0, 0, 0, 0.1));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_5rpx_12rpx_0_rgba_pl_0_2c_0_2c_0_2c__d_05_pr__br_ {
    --un-shadow: 0 5rpx 12rpx 0 var(--un-shadow-color, rgba(0, 0, 0, .05));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_5rpx_12rpx_0_rgba_pl_0_2c_0_2c_0_2c_0_d_05_pr__br_ {
    --un-shadow: 0 5rpx 12rpx 0 var(--un-shadow-color, rgba(0, 0, 0, 0.05));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_5rpx_15rpx_0_rgba_pl_0_2c_0_2c_0_2c_0_d_05_pr__br_ {
    --un-shadow: 0 5rpx 15rpx 0 var(--un-shadow-color, rgba(0, 0, 0, 0.05));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_5rpx_15rpx_0_rgba_pl_0_2c_0_2c_0_2c_0_d_07_pr__br_ {
    --un-shadow: 0 5rpx 15rpx 0 var(--un-shadow-color, rgba(0, 0, 0, 0.07));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_5rpx_15rpx_0_rgba_pl_0_2c_0_2c_0_2c_0_d_08_pr__br_ {
    --un-shadow: 0 5rpx 15rpx 0 var(--un-shadow-color, rgba(0, 0, 0, 0.08));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_5rpx_18rpx_0_rgba_pl_0_2c_0_2c_0_2c_0_d_08_pr__br_ {
    --un-shadow: 0 5rpx 18rpx 0 var(--un-shadow-color, rgba(0, 0, 0, 0.08));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_5rpx_20rpx_0_rgba_pl_0_2c_0_2c_0_2c__d_08_pr__br_ {
    --un-shadow: 0 5rpx 20rpx 0 var(--un-shadow-color, rgba(0, 0, 0, .08));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_5rpx_20rpx_0_rgba_pl_0_2c_0_2c_0_2c_0_d_08_pr__br_ {
    --un-shadow: 0 5rpx 20rpx 0 var(--un-shadow-color, rgba(0, 0, 0, 0.08));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_5rpx_20rpx_0_rgba_pl_204_2c_204_2c_204_2c_0_d_19_pr__br_ {
    --un-shadow: 0 5rpx 20rpx 0 var(--un-shadow-color, rgba(204, 204, 204, 0.19));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_5rpx_25rpx_0_rgba_pl_202_2c_202_2c_202_2c_0_d_25_pr__br_ {
    --un-shadow: 0 5rpx 25rpx 0 var(--un-shadow-color, rgba(202, 202, 202, 0.25));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_6px_15px_rgba_pl_0_2c_0_2c_0_2c_0_d_1_pr__br_ {
    --un-shadow: 0 6px 15px var(--un-shadow-color, rgba(0, 0, 0, 0.1));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_6px_16px_0_rgba_pl_0_2c_0_2c_0_2c_0_d_15_pr__br_ {
    --un-shadow: 0 6px 16px 0 var(--un-shadow-color, rgba(0, 0, 0, 0.15));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_6rpx_0_0_rgba_pl_180_2c_180_2c_180_2c_0_d_9_pr__br_ {
    --un-shadow: 0 6rpx 0 0 var(--un-shadow-color, rgba(180, 180, 180, 0.9));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_8rpx_0_0_rgba_pl_176_2c_81_2c_0_2c_0_d_9_pr__br_ {
    --un-shadow: 0 8rpx 0 0 var(--un-shadow-color, rgba(176, 81, 0, 0.9));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_8rpx_15rpx_0_rgba_pl_0_2c_0_2c_0_2c_0_d_06_pr__br_ {
    --un-shadow: 0 8rpx 15rpx 0 var(--un-shadow-color, rgba(0, 0, 0, 0.06));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0_2c_5rpx_2c_15rpx_2c_0_2c_rgba_pl_0_2c_0_2c_0_2c__d_08_pr__br_ {
    --un-shadow: 0,5rpx,15rpx,0,rgba(0,0,0,.08);
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0px_-5px_20px_0px_rgba_pl_37_2c_37_2c_37_2c_0_d_1_pr__br_ {
    --un-shadow: 0px -5px 20px 0px var(--un-shadow-color, rgba(37, 37, 37, 0.1));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0px_0px_15px_0px_rgba_pl_0_2c_0_2c_0_2c_0_d_1_pr__br_ {
    --un-shadow: 0px 0px 15px 0px var(--un-shadow-color, rgba(0, 0, 0, 0.1));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0px_0px_15rpx_0px_rgba_pl_0_2c_0_2c_0_2c_0_d_5_pr__br_ {
    --un-shadow: 0px 0px 15rpx 0px var(--un-shadow-color, rgba(0, 0, 0, 0.5));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0px_0px_16rpx_0px_rgba_pl_0_2c_0_2c_0_2c_0_d_2_pr__br_ {
    --un-shadow: 0px 0px 16rpx 0px var(--un-shadow-color, rgba(0, 0, 0, 0.2));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0px_0px_20px_0px_rgba_pl_0_2c_0_2c_0_2c_0_d_08_pr__br_ {
    --un-shadow: 0px 0px 20px 0px var(--un-shadow-color, rgba(0, 0, 0, 0.08));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0px_12px_14px_0px_rgba_pl_220_2c_227_2c_232_2c_0_d_3_pr__br_ {
    --un-shadow: 0px 12px 14px 0px var(--un-shadow-color, rgba(220, 227, 232, 0.3));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0px_2px_10px_0px_rgba_pl_0_2c_0_2c_0_2c_0_d_08_pr__br_ {
    --un-shadow: 0px 2px 10px 0px var(--un-shadow-color, rgba(0, 0, 0, 0.08));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0px_5px_10px_0px_rgba_pl_0_2c_0_2c_0_2c_0_d_1_pr__br_ {
    --un-shadow: 0px 5px 10px 0px var(--un-shadow-color, rgba(0, 0, 0, 0.1));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0px_6px_16px_0px_rgba_pl_0_2c_0_2c_0_2c_0_d_15_pr__br_ {
    --un-shadow: 0px 6px 16px 0px var(--un-shadow-color, rgba(0, 0, 0, 0.15));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0px_8rpx_0px_0px_rgba_pl_176_2c_81_2c_0_2c_0_d_9_pr__br_ {
    --un-shadow: 0px 8rpx 0px 0px var(--un-shadow-color, rgba(176, 81, 0, 0.9));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0rpx_-5rpx_10rpx_0rpx_rgba_pl_0_2c_0_2c_0_2c_0_d_1_pr__br_ {
    --un-shadow: 0rpx -5rpx 10rpx 0rpx var(--un-shadow-color, rgba(0, 0, 0, 0.1));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0rpx_10rpx_22rpx_0rpx_rgba_pl_0_2c_0_2c_0_2c_0_d_05_pr__br_ {
    --un-shadow: 0rpx 10rpx 22rpx 0rpx var(--un-shadow-color, rgba(0, 0, 0, 0.05));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0rpx_12rpx_14rpx_0rpx_rgba_pl_220_2c_227_2c_232_2c_0_d_2_pr__br_ {
    --un-shadow: 0rpx 12rpx 14rpx 0rpx var(--un-shadow-color, rgba(220, 227, 232, 0.2));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0rpx_12rpx_14rpx_0rpx_rgba_pl_220_2c_227_2c_232_2c_0_d_3_pr__br_ {
    --un-shadow: 0rpx 12rpx 14rpx 0rpx var(--un-shadow-color, rgba(220, 227, 232, 0.3));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0rpx_16rpx_40rpx_0rpx_rgba_pl_0_2c_0_2c_0_2c_0_d_1_pr__br_ {
    --un-shadow: 0rpx 16rpx 40rpx 0rpx var(--un-shadow-color, rgba(0, 0, 0, 0.1));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0rpx_2rpx_4rpx_0rpx_rgba_pl_0_2c_0_2c_0_2c_0_d_1_pr__br_ {
    --un-shadow: 0rpx 2rpx 4rpx 0rpx var(--un-shadow-color, rgba(0, 0, 0, 0.1));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0rpx_4rpx_10rpx_0rpx_rgba_pl_0_2c_0_2c_0_2c_0_d_2_pr__br_ {
    --un-shadow: 0rpx 4rpx 10rpx 0rpx var(--un-shadow-color, rgba(0, 0, 0, 0.2));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0rpx_4rpx_14rpx_0rpx_rgba_pl_200_2c_204_2c_213_2c_0_d_5_pr__br_ {
    --un-shadow: 0rpx 4rpx 14rpx 0rpx var(--un-shadow-color, rgba(200, 204, 213, 0.5));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_0rpx_5rpx_10rpx_0rpx_rgba_pl_0_2c_0_2c_0_2c_0_d_08_pr__br_ {
    --un-shadow: 0rpx 5rpx 10rpx 0rpx var(--un-shadow-color, rgba(0, 0, 0, 0.08));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_2rpx_5rpx_15rpx_0rpx_rgba_pl_0_2c_0_2c_0_2c_0_d_09_pr__br_ {
    --un-shadow: 2rpx 5rpx 15rpx 0rpx var(--un-shadow-color, rgba(0, 0, 0, 0.09));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_inset_0rpx_0rpx_55rpx_0rpx_rgba_pl_255_2c_255_2c_255_2c_0_d_5_pr__br_ {
    --un-shadow: inset 0rpx 0rpx 55rpx 0rpx var(--un-shadow-color, rgba(255, 255, 255, 0.5));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_inset_0rpx_30rpx_55rpx_0rpx_rgba_pl_255_2c_255_2c_255_2c_0_d_35_pr__br_ {
    --un-shadow: inset 0rpx 30rpx 55rpx 0rpx var(--un-shadow-color, rgba(255, 255, 255, 0.35));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_inset_0rpx_6rpx_5rpx_0rpx__h_fff4de_2c_inset_0rpx_-8rpx_6rpx_0rpx__h_f7bf8b_br_ {
    --un-shadow: inset 0rpx 6rpx 5rpx 0rpx #fff4de,inset 0rpx -8rpx 6rpx 0rpx #f7bf8b;
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-_bl_none_br_ {
    --un-shadow: none;
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.shadow-md {
    --un-shadow: var(--un-shadow-inset) 0 4px 6px -1px var(--un-shadow-color, rgba(0, 0, 0, 0.1)),var(--un-shadow-inset) 0 2px 4px -2px var(--un-shadow-color, rgba(0, 0, 0, 0.1));
    box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
}

.backdrop-blur-10px {
    --un-backdrop-blur: blur(10px);
    -webkit-backdrop-filter: var(--un-backdrop-blur) var(--un-backdrop-brightness) var(--un-backdrop-contrast) var(--un-backdrop-grayscale) var(--un-backdrop-hue-rotate) var(--un-backdrop-invert) var(--un-backdrop-opacity) var(--un-backdrop-saturate) var(--un-backdrop-sepia);
    backdrop-filter: var(--un-backdrop-blur) var(--un-backdrop-brightness) var(--un-backdrop-contrast) var(--un-backdrop-grayscale) var(--un-backdrop-hue-rotate) var(--un-backdrop-invert) var(--un-backdrop-opacity) var(--un-backdrop-saturate) var(--un-backdrop-sepia);
}

.backdrop-blur-15px {
    --un-backdrop-blur: blur(15px);
    -webkit-backdrop-filter: var(--un-backdrop-blur) var(--un-backdrop-brightness) var(--un-backdrop-contrast) var(--un-backdrop-grayscale) var(--un-backdrop-hue-rotate) var(--un-backdrop-invert) var(--un-backdrop-opacity) var(--un-backdrop-saturate) var(--un-backdrop-sepia);
    backdrop-filter: var(--un-backdrop-blur) var(--un-backdrop-brightness) var(--un-backdrop-contrast) var(--un-backdrop-grayscale) var(--un-backdrop-hue-rotate) var(--un-backdrop-invert) var(--un-backdrop-opacity) var(--un-backdrop-saturate) var(--un-backdrop-sepia);
}

.backdrop-blur-30 {
    --un-backdrop-blur: blur(30px);
    -webkit-backdrop-filter: var(--un-backdrop-blur) var(--un-backdrop-brightness) var(--un-backdrop-contrast) var(--un-backdrop-grayscale) var(--un-backdrop-hue-rotate) var(--un-backdrop-invert) var(--un-backdrop-opacity) var(--un-backdrop-saturate) var(--un-backdrop-sepia);
    backdrop-filter: var(--un-backdrop-blur) var(--un-backdrop-brightness) var(--un-backdrop-contrast) var(--un-backdrop-grayscale) var(--un-backdrop-hue-rotate) var(--un-backdrop-invert) var(--un-backdrop-opacity) var(--un-backdrop-saturate) var(--un-backdrop-sepia);
}

.blur-10 {
    --un-blur: blur(10px);
    filter: var(--un-blur) var(--un-brightness) var(--un-contrast) var(--un-drop-shadow) var(--un-grayscale) var(--un-hue-rotate) var(--un-invert) var(--un-saturate) var(--un-sepia);
}

.blur-100 {
    --un-blur: blur(100px);
    filter: var(--un-blur) var(--un-brightness) var(--un-contrast) var(--un-drop-shadow) var(--un-grayscale) var(--un-hue-rotate) var(--un-invert) var(--un-saturate) var(--un-sepia);
}

.blur-5 {
    --un-blur: blur(5px);
    filter: var(--un-blur) var(--un-brightness) var(--un-contrast) var(--un-drop-shadow) var(--un-grayscale) var(--un-hue-rotate) var(--un-invert) var(--un-saturate) var(--un-sepia);
}

.blur-80 {
    --un-blur: blur(80px);
    filter: var(--un-blur) var(--un-brightness) var(--un-contrast) var(--un-drop-shadow) var(--un-grayscale) var(--un-hue-rotate) var(--un-invert) var(--un-saturate) var(--un-sepia);
}

.drop-shadow-_bl_0_5rpx_10rpx_rgba_pl_0_2c_0_2c_0_2c_0_d_1_pr__br_ {
    --un-drop-shadow: drop-shadow(0 5rpx 10rpx rgba(0,0,0,0.1));
    filter: var(--un-blur) var(--un-brightness) var(--un-contrast) var(--un-drop-shadow) var(--un-grayscale) var(--un-hue-rotate) var(--un-invert) var(--un-saturate) var(--un-sepia);
}

.grayscale {
    --un-grayscale: grayscale(1);
    filter: var(--un-blur) var(--un-brightness) var(--un-contrast) var(--un-drop-shadow) var(--un-grayscale) var(--un-hue-rotate) var(--un-invert) var(--un-saturate) var(--un-sepia);
}

.transition {
    transition-property: color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.transition-box-shadow,.transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.transition-height {
    transition-property: height;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.transition-left {
    transition-property: left;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.transition-opacity {
    transition-property: opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.transition-transform {
    transition-property: transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.transition-width {
    transition-property: width;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.duration-_bl_0_d_3s_br_,.transition-duration-0_d_3s {
    transition-duration: 0.3s;
}

.duration-1000 {
    transition-duration: 1000ms;
}

.duration-200,.transition-duration-200 {
    transition-duration: 200ms;
}

.duration-250 {
    transition-duration: 250ms;
}

.duration-300,.transition-duration-300 {
    transition-duration: 300ms;
}

.duration-500 {
    transition-duration: 500ms;
}

.duration-600 {
    transition-duration: 600ms;
}

.transition-duration-0_d_15s {
    transition-duration: 0.15s;
}

.transition-duration-0_d_25s {
    transition-duration: 0.25s;
}

.transition-duration-0_d_2s {
    transition-duration: 0.2s;
}

.transition-duration-0_d_35s {
    transition-duration: 0.35s;
}

.transition-duration-0_d_4s {
    transition-duration: 0.4s;
}

.transition-duration-0_d_6s {
    transition-duration: 0.6s;
}

.transition-duration-0_d_7s {
    transition-duration: 0.7s;
}

.delay-0 {
    transition-delay: 0s;
}

.transition-delay-0_d_2s {
    transition-delay: 0.2s;
}

.ease,.ease-in-out,.transition-ease {
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.ease-_bl_cubic-bezier_pl_0_d_4_2c_0_d_6_2c_0_d_2_2c_1_pr__br_ {
    transition-timing-function: cubic-bezier(0.4,0.6,0.2,1);
}

.ease-linear,.transition-ease-_bl_linear_br_ {
    transition-timing-function: linear;
}

.transition-ease-_bl_ease_br_ {
    transition-timing-function: ease;
}

.transition-none {
    transition: none;
}

.content-_bl__q__q__br_ {
    content: '';
}

.before-content-_bl__q__q__br_::before {
    content: '';
}

.after_c_content-_bl__q__q__br_::after {
    content: '';
}

.not-last-after-content-_bl__q___q__br_:not(:last-child)::after {
    content: ' ';
}

.before-content-empty::before {
    content: "";
}

.after-content-empty::after {
    content: "";
}

.not-last-after-content-empty:not(:last-child)::after {
    content: "";
}

.pb-safe,.pb-safe-bottom,.pb-safe-half-plus-24 {
    padding-bottom: env(safe-area-inset-bottom, 0);
}

.pb_a_safe-24 {
    padding-bottom: calc(env(safe-area-inset-bottom) + 24rpx);
}

.after_c_h-safe-b-0::after {
    height: env(safe-area-inset-bottom, 0);
}

.bottom_a_safe-100 {
    bottom: calc(env(safe-area-inset-bottom) + 100rpx);
}

.bottom_a_safe-110 {
    bottom: calc(env(safe-area-inset-bottom) + 110rpx);
}

.bottom_a_safe-116 {
    bottom: calc(env(safe-area-inset-bottom) + 116rpx);
}

.bottom_a_safe-124 {
    bottom: calc(env(safe-area-inset-bottom) + 124rpx);
}

.bottom_a_safe-188 {
    bottom: calc(env(safe-area-inset-bottom) + 188rpx);
}

.bottom_a_safe-196 {
    bottom: calc(env(safe-area-inset-bottom) + 196rpx);
}

.bottom_a_safe-198 {
    bottom: calc(env(safe-area-inset-bottom) + 198rpx);
}

.bottom_a_safe-98 {
    bottom: calc(env(safe-area-inset-bottom) + 98rpx);
}

.pointer-events-all {
    pointer-events: all;
}

.break-words {
    word-break: break-word;
}

.safe-b {
    overflow: hidden;
    margin-bottom: calc(34rpx / 2 + env(safe-area-inset-bottom) / 2);
}

.translate-_bl_-50_p__2c_-50_p__br_ {
    --un-translate-x: -50%;
    --un-translate-y: -50%;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y))
          translateZ(var(--un-translate-z)) rotate(var(--un-rotate))
          rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y))
          rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y))
          scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y))
          scaleZ(var(--un-scale-z));
}

.before-translate-_bl_-50_p__2c_-50_p__br_::before {
    --un-translate-x: -50%;
    --un-translate-y: -50%;
    transform: translateX(var(--un-translate-x)) translateY(var(--un-translate-y))
          translateZ(var(--un-translate-z)) rotate(var(--un-rotate))
          rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y))
          rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y))
          scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y))
          scaleZ(var(--un-scale-z));
}

.font-size-inherit {
    font-size: inherit;
}

.font-size-initial {
    font-size: initial;
}

.bg-initial {
    background-color: initial;
}

.font-inherit {
    font-family: inherit;
}

.font-sans {
    font-family: ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";
}
