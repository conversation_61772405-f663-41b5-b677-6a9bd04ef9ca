	<style> </style> 	<page></page> 	<script> 	var __setCssStartTime__ = Date.now();			__wxAppCode__['components/auth-mobile-4ee542a2/index.wxss']();	
	var __setCssEndTime__ = Date.now();	(function() { 		var gf = $gwx( './components/auth-mobile-4ee542a2/index.wxml' ); 		if (window.__wxAppCodeReadyCallback__) { 			window.__wxAppCodeReadyCallback__(gf); 		} else { 			document.dispatchEvent(new CustomEvent( "generateFuncReady", { 				detail: { 					generateFunc: gf 			}})); 		} 	})(); 	</script> 	 