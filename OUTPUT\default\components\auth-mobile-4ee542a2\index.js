var __async = (e, t, r) => new Promise(((n, i) => {
    var o = e => {
        try {
          s(r.next(e))
        } catch (e) {
          i(e)
        }
      },
      c = e => {
        try {
          s(r.throw(e))
        } catch (e) {
          i(e)
        }
      },
      s = e => e.done ? n(e.value) : Promise.resolve(e.value).then(o, c);
    s((r = r.apply(e, t)).next())
  })),
  g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [209], {
    322: function(e, t, r) {
      r.g.currentModuleId = "_4ee542a2", r.g.currentCtor = Component, r.g.currentCtorType = "component", r.g.currentResourceType = "component", r(324), r.g.currentSrcMode = "wx", r(323)
    },
    323: function(e, t, r) {
      "use strict";
      r.r(t);
      var n = r(279),
        i = r(4),
        o = r(35),
        c = r(81),
        s = (r(36), r(298)),
        a = Object.defineProperty,
        u = Object.defineProperties,
        l = Object.getOwnPropertyDescriptors,
        h = Object.getOwnPropertySymbols,
        p = Object.prototype.hasOwnProperty,
        y = Object.prototype.propertyIsEnumerable,
        g = (e, t, r) => t in e ? a(e, t, {
          enumerable: !0,
          configurable: !0,
          writable: !0,
          value: r
        }) : e[t] = r;
      (0, n.a)({
        properties: {
          traceScreenType: {
            type: String,
            value: ""
          },
          traceOperationType: {
            type: String,
            value: ""
          },
          skip: {
            type: Boolean,
            value: !1
          },
          force: {
            type: Boolean,
            value: !1
          },
          update: {
            type: Boolean,
            value: !1
          },
          buttonStyle: {
            type: String,
            value: ""
          },
          registerSource: {
            type: Number,
            value: 0
          },
          activityId: {
            type: String,
            value: ""
          },
          inviteInfo: {
            type: String,
            value: ""
          },
          loginScene: {
            type: String,
            value: "refresh"
          },
          channelCode: {
            type: String,
            value: ""
          }
        },
        attached() {
          this._lock = !1
        },
        computed: {
          isAuth() {
            return c.stdStore.getters.isAuth
          }
        },
        methods: {
          handleTap() {
            this.triggerEvent("success")
          },
          getPhoneNumber(e) {
            return __async(this, null, (function*() {
              if (this._lock) return;
              try {
                this._lock = !0;
                {
                  const {
                    code: t,
                    encryptedData: r,
                    iv: n
                  } = e.detail;
                  if (!(t || r && n)) return this.triggerEvent(this.force ? "fail" : "success"), void o.a.reportError("getPhoneNumber fail", e);
                  const i = {};
                  t ? i.code = t : r && n && (i.encrypted_data = r, i.iv = n), this.requestBindMobile(i)
                }
              } catch (e) {
                console.error("getPhoneNumber", e), e && "401" === String(e.code).slice(-3) ? this.authSuccess({}) : (i.a.showToast({
                  title: e.message
                }), this.triggerEvent("fail"))
              } finally {
                this._lock = !1
              }
            }))
          },
          onAuthError(e) {
            o.a.reportError("getPhoneNumber onAuthError", e), this.force || this.authSuccess({})
          },
          requestBindMobile(e) {
            return __async(this, null, (function*() {
              const t = (r = ((e, t) => {
                for (var r in t || (t = {})) p.call(t, r) && g(e, r, t[r]);
                if (h)
                  for (var r of h(t)) y.call(t, r) && g(e, r, t[r]);
                return e
              })({}, e), n = {
                reg_activity_source: this.registerSource,
                is_update_mobile: this.update ? 1 : 0,
                channel_code: this.channelCode
              }, u(r, l(n)));
              var r, n;
              this.activityId && (t.reg_activity_id = this.activityId), this.inviteInfo && (t.inviteInfo = {
                identityType: 1,
                customerId: this.inviteInfo
              });
              const i = yield(0, s.a)(t);
              this.authSuccess(i)
            }))
          },
          authSuccess(e) {
            this.triggerEvent("success", e), this.triggerEvent("change")
          }
        }
      })
    },
    324: function(e, t, r) {
      r.g.currentInject = {
        moduleId: "_4ee542a2"
      }, r.g.currentInject.render = function(e, t, r, n) {
        !n("update") && (n("isAuth") || n("skip")), n("traceScreenType"), n("traceOperationType"), n("buttonStyle"), r()
      }
    }
  },
  function(e) {
    var t;
    t = 322, e(e.s = t)
  }
]);