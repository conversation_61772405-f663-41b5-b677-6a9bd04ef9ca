var g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [210], {
    281: function(e, t, n) {
      n.g.currentModuleId = "_54d9e854", n.g.currentCtor = Component, n.g.currentCtorType = "component", n.g.currentResourceType = "component", n(283), n.g.currentSrcMode = "wx", n(282)
    },
    282: function(e, t, n) {
      "use strict";
      n.r(t), (0, n(279).a)({
        properties: {
          prop: {
            type: String,
            value: "index.properties"
          }
        },
        data: {
          flag: !1
        },
        lifetimes: {
          ready() {
            wx.showLoading({
              title: "唤起支付中"
            }), this.login().then((e => (e.code && this.requestDuibaPay(e.code), e))).catch((() => {
              wx.hideLoading()
            }))
          }
        },
        methods: {
          getCurrentPageUrlQuery: function() {
            const e = getCurrentPages(),
              t = e[e.length - 1],
              {
                options: n
              } = t;
            return n
          },
          login: function() {
            return new Promise(((e, t) => {
              wx.login({
                success: function(t) {
                  e(t)
                },
                fail: function(e) {
                  t(e)
                },
                timeout: function(e) {
                  t(e)
                }
              })
            }))
          },
          requestDuibaPay: function(e) {
            const t = this.getCurrentPageUrlQuery(),
              {
                orderId: n
              } = t,
              {
                env: c
              } = t,
              {
                cookie: o
              } = t,
              r = decodeURIComponent(c);
            wx.request({
              url: "".concat(decodeURIComponent(c) || "http://activity.m.duiba.com.cn", "/ambPay/wxPayLite/charge"),
              data: {
                orderId: n,
                authCode: e
              },
              header: {
                cookie: decodeURIComponent(o)
              },
              success: function(e) {
                const {
                  data: t
                } = e;
                if (!t.success) return;
                const c = t.data,
                  {
                    timeStamp: o
                  } = c,
                  {
                    nonceStr: i
                  } = c,
                  {
                    signType: a
                  } = c,
                  {
                    paySign: u
                  } = c;
                wx.requestPayment({
                  timeStamp: o,
                  nonceStr: i,
                  package: t.data.package,
                  signType: a,
                  paySign: u,
                  success: function() {
                    wx.redirectTo({
                      url: "/pages/duibaRedirect/duibaRedirect?redirect=".concat(encodeURIComponent("".concat(r, "/crecord/orderPayResult?orderId=").concat(n)))
                    })
                  },
                  fail: function(e) {
                    const {
                      errMsg: t
                    } = e;
                    "requestPayment:fail cancel" === t && wx.redirectTo({
                      url: "/pages/duibaRedirect/duibaRedirect?redirect=".concat(encodeURIComponent("".concat(r, "/ambPay/cashier?orderId=").concat(n)))
                    })
                  }
                })
              },
              complete: function() {
                wx.hideLoading()
              }
            })
          }
        }
      })
    },
    283: function(e, t, n) {
      n.g.currentInject = {
        moduleId: "_54d9e854"
      }
    }
  },
  function(e) {
    var t;
    t = 281, e(e.s = t)
  }
]);