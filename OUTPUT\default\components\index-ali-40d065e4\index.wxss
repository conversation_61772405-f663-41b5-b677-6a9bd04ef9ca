.Q-navbar-btn-group._40d065e4:after {
    border: 1px solid transparent;
    border-radius: inherit;
    box-sizing: border-box;
    content: " ";
    height: 200%;
    left: 0;
    pointer-events: none;
    position: absolute;
    top: 0;
    transform: scale(.5);
    transform-origin: 0 0;
    width: 200%
}

.Q-navbar-btn-group-white._40d065e4:after {
    border-color: hsla(0,0%,100%,.3)
}

.Q-navbar-btn-group-black._40d065e4:after {
    border-color: rgba(0,0,0,.05)
}

.navbar-icon-back._40d065e4:after {
    bottom: -10rpx;
    content: "";
    left: -10rpx;
    position: absolute;
    right: -10rpx;
    top: -10rpx
}

.Q-navbar-single-btn._40d065e4:after {
    border: 1px solid transparent;
    border-radius: inherit;
    box-sizing: border-box;
    content: " ";
    height: 200%;
    left: 0;
    pointer-events: none;
    position: absolute;
    top: 0;
    transform: scale(.5);
    transform-origin: 0 0;
    width: 200%
}

.Q-navbar-single-btn-white._40d065e4:after {
    border-color: hsla(0,0%,100%,.3)
}

.Q-navbar-single-btn-black._40d065e4:after {
    border-color: rgba(0,0,0,.05)
}

.Q-transparent-back._40d065e4:after {
    display: none
}
