var __async = (t, e, a) => new Promise(((n, o) => {
    var r = t => {
        try {
          c(a.next(t))
        } catch (t) {
          o(t)
        }
      },
      i = t => {
        try {
          c(a.throw(t))
        } catch (t) {
          o(t)
        }
      },
      c = t => t.done ? n(t.value) : Promise.resolve(t.value).then(r, i);
    c((a = a.apply(t, e)).next())
  })),
  g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [213], {
    316: function(t, e, a) {
      a.g.currentModuleId = "_7f7626cc", a.g.currentCtor = Component, a.g.currentCtorType = "component", a.g.currentResourceType = "component", a(318), a.g.currentSrcMode = "wx", a(317)
    },
    317: function(t, e, a) {
      "use strict";
      a.r(e);
      var n = a(279),
        o = a(4),
        r = a(81),
        i = a(117),
        c = a(119),
        h = a(124),
        s = a(314),
        g = a(120),
        u = a(133),
        l = a(130),
        m = Object.defineProperty,
        d = Object.getOwnPropertySymbols,
        p = Object.prototype.hasOwnProperty,
        v = Object.prototype.propertyIsEnumerable,
        b = (t, e, a) => e in t ? m(t, e, {
          enumerable: !0,
          configurable: !0,
          writable: !0,
          value: a
        }) : t[e] = a;
      (0, n.a)({
        options: {
          multipleSlots: !0
        },
        properties: {
          patch: {
            type: Boolean,
            value: !1
          },
          title: {
            type: String,
            value: ""
          },
          fixed: {
            type: Boolean,
            value: !1
          },
          titleColor: {
            type: String,
            value: ""
          },
          background: {
            type: String,
            value: ""
          },
          backgroundImage: {
            type: String,
            value: ""
          },
          linearGradientBackground: {
            type: String,
            value: ""
          },
          customerTheme: {
            type: String,
            value: ""
          },
          notShowHome: {
            type: Boolean,
            value: !1
          },
          onlyShowHome: {
            type: Boolean,
            value: !1
          },
          isShowBack: {
            type: Boolean,
            value: !1
          },
          delta: {
            type: Number,
            value: 1
          },
          transparentBack: {
            type: Boolean,
            value: !0
          },
          showSearch: {
            type: Boolean,
            value: !1
          },
          showPinNav: {
            type: Boolean,
            value: !1
          },
          shouldExtNavColor: {
            type: Boolean,
            value: !0
          },
          showStoreName: {
            type: Boolean,
            value: !1
          },
          confirmBack: {
            type: Boolean,
            value: !1
          },
          confirmText: {
            type: String,
            value: "确定要退出当前页面吗?"
          }
        },
        data: {
          showBack: !1,
          showHome: !1,
          innerWidth: "",
          innerPaddingRight: "",
          navbarHeight: 0,
          statusBarHeight: 0,
          navbarContentHeight: 0,
          menuButtonRect: {
            width: 87,
            height: 32,
            ml: 7
          },
          reactRight: 0,
          theme: "black",
          navigationBarBackgroundColor: ""
        },
        computed: ((t, e) => {
          for (var a in e || (e = {})) p.call(e, a) && b(t, a, e[a]);
          if (d)
            for (var a of d(e)) v.call(e, a) && b(t, a, e[a]);
          return t
        })({
          navbarStyle() {
            let t = "padding-top:".concat(this.statusBarHeight, "px;height:").concat(this.navbarContentHeight, "px;"),
              e = "#fff";
            const a = g.store.getters.navbarBgColor || "";
            return a && (e = a), this.navigationBarBackgroundColor && (e = this.navigationBarBackgroundColor), this.background && (e = this.background), this.backgroundImage && (e = "#fff"), this.patch && (e = "#fff"), t += "background-color: ".concat(e, ";"), this.backgroundImage && (t += "background-image: url('".concat(this.backgroundImage, "');background-size: 100% 100%;")), t += "color:".concat(this.theme, ";"), this.linearGradientBackground && (t += this.linearGradientBackground), t
          },
          navbarIsWhite() {
            return "white" === this.theme || "white" === this.customerTheme
          },
          navbarLeftML() {
            if (!this.menuButtonRect) return "";
            return "margin-left: ".concat(this.menuButtonRect.ml, "px;")
          },
          navbarLeftSquareStyle() {
            if (!this.menuButtonRect) return "";
            const {
              width: t,
              height: e
            } = this.menuButtonRect;
            return "width: ".concat(t, "px;height: ").concat(e, "px;border-radius: ").concat(e, "px;")
          },
          navbarLeftCircleStyle() {
            if (!this.menuButtonRect) return "";
            const {
              height: t
            } = this.menuButtonRect;
            return "width: ".concat(t, "px;height: ").concat(t, "px;border-radius: ").concat(t, "px;")
          },
          navbarLeftBackBgImg() {
            return "background-image:url(".concat(this.navbarIsWhite ? "https://images.qmai.cn/s12416/images/2021/06/29/076d838098aad6c5.png" : "https://images.qmai.cn/resource/20210824210816/2025/03/31/back.png", ")")
          },
          navbarLeftHomeBgImg() {
            return "background-image:url(".concat(this.navbarIsWhite ? "https://img-shop.qmimg.cn/s1001195/images/2021/03/24/1c68f967ef1fefdc.png" : "https://img-shop.qmimg.cn/s1001195/images/2021/03/24/7a42e4a6acce5c15.png", ")")
          },
          navbarLeftSearchBgImg() {
            return "background-image:url(".concat(this.navbarIsWhite ? "https://images.qmai.cn/s12416/images/2021/06/29/1b7a57683b47e070.png" : "https://images.qmai.cn/s12416/images/2021/06/29/f5fda7f3c113655b.png", ")")
          },
          navbarLeftSearchLineBg() {
            return "background-color:".concat(this.navbarIsWhite ? "rgba(255, 255, 255, 0.3)" : "rgba(255, 255, 255, 0.15)", ";")
          },
          isLogin() {
            return g.store.state.isLogin
          }
        }, g.store.mapState(["extConfig"])),
        attached() {
          const t = (0, s.a)() || {},
            e = (0, l.a)() || {},
            {
              left: a,
              right: n,
              width: o,
              height: r
            } = t,
            {
              windowWidth: i,
              windowHeight: c
            } = e;
          this.updateIconState(), this.getNavbarHeight(t, e);
          const h = i - t.left + 5;
          this.reactRight = h > 150 ? 143 : h, t && (this.innerPaddingRight = "padding-right: ".concat(i - t.left, "px;"), o && (this.menuButtonRect.width = o), r && (this.menuButtonRect.height = r), i && (this.menuButtonRect.ml = i - n)), this.menuButtonRect.ml = 10, this.innerWidth = "width: auto";
          const {
            window: g
          } = this.extConfig || {};
          this.shouldExtNavColor && g && g.navigationBarTextStyle && (this.navigationBarBackgroundColor = g.navigationBarBackgroundColor, this.theme = g.navigationBarTextStyle)
        },
        pageLifetimes: {
          show() {}
        },
        methods: {
          getNavbarHeight(t, e) {
            let a = e.ios ? 44 : 48,
              n = e.statusBarHeight;
            r.stdStore.getters.isEmbeddedApp ? (n = 20, a = e.titleBarHeight) : t && (a = 2 * (t.top - e.statusBarHeight) + t.height), this.statusBarHeight = n, this.navbarContentHeight = a, this.triggerEvent("getNavHeight", a + n)
          },
          updateIconState() {
            const t = getCurrentPages();
            if (!t || !t.length) return;
            const e = t.length;
            this.showBack = (e > 1 || this.isShowBack) && !this.onlyShowHome, this.showHome = 1 === e && !this.notShowHome && !(0, h.d)(t[0].route) || this.onlyShowHome
          },
          goHome() {
            if (!this.isLogin) return void this.promotionGoHome();
            (0, h.g)("/pages/index/index")
          },
          promotionGoHome() {
            return __async(this, null, (function*() {
              try {
                const t = (0, c.g)() || {},
                  e = t.storeId ? t.storeId.toString() : "";
                yield(0, u.e)(e), yield(0, i.fetchLogin)({
                  forceLogin: !0,
                  source: "promotionGoHome"
                }), (0, h.g)("/pages/index/index")
              } catch (t) {
                console.log("promotionGoHomeError", t), (0, h.g)("/pages/index/index")
              }
            }))
          },
          handleBack() {
            const {
              delta: t,
              confirmBack: e,
              confirmText: a
            } = this;
            e ? o.a.showModal({
              content: a,
              cancelText: "暂不",
              cancelColor: "#333333",
              confirmText: "确定",
              confirmColor: g.store.getters.colorTheme,
              success: e => {
                e.confirm && ((0, h.e)(t), this.triggerEvent("back"))
              }
            }) : ((0, h.e)(t), this.triggerEvent("back"))
          },
          handleSearch() {
            this.triggerEvent("search")
          }
        }
      })
    },
    318: function(t, e, a) {
      a.g.currentInject = {
        moduleId: "_7f7626cc"
      }, a.g.currentInject.render = function(t, e, a, n) {
        n("fixed") || n("navbarContentHeight"), n("navbarStyle"), n("showStoreName"), n("statusBarHeight"), n("navbarLeftML"), n("showStoreName"), n("showPinNav") || (n("showSearch") ? (n("navbarIsWhite"), n("navbarLeftSquareStyle"), n("showBack") ? n("navbarLeftBackBgImg") : n("navbarLeftHomeBgImg"), n("navbarLeftSearchLineBg"), e("menuButtonRect.height")) : n("showBack") ? (n("transparentBack"), n("navbarIsWhite"), n("navbarLeftCircleStyle"), n("navbarLeftBackBgImg")) : n("showHome") && (n("navbarIsWhite"), n("navbarLeftCircleStyle"), n("navbarLeftHomeBgImg"))), n("title") && n("titleColor"), n("reactRight"), a()
      }
    }
  },
  function(t) {
    var e;
    e = 316, t(t.s = e)
  }
]);