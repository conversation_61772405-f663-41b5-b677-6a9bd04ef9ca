<view class="relative z-199 bg-_bl__h_f8f8f8_br_ invisible _7f7626cc " style="padding-top: {{statusBarHeight}}px; height: {{navbarContentHeight}}px" wx:if="{{!fixed}}"></view>
<view class="overflow-hidden fixed top-0 left-0 right-0 z-200 _7f7626cc ">
    <view class="relative z-201 flex items-center justify-center box-content bg-no-repeat bg-center bg-_bl_length_c_100_p__auto_br_ _7f7626cc " style="{{navbarStyle}}">
        <view class="absolute left-0 bottom-0 flex items-center w-190 {{showStoreName?'flex-row-reverse items-start justify-end w-500':''}} _7f7626cc " style="top:{{statusBarHeight}}px;{{navbarLeftML}}">
            <slot class=" _7f7626cc " name="left" wx:if="{{showStoreName}}"></slot>
            <slot class=" _7f7626cc " name="left" wx:if="{{showPinNav}}"></slot>
            <view class="{{'Q-navbar-btn-group relative flex items-center justify-evenly '+(navbarIsWhite?'bg-_bl_rgba_pl_0_2c_0_2c_0_2c_0_d_17_pr__br_ Q-navbar-btn-group-white':'bg-_bl_rgba_pl_255_2c_255_2c_255_2c_0_d_6_pr__br_ Q-navbar-btn-group-black')+' _7f7626cc '}}" style="{{navbarLeftSquareStyle}}" wx:elif="{{showSearch}}">
                <view bindtap="handleBack" class="relative h-full bg-no-repeat bg-center bg-_bl_length_c_60_p__br_ Q-navbar-icon-back _7f7626cc " style="{{navbarLeftBackBgImg}}" wx:if="{{showBack}}"></view>
                <view bindtap="goHome" class="h-full bg-no-repeat bg-center bg-_bl_length_c_45_p__br_ _7f7626cc " style="{{navbarLeftHomeBgImg}}" wx:else></view>
                <view class="absolute top-50_p_ left-50_p_ w-1px scale-x-50 translate-_bl_-50_p__2c_-50_p__br_ _7f7626cc " style="{{navbarLeftSearchLineBg}} height: {{menuButtonRect.height/2}}px;"></view>
                <view bindtap="handleSearch" class="h-full bg-no-repeat bg-center bg-_bl_length_c_45_p__br_ _7f7626cc " style="{{navbarLeftSearchLineBg}}"></view>
            </view>
            <view bindtap="handleBack" class="{{(transparentBack?'color-_h_343434 bg-transparent bg-_bl_length_c_80_p__br_ Q-transparent-back':'')+' relative flex items-center justify-center bg-no-repeat bg-center bg-_bl_length_c_40rpx_40rpx_br_ Q-navbar-single-btn '+(navbarIsWhite?'bg-_bl_rgba_pl_0_2c_0_2c_0_2c_0_d_17_pr__br_ Q-navbar-single-btn-white':'bg-_bl_rgba_pl_255_2c_255_2c_255_2c_0_d_6_pr__br_ Q-navbar-single-btn-black')+' bg-_bl_length_c_60_p__br_ Q-navbar-icon-back _7f7626cc '}}" style="{{navbarLeftCircleStyle}}{{navbarLeftBackBgImg}}" wx:elif="{{showBack}}"></view>
            <view bindtap="goHome" class="{{'relative flex items-center justify-center h-full bg-no-repeat bg-center bg-_bl_length_c_40rpx_40rpx_br_ Q-navbar-single-btn '+(navbarIsWhite?'bg-_bl_rgba_pl_0_2c_0_2c_0_2c_0_d_17_pr__br_ Q-navbar-single-btn-white':'bg-_bl_rgba_pl_255_2c_255_2c_255_2c_0_d_6_pr__br_ Q-navbar-single-btn-black')+' _7f7626cc '}}" style="{{navbarLeftCircleStyle}}{{navbarLeftHomeBgImg}}" wx:elif="{{showHome}}"></view>
            <slot class=" _7f7626cc " name="left" wx:else></slot>
        </view>
        <view class="w-full flex items-center justify-center overflow-hidden font-500 text-34 pointer-events-none _7f7626cc ">
            <view class="overflow-hidden text-ellipsis whitespace-nowrap w-300 text-center _7f7626cc " style="color:{{titleColor}}" wx:if="{{title}}">{{title}}</view>
            <view class=" _7f7626cc " style="pointer-events: auto" wx:else>
                <slot class=" _7f7626cc " name="center"></slot>
            </view>
        </view>
        <view class="absolute _7f7626cc " style="right:{{reactRight}}px;">
            <slot class=" _7f7626cc " name="right"></slot>
        </view>
    </view>
</view>
