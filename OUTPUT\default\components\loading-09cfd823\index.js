var g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [215], {
    293: function(t, r, e) {
      e.g.currentModuleId = "_09cfd823", e.g.currentCtor = Component, e.g.currentCtorType = "component", e.g.currentResourceType = "component", e(295), e.g.currentSrcMode = "wx", e(294)
    },
    294: function(t, r, e) {
      "use strict";
      e.r(r);
      var o = e(279),
        i = e(35),
        a = e(124),
        n = e(120),
        s = e(173),
        g = e(122),
        h = Object.defineProperty,
        d = Object.defineProperties,
        l = Object.getOwnPropertyDescriptors,
        u = Object.getOwnPropertySymbols,
        c = Object.prototype.hasOwnProperty,
        m = Object.prototype.propertyIsEnumerable,
        p = (t, r, e) => r in t ? h(t, r, {
          enumerable: !0,
          configurable: !0,
          writable: !0,
          value: e
        }) : t[r] = e,
        y = (t, r) => {
          for (var e in r || (r = {})) c.call(r, e) && p(t, e, r[e]);
          if (u)
            for (var e of u(r)) m.call(r, e) && p(t, e, r[e]);
          return t
        };
      (0, o.a)({
        properties: {
          loadStatus: {
            type: String,
            value: "pending"
          },
          loadStatusErrorDesc: {
            type: String,
            value: ""
          },
          operate: {
            type: String,
            value: ""
          },
          loadErrorInfo: {
            type: Object,
            value: {}
          },
          isFromErrorPage: {
            type: Boolean,
            value: !1
          },
          isShowNavigator: {
            type: Boolean,
            value: !0
          },
          refresh: {
            type: Boolean,
            value: !0
          },
          reasonText: {
            type: String,
            value: ""
          }
        },
        data: {
          errorImageSrc: "https://images.qmai.cn/resource/20210824210816/2023/10/16/yongdu.png",
          errorImageStyle: "width: 300rpx; height: 300rpx",
          retryImageSrc: "https://images.qmai.cn/resource/20210824210816/2023/10/16/xiaohuojian.gif",
          retryImageStyle: "width: 200rpx; height: 200rpx",
          tryLoad: !1,
          retryCount: 0,
          loadingUrl: "",
          loadingBtnText: "",
          customText: "",
          errorMode: 1,
          paddingBottom: "padding-bottom: 0;",
          errorButtonStyle: "",
          errorType: "",
          navHeight: 0,
          retry_bg: "",
          showErrorImage: !0,
          topStyle: ""
        },
        computed: {
          status() {
            return this.tryLoad ? "retry" : this.loadStatus
          },
          btnBgColor() {
            return n.store.getters.colorTheme
          },
          showNavigator() {
            return this.isShowNavigator && "tabbarPage" !== this.errorType
          },
          networkStatus() {
            return n.store.state.networkStatus
          }
        },
        watch: {
          status: {
            handler() {
              var t, r, e;
              "error" === this.status && i.a.reportError("pageError retryCount:".concat(this.retryCount), {
                desc: this.loadStatusErrorDesc,
                mainError: (r = y({}, this.loadErrorInfo), e = {
                  limitedType: (null == (t = this.loadErrorInfo) ? void 0 : t.limited) ? "触发限流" : "触发其他"
                }, d(r, l(e)))
              })
            },
            immediate: !0
          },
          "loadErrorInfo.limited": {
            handler(t) {
              "error" === this.status && this.init()
            },
            immediate: !0
          },
          networkStatus: {
            handler(t) {
              this.showErrorImage = t
            },
            immediate: !0
          }
        },
        lifetimes: {
          ready() {
            this.initCurrentPage(), this.init()
          }
        },
        methods: {
          init() {
            var t;
            (null == (t = this.loadErrorInfo) ? void 0 : t.limited) ? (this.getErrorInfo("limitFlow"), this.loadingBtnText = "现在试试") : (this.getErrorInfo("networkError"), this.loadingBtnText = "刷新"), this.setLoadingUrl()
          },
          setLoadingUrl() {
            const t = (0, s.a)("loading");
            this.loadingUrl = this.retryImageSrc = t.customImg || t.defaultImg || "https://images.qmai.cn/resource/20210824210816/2023/10/16/xiaohuojian.gif"
          },
          getErrorInfo(t) {
            const r = (0, s.a)(t);
            if (!r) return;
            this.errorImageSrc = r.customImg || r.defaultImg, this.customText = this.reasonText || r.text, this.errorMode = r.mode || 1, this.setErrorModeStyle(this.errorMode)
          },
          setErrorModeStyle(t) {
            if (1 == t && (this.errorImageStyle = "width: 600rpx; height: 600rpx"), 2 == t) {
              let t = "100vh";
              this.showNavigator && (t = "calc(100vh - ".concat(this.navHeight, "px)"), this.retry_bg = "width: 100vw;height:".concat(t)), this.errorImageStyle = "width: 100vw;height:".concat(t)
            }
            this.topStyle = this.showNavigator ? "top: ".concat(this.navHeight, "px") : ""
          },
          handleBottomStyle() {
            this.paddingBottom = "padding-bottom: calc(94rpx + constant(safe-area-inset-bottom));padding-bottom: calc(94rpx + env(safe-area-inset-bottom));"
          },
          tapRetryBtn() {
            if (this.tryLoad) return;
            this.tryLoad = !0, this.retryCount += 1;
            const t = (0, g.v)(3e3, 5e3, 0);
            setTimeout((() => {
              this.triggerEvent("reload")
            }), t - 2e3), setTimeout((() => {
              this.tryLoad = !1
            }), t)
          },
          initCurrentPage() {
            if (this.isFromErrorPage) return void(2 === this.errorMode && (this.errorButtonStyle = "position: absolute;"));
            const t = getCurrentPages(),
              r = t[t.length - 1];
            t.length > 1 ? (this.errorType = "navigatePage", this.paddingBottom = "") : (0, a.d)(r.route) ? (this.handleBottomStyle(), this.errorType = "tabbarPage") : (this.paddingBottom = "", this.errorType = "redirectPage")
          },
          getNavHeight(t) {
            this.navHeight = t && t.detail || 0
          }
        }
      })
    },
    295: function(t, r, e) {
      e(203);
      e.g.currentInject = {
        moduleId: "_09cfd823"
      }, e.g.currentInject.render = function(t, r, e, o) {
        o("showNavigator") && o("status"), "pending" === o("status") ? o("loadingUrl") : "error" === o("status") && (o("paddingBottom"), o("topStyle"), o("showErrorImage") && (o("errorImageSrc"), o("errorImageStyle")), 1 == o("errorMode") && o("customText"), o("refresh") && (o("errorMode"), o("btnBgColor"), o("errorButtonStyle"), o("loadingBtnText"))), "retry" === o("status") && (o("retry_bg"), o("showErrorImage") && (o("retryImageSrc"), o("retryImageStyle")), o("btnBgColor")), e()
      }
    }
  },
  function(t) {
    var r;
    r = 293, t(t.s = r)
  }
]);