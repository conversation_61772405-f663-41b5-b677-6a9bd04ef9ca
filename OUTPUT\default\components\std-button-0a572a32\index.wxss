.std-button {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    -webkit-text-size-adjust: 100%;
    align-items: center;
    -webkit-appearance: none;
    border-radius: 4rpx;
    box-sizing: border-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    font-size: 32rpx;
    height: 88rpx;
    justify-content: center;
    line-height: 1.2;
    padding: 0;
    position: relative;
    text-align: center;
    transition: opacity .2s;
    vertical-align: middle
}

.std-button:before {
    background-color: #000;
    border: inherit;
    border-color: #000;
    border-radius: inherit;
    content: " ";
    height: 100%;
    left: 50%;
    opacity: 0;
    position: absolute;
    top: 50%;
    transform: translate(-50%,-50%);
    width: 100%
}

.std-button:after {
    border-width: 0
}

.std-button--unclickable:after {
    display: none
}

.std-button--default {
    background: #fff;
    border: 1px solid #979797;
    color: #222
}

.std-button--primary {
    background: var(--std-primary-color,#ff5a00);
    border: 1px solid var(--std-primary-color,#ff5a00);
    color: #fff
}

.std-button--danger {
    background: #ee0a24;
    border: 1px solid #ee0a24;
    color: #fff
}

.std-button--warning {
    background: #ff976a;
    border: 1px solid #ff976a;
    color: #fff
}

.std-button--plain {
    background: #fff
}

.std-button--plain.std-button--primary {
    color: var(--std-primary-color,#ff5a00)
}

.std-button--plain.std-button--danger {
    color: #ee0a24
}

.std-button--plain.std-button--warning {
    color: #ff976a
}

.std-button--large {
    height: 100rpx;
    width: 100%
}

.std-button--normal {
    font-size: 28rpx;
    padding: 0 30rpx
}

.std-button--small {
    font-size: 28rpx;
    height: 70rpx;
    min-width: 120rpx;
    padding: 0 28rpx
}

.std-button--mini {
    display: inline-block;
    font-size: 20rpx;
    height: 44rpx;
    min-width: 100rpx
}

.std-button--mini+.std-button--mini {
    margin-left: 10rpx
}

.std-button--block {
    display: -ms-flexbox;
    display: flex;
    width: 100%
}

.std-button--round {
    border-radius: 999rpx
}

.std-button--square {
    border-radius: 0
}

.std-button--disabled {
    opacity: .5
}

.std-button__text {
    display: inline
}

.std-button__loading-text {
    margin-left: 8rpx
}

.std-button--hairline {
    border-width: 0;
    padding-top: 1px
}

.std-button--hairline:after {
    border-color: inherit;
    border-radius: 8rpx;
    border-width: 1px
}

.std-button--hairline.std-button--round:after {
    border-radius: 999rpx
}

.std-button--hairline.std-button--square:after {
    border-radius: 0
}
