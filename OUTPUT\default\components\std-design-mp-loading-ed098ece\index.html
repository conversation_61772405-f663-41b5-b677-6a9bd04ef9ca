	<style> </style> 	<page></page> 	<script> 	var __setCssStartTime__ = Date.now();			__wxAppCode__['components/std-design-mp-loading-ed098ece/index.wxss']();	
	var __setCssEndTime__ = Date.now();	(function() { 		var gf = $gwx( './components/std-design-mp-loading-ed098ece/index.wxml' ); 		if (window.__wxAppCodeReadyCallback__) { 			window.__wxAppCodeReadyCallback__(gf); 		} else { 			document.dispatchEvent(new CustomEvent( "generateFuncReady", { 				detail: { 					generateFunc: gf 			}})); 		} 	})(); 	</script> 	 