var g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [217], {
    287: function(e, t, r) {
      r.g.currentModuleId = "_ed098ece", r.g.currentCtor = Component, r.g.currentCtorType = "component", r.g.currentResourceType = "component", r(289), r.g.currentSrcMode = "wx", r(288)
    },
    288: function(e, t, r) {
      "use strict";
      r.r(t);
      var c = r(279),
        n = r(116);
      (0, c.a)({
        properties: {
          key: {
            type: String,
            value: "loading"
          }
        },
        data: {
          currentImgSrc: ""
        },
        lifetimes: {
          ready() {
            this.setDefaultData()
          }
        },
        methods: {
          setDefaultData() {
            const e = (0, n.f)(this.key);
            e && (this.currentImgSrc = e.customImg || e.defaultImg || "https://images.qmai.cn/s16/images/2020/07/14/6bf684c771bac130.gif")
          }
        }
      })
    },
    289: function(e, t, r) {
      r(203);
      r.g.currentInject = {
        moduleId: "_ed098ece"
      }, r.g.currentInject.render = function(e, t, r, c) {
        c("currentImgSrc"), r()
      }
    }
  },
  function(e) {
    var t;
    t = 287, e(e.s = t)
  }
]);