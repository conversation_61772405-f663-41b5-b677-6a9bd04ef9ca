var g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [218], {
    330: function(t, e, o) {
      o.g.currentModuleId = "_4d62dcc8", o.g.currentCtor = Component, o.g.currentCtorType = "component", o.g.currentResourceType = "component", o(332), o.g.currentSrcMode = "wx", o(331)
    },
    331: function(t, e, o) {
      "use strict";
      o.r(e);
      var n = o(279),
        l = o(4);
      (0, n.a)({
        properties: {
          show: {
            type: <PERSON><PERSON><PERSON>,
            observer(t) {
              !t && this.stopLoading()
            }
          },
          title: String,
          message: String,
          customStyle: String,
          messageAlign: String,
          overlayStyle: String,
          useSlot: Boolean,
          useTitleSlot: Boolean,
          useConfirmButtonSlot: Boolean,
          useCancelButtonSlot: Boolean,
          showCancelButton: Boolean,
          closeOnClickOverlay: Boolean,
          confirmButtonOpenType: String,
          width: {
            type: null
          },
          zIndex: {
            type: Number,
            value: 2e3
          },
          confirmButtonText: {
            type: String,
            value: "确认"
          },
          cancelButtonText: {
            type: String,
            value: "取消"
          },
          confirmButtonColor: {
            type: String,
            value: "#ff5a00"
          },
          cancelButtonColor: {
            type: String,
            value: "#666"
          },
          showConfirmButton: {
            type: Boolean,
            value: !0
          },
          overlay: {
            type: Boolean,
            value: !0
          },
          transition: {
            type: String,
            value: "scale"
          }
        },
        data: {
          loading: {
            confirm: !1,
            cancel: !1
          },
          callback: () => {}
        },
        methods: {
          onConfirm() {
            this.handleAction("confirm")
          },
          onCancel() {
            this.handleAction("cancel")
          },
          onClickOverlay() {
            this.handleAction("cancel")
          },
          close(t) {
            l.a.nextTick((() => {
              this.triggerEvent("close", {
                action: t
              });
              const {
                callback: e
              } = this.data;
              e && e(t, this)
            }))
          },
          stopLoading() {
            this.$forceUpdate({
              loading: {
                confirm: !1,
                cancel: !1
              }
            })
          },
          handleAction(t) {
            this.triggerEvent(t, {
              dialog: this,
              action: t
            })
          }
        }
      })
    },
    333: function(t, e, o) {
      var n = o(329),
        l = n.style,
        r = n.addUnit;
      t.exports = {
        addUnit: r,
        rootStyle: function(t) {
          var e = t.customStyle,
            o = t.width;
          return l([{
            width: r(o)
          }, e, "border-radius: 20rpx;"])
        }
      }
    },
    332: function(t, e, o) {
      var n = o(333);
      o.g.currentInject = {
        moduleId: "_4d62dcc8"
      }, o.g.currentInject.render = function(t, e, o, l) {
        l("show"), l("zIndex"), l("overlay"), l("transition"), n.rootStyle({
          width: l("width"),
          customStyle: l("customStyle")
        }), l("overlayStyle"), l("closeOnClickOverlay"), (l("title") || l("useTitleSlot")) && (l("message") || l("useSlot"), l("useTitleSlot") || l("title")), l("useSlot") || l("message") && l("title"), (l("showCancelButton") || l("showConfirmButton")) && (l("showCancelButton") && (l("useCancelButtonSlot") || (e("loading.cancel"), l("cancelButtonColor"), l("cancelButtonText"))), l("showConfirmButton") && (l("useConfirmButtonSlot") || (e("loading.confirm"), l("confirmButtonColor"), l("confirmButtonOpenType"), l("confirmButtonText")))), o()
      }
    }
  },
  function(t) {
    var e;
    e = 330, t(t.s = e)
  }
]);