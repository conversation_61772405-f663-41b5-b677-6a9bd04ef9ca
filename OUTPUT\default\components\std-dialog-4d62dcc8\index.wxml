<std-popup bind:close="onClickOverlay" closeOnClickOverlay="{{closeOnClickOverlay}}" customStyle="{{utils.rootStyle( {width:width,customStyle:customStyle} )}}" overlay="{{overlay}}" overlayStyle="{{overlayStyle}}" safeAreaInsetBottom="{{false}}" show="{{show}}" transition="{{transition}}" zIndex="{{zIndex}}">
    <view class="top-45_p_ w-85vw overflow-hidden text-24 bg-hex-fff rd-20">
        <view class="font-bold text-36 text-center {{!(message||useSlot)?'px-24 pt-24 pb-24':'pt-32 pb-32'}}" wx:if="{{title||useTitleSlot}}">
            <slot name="title" wx:if="{{useTitleSlot}}"></slot>
            <block wx:elif="{{title}}">{{title}}</block>
        </view>
        <slot wx:if="{{useSlot}}"></slot>
        <view class="max-h-60vh px-34 pb-34 overflow-y-auto text-24 text-center {{title?'pt-20 c-hex-333':'pt-34'}}" wx:elif="{{message}}">
            <text class="break-words">{{message}}</text>
        </view>
        <view class="b-t-1px b-t-solid b-t-hex-e5e5e5 flex" wx:if="{{showCancelButton||showConfirmButton}}">
            <block wx:if="{{showCancelButton}}">
                <slot name="cancel-button" wx:if="{{useCancelButtonSlot}}"></slot>
                <std-button bind:click="onCancel" class="flex-1 last-b-l-1px last-b-l-solid last-b-l-hex-e5e5e5" customStyle="color: {{cancelButtonColor}};border:none;" loading="{{loading.cancel}}" size="large" wx:else>{{cancelButtonText}}</std-button>
            </block>
            <block wx:if="{{showConfirmButton}}">
                <slot name="confirm-button" wx:if="{{useConfirmButtonSlot}}"></slot>
                <std-button bind:click="onConfirm" class="flex-1 last-b-l-1px last-b-l-solid last-b-l-hex-e5e5e5" customStyle="color: {{confirmButtonColor}};border:none;" loading="{{loading.confirm}}" openType="{{confirmButtonOpenType}}" size="large" wx:else>{{confirmButtonText}}</std-button>
            </block>
        </view>
    </view>
</std-popup>

<wxs module="utils" src="..\..\wxs\index4d630345.wxs"/>