var __async = (e, t, i) => new Promise(((n, r) => {
    var a = e => {
        try {
          s(i.next(e))
        } catch (e) {
          r(e)
        }
      },
      o = e => {
        try {
          s(i.throw(e))
        } catch (e) {
          r(e)
        }
      },
      s = e => e.done ? n(e.value) : Promise.resolve(e.value).then(a, o);
    s((i = i.apply(e, t)).next())
  })),
  g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [219], {
    304: function(e, t, i) {
      i.g.currentModuleId = "_197934ef", i.g.currentCtor = Component, i.g.currentCtorType = "component", i.g.currentResourceType = "component", i(306), i.g.currentSrcMode = "wx", i(305)
    },
    305: function(e, t, i) {
      "use strict";
      i.r(t);
      var n = i(279),
        r = i(4),
        a = i(35),
        o = i(123),
        s = i.n(o),
        c = i(36),
        l = i(76);
      let u = null;

      function f() {
        if (u) return u;
        return u = r.a.getSystemInfoSync(), u
      }
      const h = function() {
        const e = f(),
          {
            platform: t,
            system: i
          } = e;
        if ("devtools" === t) return "webp";
        let n, r;
        if ([n, r] = i.split(" "), "Android" === n || "iOS" === n && (0, l.a)(r, "14.0.0")) return "webp";
        return "normal"
      }();

      function d(e) {
        if (e < 1024) return "".concat(e, "B");
        if (e < 1048576) return "".concat((e / 1024).toFixed(2), "KB");
        return "".concat((e / 1024 / 1024).toFixed(2), "MB")
      }

      function m(e, t, i = {}) {
        if (!e) return;
        if (-1 === e.indexOf("images.qmai.cn")) return e;
        if (e.indexOf(".gif") > -1) return e;
        if (e.indexOf("?x-oss-process=") > -1 || e.indexOf("&x-oss-process=") > -1) return e;
        let n = "x-oss-process=image";
        if ("webp" === h && (n += "/format,webp"), !i.noResize) {
          const {
            pixelRatio: e = 3,
            screenWidth: i = 0
          } = f(), r = parseInt("".concat(i * e), 10);
          t = Number(t), (t = (0, c.pb)(t) ? Math.min(parseInt("".concat(t * (i / 750) * e), 10), r) : r) && !isNaN(t) && (n += "/resize,w_".concat(t, ",type_6"), n += "/sharpen,1")
        }
        return "".concat(e).concat(e.indexOf("?") > -1 ? "&" : "?").concat(n)
      }
      const p = 104857600,
        g = "".concat(r.a.env.USER_DATA_PATH, "/qm_image");
      let y = [],
        S = 0,
        x = null;
      const w = function() {
          if (x) return x;
          x = r.a.getFileSystemManager();
          try {
            x.accessSync(g)
          } catch (e) {
            x.mkdirSync(g)
          }
          return x
        }(),
        v = w.statSync("".concat(g), !0);

      function M(e, t) {
        return __async(this, null, (function*() {
          try {
            const {
              size: i
            } = yield function(e) {
              return new Promise(((t, i) => {
                w.getFileInfo({
                  filePath: e,
                  success: e => {
                    t(e)
                  },
                  fail: t => {
                    a.a.notify("本地图片getFileInfo失败", {
                      error: t,
                      tempFilePath: e
                    }), i(t)
                  }
                })
              }))
            }(e), n = "".concat(g, "/").concat(t);
            S + i > p && (a.a.notify("超出了最大存储", {
              fileTotalSize: d(S),
              fileTotal: y.length
            }), y.forEach((e => {
              if (e.quoted || S + i < p) return;
              w.unlinkSync("".concat(g).concat(e.path)), e.isDelete = !0, S -= e.size
            })), y = y.filter((e => !e.isDelete))), w.saveFileSync(e, n);
            const r = Math.floor(Date.now() / 1e3);
            return S += i, y.push({
              path: "/".concat(t),
              size: i,
              lastModifiedTime: r,
              lastModifiedTimeFormat: s().unix(r).format("YYYY-MM-DD HH:mm:ss")
            }), {
              filePath: n
            }
          } catch (t) {
            return a.a.notify("本地图片保存失败", {
              error: t,
              tempFilePath: e
            }), {
              filePath: e
            }
          }
        }))
      }
      Array.isArray(v) && (e => {
        "/" === e[0].path && e.shift();
        const t = e.sort(((e, t) => e.stats.lastModifiedTime - t.stats.lastModifiedTime)).reduce(((e, t) => {
          if (Date.now() - 1e3 * t.stats.lastModifiedTime > 1296e6) try {
            w.unlinkSync("".concat(g, "/").concat(t.path))
          } catch (e) {} else e.push({
            path: t.path,
            size: t.stats.size,
            lastModifiedTime: t.stats.lastModifiedTime,
            lastModifiedTimeFormat: s().unix(t.stats.lastModifiedTime).format("YYYY-MM-DD HH:mm:ss")
          }), S += t.stats.size;
          return e
        }), []);
        y = t
      })(v);
      const z = new Map;
      (0, n.a)({
        properties: {
          src: {
            type: String,
            value: ""
          },
          noResize: {
            type: Boolean,
            value: !1
          },
          noCache: {
            type: Boolean,
            value: !1
          },
          width: {
            type: String,
            value: ""
          },
          height: {
            type: String,
            value: ""
          },
          mode: {
            type: String,
            value: "scaleToFill"
          },
          showMenuByLongpress: {
            type: Boolean,
            value: !1
          },
          webp: {
            type: Boolean,
            value: !1
          },
          lazyLoad: {
            type: Boolean,
            value: !1
          },
          customStyle: {
            type: String,
            value: ""
          }
        },
        data: {
          imageSrc: ""
        },
        watch: {
          src: {
            handler(e) {
              e && this.handleFileCache(e)
            },
            immediate: !0
          }
        },
        methods: {
          onLoad(e) {
            this.triggerEvent("load", e.detail)
          },
          onError(e) {
            (function(e) {
              if (-1 === e.indexOf("images.qmai.cn")) return !1;
              if (e.indexOf("?x-oss-process=") > -1 || e.indexOf("&x-oss-process=") > -1) return !0;
              return !1
            })(this.imageSrc) && this.imageSrc.includes("format,avif") ? this.imageSrc = this.imageSrc.replace("format,avif", "format,webp") : this.imageSrc.indexOf("//usr/") > -1 && (this.imageSrc = m(this.src, this.width, {
              noResize: this.noResize
            })), this.triggerEvent("error", e.detail)
          },
          handleFileCache(e) {
            return __async(this, null, (function*() {
              if (this.noCache) this.imageSrc = m(e, this.width, {
                noResize: this.noResize
              });
              else try {
                const t = yield function(e, t) {
                  return new Promise((i => {
                    const n = function(e) {
                      e.includes("?") && (e = e.split("?")[0]);
                      const t = e.split("/");
                      return t[t.length - 1]
                    }(e);
                    w.access({
                      path: "".concat(g, "/").concat(n),
                      success() {
                        const e = y.find((e => e.path === "/".concat(n)));
                        e && (e.quoted = !0), i({
                          filePath: "".concat(g, "/").concat(n)
                        })
                      },
                      fail() {
                        const o = m(e, t),
                          s = z.get(o);
                        if (s) return void i(s);
                        const c = r.a.downloadFile({
                          url: o
                        }).then((e => __async(this, null, (function*() {
                          const t = yield M(e.tempFilePath, n);
                          return z.delete(o), {
                            filePath: t.filePath
                          }
                        })))).catch((e => (a.a.notify("本地图片downloadFile失败", {
                          error: e,
                          src: o
                        }), z.delete(o), {
                          filePath: o
                        })));
                        z.set(o, c), i(c)
                      }
                    })
                  }))
                }(e, this.width);
                this.imageSrc = t.filePath
              } catch (t) {
                this.imageSrc = m(e, this.width, {
                  noResize: this.noResize
                })
              }
            }))
          }
        }
      })
    },
    307: function(e) {
      e.exports = {
        imageStyle: function(e) {
          var t = e.width + "",
            i = e.height + "",
            n = {};
          if (t) {
            if (-1 === t.indexOf("%") && -1 === t.indexOf("px")) {
              var r = parseInt(t);
              isNaN(r) || (t += "rpx")
            }
            n.width = t
          }
          if (i) {
            if (-1 === i.indexOf("%") && -1 === i.indexOf("px")) {
              var a = parseInt(i);
              isNaN(a) || (i += "rpx")
            }
            n.height = i
          }
          return n
        }
      }
    },
    306: function(e, t, i) {
      var n = i(307),
        r = i(308);
      i(203);
      i.g.currentInject = {
        moduleId: "_197934ef"
      }, i.g.currentInject.render = function(e, t, i, a) {
        r.stringifyStyle(a("customStyle"), n.imageStyle({
          width: a("width"),
          height: a("height")
        })), a("imageSrc"), a("mode"), a("showMenuByLongpress"), a("webp"), a("lazyLoad"), i()
      }
    }
  },
  function(e) {
    var t;
    t = 304, e(e.s = t)
  }
]);