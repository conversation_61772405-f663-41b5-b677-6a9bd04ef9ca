var g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [220], {
    284: function(e, t, o) {
      o.g.currentModuleId = "_12d063da", o.g.currentCtor = Component, o.g.currentCtorType = "component", o.g.currentResourceType = "component", o(286), o.g.currentSrcMode = "wx", o(285)
    },
    285: function(e, t, o) {
      "use strict";
      o.r(t);
      var a = o(279),
        n = o(124);
      (0, a.a)({
        options: {
          multipleSlots: !0
        },
        properties: {
          patch: {
            type: Boolean,
            value: !1
          },
          title: {
            type: String,
            value: ""
          },
          fixed: {
            type: Boolean,
            value: !1
          },
          titleColor: {
            type: String,
            value: ""
          },
          background: {
            type: String,
            value: ""
          },
          backgroundImage: {
            type: String,
            value: ""
          },
          linearGradientBackground: {
            type: String,
            value: ""
          },
          customerTheme: {
            type: String,
            value: ""
          },
          notShowHome: {
            type: Boolean,
            value: !1
          },
          onlyShowHome: {
            type: Boolean,
            value: !1
          },
          isShowBack: {
            type: Boolean,
            value: !1
          },
          delta: {
            type: Number,
            value: 1
          },
          transparentBack: {
            type: Boolean,
            value: !0
          },
          showSearch: {
            type: Boolean,
            value: !1
          },
          showPinNav: {
            type: Boolean,
            value: !1
          },
          shouldExtNavColor: {
            type: Boolean,
            value: !0
          },
          showStoreName: {
            type: Boolean,
            value: !1
          },
          confirmBack: {
            type: Boolean,
            value: !1
          },
          confirmText: {
            type: String,
            value: "确定要退出当前页面吗?"
          }
        },
        data: {},
        methods: {
          getNavHeight(e) {
            this.triggerEvent("getNavHeight", e.detail)
          },
          back() {
            const {
              delta: e
            } = this;
            (0, n.e)(e), this.triggerEvent("back", {
              delta: e
            }, {})
          },
          search() {
            this.triggerEvent("search")
          }
        }
      })
    },
    286: function(e, t, o) {
      o.g.currentInject = {
        moduleId: "_12d063da"
      }, o.g.currentInject.render = function(e, t, o, a) {
        a("title"), a("fixed"), a("titleColor"), a("background"), a("backgroundImage"), a("linearGradientBackground"), a("customerTheme"), a("notShowHome"), a("onlyShowHome"), a("isShowBack"), a("delta"), a("transparentBack"), a("showSearch"), a("showPinNav"), a("shouldExtNavColor"), a("showStoreName"), a("confirmBack"), a("confirmText"), o()
      }
    }
  },
  function(e) {
    var t;
    t = 284, e(e.s = t)
  }
]);