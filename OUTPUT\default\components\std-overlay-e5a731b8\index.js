var g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [221], {
    334: function(e, o, n) {
      n.g.currentModuleId = "_e5a731b8", n.g.currentCtor = Component, n.g.currentCtorType = "component", n.g.currentResourceType = "component", n(336), n.g.currentSrcMode = "wx", n(335)
    },
    335: function(e, o, n) {
      "use strict";
      n.r(o);
      var r = n(279),
        t = n(36);
      (0, r.a)({
        options: {
          multipleSlots: !0
        },
        properties: {
          show: Boolean,
          customStyle: String,
          duration: {
            type: null,
            value: 300
          },
          zIndex: {
            type: Number,
            value: 1
          },
          lockScroll: {
            type: Boolean,
            value: !0
          },
          overlay: Boolean
        },
        methods: {
          onClick() {
            this.triggerEvent("click")
          },
          noop: t.Sb
        }
      })
    },
    336: function(e, o, n) {
      n.g.currentInject = {
        moduleId: "_e5a731b8"
      }, n.g.currentInject.render = function(e, o, n, r) {
        r("lockScroll"), r("show"), r("overlay"), r("zIndex"), r("customStyle"), r("duration"), n()
      }
    }
  },
  function(e) {
    var o;
    o = 334, e(e.s = o)
  }
]);