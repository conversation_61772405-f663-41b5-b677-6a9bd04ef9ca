	<style> </style> 	<page></page> 	<script> 	var __setCssStartTime__ = Date.now();			__wxAppCode__['components/std-popup-1d6ccb00/index.wxss']();	
	var __setCssEndTime__ = Date.now();	(function() { 		var gf = $gwx( './components/std-popup-1d6ccb00/index.wxml' ); 		if (window.__wxAppCodeReadyCallback__) { 			window.__wxAppCodeReadyCallback__(gf); 		} else { 			document.dispatchEvent(new CustomEvent( "generateFuncReady", { 				detail: { 					generateFunc: gf 			}})); 		} 	})(); 	</script> 	 