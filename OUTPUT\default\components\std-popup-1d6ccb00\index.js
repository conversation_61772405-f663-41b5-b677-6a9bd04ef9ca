var g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [222], {
    325: function(e, t, s) {
      s.g.currentModuleId = "_1d6ccb00", s.g.currentCtor = Component, s.g.currentCtorType = "component", s.g.currentResourceType = "component", s(327), s.g.currentSrcMode = "wx", s(326)
    },
    326: function(e, t, s) {
      "use strict";
      s.r(t);
      var n = s(279),
        o = s(36);
      const i = e => ({
        enter: "std-".concat(e, "-enter std-").concat(e, "-enter-active"),
        "enter-to": "std-".concat(e, "-enter-to std-").concat(e, "-enter-active"),
        leave: "std-".concat(e, "-leave std-").concat(e, "-leave-active"),
        "leave-to": "std-".concat(e, "-leave-to std-").concat(e, "-leave-active")
      });
      (0, n.a)({
        options: {
          multipleSlots: !0
        },
        properties: {
          mainStyle: String,
          closeStyle: String,
          show: {
            type: Boolean,
            value: !1,
            observer: "observeShow"
          },
          duration: {
            type: null,
            value: 300,
            observer: "observeDuration"
          },
          name: {
            type: String,
            value: "fade"
          },
          title: String,
          round: Boolean,
          closeable: Boolean,
          customStyle: String,
          overlayStyle: String,
          transition: {
            type: String,
            observer: "observeClass"
          },
          zIndex: {
            type: Number,
            value: 100
          },
          overlay: {
            type: Boolean,
            value: !0
          },
          closeIcon: {
            type: String,
            value: "close"
          },
          closeIconPosition: {
            type: String,
            value: "top-right"
          },
          closeOnClickOverlay: {
            type: Boolean,
            value: !0
          },
          position: {
            type: String,
            value: "center",
            observer: "observeClass"
          },
          safeAreaInsetBottom: {
            type: Boolean,
            value: !0
          },
          safeAreaInsetTop: {
            type: Boolean,
            value: !1
          },
          lockScroll: {
            type: Boolean,
            value: !0
          }
        },
        data: {
          type: "",
          inited: !1,
          display: !1
        },
        methods: {
          onClickCloseIcon() {
            this.triggerEvent("close")
          },
          onClickOverlay() {
            this.triggerEvent("click-overlay"), this.closeOnClickOverlay && this.triggerEvent("close")
          },
          observeClass() {
            const {
              transition: e,
              position: t,
              duration: s
            } = this, n = {
              name: e || t
            };
            "none" === e ? (n.duration = 0, this.originDuration = s) : null != this.originDuration && (n.duration = this.originDuration), this.$forceUpdate(n)
          },
          observeShow(e, t) {
            if (e === t) return;
            e ? this.enter() : this.leave()
          },
          enter() {
            const {
              duration: e,
              name: t
            } = this, s = i(t), n = (0, o.sb)(e) ? e.enter : e;
            if ("enter" === this.status) return;
            this.status = "enter", this.triggerEvent("before-enter"), (0, o.Ub)((() => {
              if ("enter" !== this.status) return;
              this.triggerEvent("enter"), this.$forceUpdate({
                inited: !0,
                display: !0,
                classes: s.enter,
                currentDuration: n
              }), (0, o.Ub)((() => {
                if ("enter" !== this.status) return;
                this.transitionEnded = !1, this.$forceUpdate({
                  classes: s["enter-to"]
                })
              }))
            }))
          },
          leave() {
            if (!this.display) return;
            const {
              duration: e,
              name: t
            } = this, s = i(t), n = (0, o.sb)(e) ? e.leave : e;
            this.status = "leave", this.triggerEvent("before-leave"), (0, o.Ub)((() => {
              if ("leave" !== this.status) return;
              this.triggerEvent("leave"), this.$forceUpdate({
                classes: s.leave,
                currentDuration: n
              }), (0, o.Ub)((() => {
                if ("leave" !== this.status) return;
                this.transitionEnded = !1, setTimeout((() => this.onTransitionEnd()), n), this.$forceUpdate({
                  classes: s["leave-to"]
                })
              }))
            }))
          },
          onTransitionEnd() {
            if (this.transitionEnded) return;
            this.transitionEnded = !0, this.triggerEvent("after-".concat(this.status));
            const {
              show: e,
              display: t
            } = this;
            !e && t && this.$forceUpdate({
              display: !1
            })
          }
        },
        ready() {
          !0 === this.show && this.observeShow(!0, !1)
        },
        attached() {
          this.observeClass()
        }
      })
    },
    328: function(e, t, s) {
      var n = s(329).style;
      e.exports = {
        popupStyle: function(e) {
          return n([{
            "z-index": e.zIndex,
            "-webkit-transition-duration": "".concat(e.currentDuration, "ms"),
            "transition-duration": "".concat(e.currentDuration, "ms")
          }, e.display ? null : "display: none", e.customStyle])
        }
      }
    },
    327: function(e, t, s) {
      var n = s(328),
        o = s(308);
      s.g.currentInject = {
        moduleId: "_1d6ccb00"
      }, s.g.currentInject.render = function(e, t, s, i) {
        i("overlay") && (i("show"), i("zIndex"), i("overlayStyle"), i("duration"), i("lockScroll")), i("inited") && (i("classes"), i("classes"), i("classes"), i("classes"), i("classes"), i("classes"), i("classes"), i("classes"), i("classes"), i("classes"), i("classes"), i("classes"), i("classes"), i("classes"), i("classes"), i("classes"), i("classes"), i("classes"), i("classes"), i("classes"), i("classes"), i("classes"), i("classes"), i("classes"), i("classes"), i("classes"), i("classes"), i("classes"), i("position"), i("position"), i("position"), i("position"), i("position"), i("round"), i("safeAreaInsetBottom"), i("safeAreaInsetTop"), o.stringifyStyle("", n.popupStyle({
          zIndex: i("zIndex"),
          currentDuration: i("currentDuration"),
          display: i("display"),
          customStyle: i("customStyle")
        }) + " ;" + i("mainStyle")), i("title"), i("closeable") && (i("closeIcon"), i("closeIcon"), i("closeIconPosition"), i("closeIconPosition"), i("closeIconPosition"), i("closeIconPosition"), i("closeIconPosition"), i("closeStyle"))), s()
      }
    }
  },
  function(e) {
    var t;
    t = 325, e(e.s = t)
  }
]);