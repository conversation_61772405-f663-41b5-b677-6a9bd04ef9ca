<std-overlay bind:click="onClickOverlay" class=" _1d6ccb00 " customStyle="{{overlayStyle}}" duration="{{duration}}" lockScroll="{{lockScroll}}" overlay="{{overlay}}" show="{{show}}" zIndex="{{zIndex}}" wx:if="{{overlay}}"></std-overlay>
<view bind:transitionend="onTransitionEnd" class=" {{classes==='std-center-enter std-center-enter-active'?'std-center-enter std-center-enter-active':''}} {{classes==='std-center-enter-to std-center-enter-active'?'std-center-enter-to std-center-enter-active':''}} {{classes==='std-center-leave std-center-leave-active'?'std-center-leave std-center-leave-active':''}} {{classes==='std-center-leave-to std-center-leave-active'?'std-center-leave-to std-center-leave-active':''}} {{classes==='std-top-enter std-top-enter-active'?'std-top-enter std-top-enter-active':''}} {{classes==='std-top-enter-to std-top-enter-active'?'std-top-enter-to std-top-enter-active':''}} {{classes==='std-top-leave std-top-leave-active'?'std-top-leave std-top-leave-active':''}} {{classes==='std-top-leave-to std-top-leave-active'?'std-top-leave-to std-top-leave-active':''}} {{classes==='std-bottom-enter std-bottom-enter-active'?'std-bottom-enter std-bottom-enter-active':''}} {{classes==='std-bottom-enter-to std-bottom-enter-active'?'std-bottom-enter-to std-bottom-enter-active':''}} {{classes==='std-bottom-leave std-bottom-leave-active'?'std-bottom-leave std-bottom-leave-active':''}} {{classes==='std-bottom-leave-to std-bottom-leave-active'?'std-bottom-leave-to std-bottom-leave-active':''}} {{classes==='std-left-enter std-left-enter-active'?'std-left-enter std-left-enter-active':''}} {{classes==='std-left-enter-to std-left-enter-active'?'std-left-enter-to std-left-enter-active':''}} {{classes==='std-left-leave std-left-leave-active'?'std-left-leave std-left-leave-active':''}} {{classes==='std-left-leave-to std-left-leave-active'?'std-left-leave-to std-left-leave-active':''}} {{classes==='std-right-enter std-right-enter-active'?'std-right-enter std-right-enter-active':''}} {{classes==='std-right-enter-to std-right-enter-active'?'std-right-enter-to std-right-enter-active':''}} {{classes==='std-right-leave std-right-leave-active'?'std-right-leave std-right-leave-active':''}} {{classes==='std-right-leave-to std-right-leave-active'?'std-right-leave-to std-right-leave-active':''}} {{classes==='std-fade-enter std-fade-enter-active'?'std-fade-enter std-fade-enter-active':''}} {{classes==='std-fade-enter-to std-fade-enter-active'?'std-fade-enter-to std-fade-enter-active':''}} {{classes==='std-fade-leave std-fade-leave-active'?'std-fade-leave std-fade-leave-active':''}} {{classes==='std-fade-leave-to std-fade-leave-active'?'std-fade-leave-to std-fade-leave-active':''}} {{classes==='std-scale-enter std-scale-enter-active'?'std-scale-enter std-scale-enter-active':''}} {{classes==='std-scale-enter-to std-scale-enter-active'?'std-scale-enter-to std-scale-enter-active':''}} {{classes==='std-scale-leave std-scale-leave-active'?'std-scale-leave std-scale-leave-active':''}} {{classes==='std-scale-leave-to std-scale-leave-active'?'std-scale-leave-to std-scale-leave-active':''}} std-popup {{position==='center'?'std-popup--center':''}} {{position==='top'?'std-popup--top':''}} {{position==='right'?'std-popup--right':''}} {{position==='bottom'?'std-popup--bottom':''}} {{position==='left'?'std-popup--left':''}} {{round?'std-popup--round':''}} {{safeAreaInsetBottom?'std-popup--safe':''}} {{safeAreaInsetTop?'std-popup--safeTop':''}} _1d6ccb00 " style="{{__stringify__.stringifyStyle('',utils.popupStyle( {zIndex:zIndex,currentDuration:currentDuration,display:display,customStyle:customStyle} )+' ;'+mainStyle)}}" wx:if="{{inited}}">
    <view class="std-popup__title _1d6ccb00 " wx:if="{{title}}">{{title}}</view>
    <slot class=" _1d6ccb00 "></slot>
    <view bind:tap="onClickCloseIcon" class=" {{closeIcon==='close'?'i-close1':''}} {{closeIcon==='chahao'?'i-chahao':''}} std-popup__close-icon {{closeIconPosition==='top-left'?'std-popup__close-icon--top-left':''}} {{closeIconPosition==='top-right'?'std-popup__close-icon--top-right':''}} {{closeIconPosition==='bottom-left'?'std-popup__close-icon--bottom-left':''}} {{closeIconPosition==='bottom-right'?'std-popup__close-icon--bottom-right':''}} {{closeIconPosition==='outter-bottom'?'std-popup__close-icon--outter-bottom':''}} _1d6ccb00 " style="{{closeStyle}}" wx:if="{{closeable}}"></view>
</view>

<wxs module="__stringify__" src="..\..\wxs\stringify2ad03c24.wxs"/>
<wxs module="utils" src="..\..\wxs\index1d6cf17d.wxs"/>