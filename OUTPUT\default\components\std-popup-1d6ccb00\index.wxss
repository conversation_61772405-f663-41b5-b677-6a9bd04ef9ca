.std-popup._1d6ccb00 {
    -webkit-overflow-scrolling: touch;
    animation: ease both;
    background-color: #fff;
    box-sizing: border-box;
    max-height: 100%;
    overflow-y: auto;
    position: fixed;
    transition-timing-function: ease
}

.std-popup--center._1d6ccb00 {
    left: 50%;
    top: 50%;
    transform: translate3d(-50%,-50%,0)
}

.std-popup--center.std-popup--round._1d6ccb00 {
    border-radius: 32rpx
}

.std-popup--top._1d6ccb00 {
    left: 0;
    top: 0;
    width: 100%
}

.std-popup--top.std-popup--round._1d6ccb00 {
    border-radius: 0 0 32rpx 32rpx
}

.std-popup--right._1d6ccb00 {
    right: 0;
    top: 50%;
    transform: translate3d(0,-50%,0)
}

.std-popup--right.std-popup--round._1d6ccb00 {
    border-radius: 32rpx 0 0 32rpx
}

.std-popup--bottom._1d6ccb00 {
    bottom: 0;
    left: 0;
    width: 100%
}

.std-popup--bottom.std-popup--round._1d6ccb00 {
    border-radius: 32rpx 32rpx 0 0
}

.std-popup--left._1d6ccb00 {
    left: 0;
    top: 50%;
    transform: translate3d(0,-50%,0)
}

.std-popup--left.std-popup--round._1d6ccb00 {
    border-radius: 0 32rpx 32rpx 0
}

.std-popup--safeTop._1d6ccb00 {
    padding-top: env(safe-area-inset-top)
}

.std-popup--safe._1d6ccb00 {
    padding-bottom: calc(env(safe-area-inset-bottom)/2)
}

.std-popup__close-icon._1d6ccb00 {
    color: #979797;
    font-size: 25rpx;
    margin: -16rpx;
    padding: 16rpx;
    position: absolute;
    z-index: 1
}

.std-popup__close-icon._1d6ccb00:active {
    opacity: .6
}

.std-popup__close-icon--top-left._1d6ccb00 {
    left: 40rpx;
    top: 40rpx
}

.std-popup__close-icon--top-right._1d6ccb00 {
    right: 40rpx;
    top: 44rpx
}

.std-popup__close-icon--bottom-left._1d6ccb00 {
    bottom: 40rpx;
    left: 40rpx
}

.std-popup__close-icon--bottom-right._1d6ccb00 {
    bottom: 40rpx;
    right: 40rpx
}

.std-popup__close-icon--outter-bottom._1d6ccb00 {
    bottom: -150rpx;
    box-sizing: border-box;
    color: #fff;
    font-size: 60rpx!important;
    left: 50%;
    margin: 0;
    padding: 30rpx;
    right: unset;
    top: unset;
    transform: translateX(-50%)
}

.std-popup__title._1d6ccb00 {
    color: #222;
    font-size: 36rpx;
    font-weight: 700;
    line-height: 50rpx;
    padding: 33rpx;
    text-align: center
}

.std-scale-enter-active._1d6ccb00,.std-scale-leave-active._1d6ccb00 {
    transition-property: opacity,transform
}

.std-scale-enter._1d6ccb00,.std-scale-leave-to._1d6ccb00 {
    opacity: 0;
    transform: translate3d(-50%,-50%,0) scale(.7)
}

.std-fade-enter-active._1d6ccb00,.std-fade-leave-active._1d6ccb00 {
    transition-property: opacity
}

.std-fade-enter._1d6ccb00,.std-fade-leave-to._1d6ccb00 {
    opacity: 0
}

.std-center-enter-active._1d6ccb00,.std-center-leave-active._1d6ccb00 {
    transition-property: opacity
}

.std-center-enter._1d6ccb00,.std-center-leave-to._1d6ccb00 {
    opacity: 0
}

.std-bottom-enter-active._1d6ccb00,.std-bottom-leave-active._1d6ccb00,.std-left-enter-active._1d6ccb00,.std-left-leave-active._1d6ccb00,.std-right-enter-active._1d6ccb00,.std-right-leave-active._1d6ccb00,.std-top-enter-active._1d6ccb00,.std-top-leave-active._1d6ccb00 {
    transition-property: transform
}

.std-bottom-enter._1d6ccb00,.std-bottom-leave-to._1d6ccb00 {
    transform: translate3d(0,100%,0)
}

.std-top-enter._1d6ccb00,.std-top-leave-to._1d6ccb00 {
    transform: translate3d(0,-100%,0)
}

.std-left-enter._1d6ccb00,.std-left-leave-to._1d6ccb00 {
    transform: translate3d(-100%,-50%,0)
}

.std-right-enter._1d6ccb00,.std-right-leave-to._1d6ccb00 {
    transform: translate3d(100%,-50%,0)
}
