	<style> </style> 	<page></page> 	<script> 	var __setCssStartTime__ = Date.now();			__wxAppCode__['components/std-toast-6146da8a/index.wxss']();	
	var __setCssEndTime__ = Date.now();	(function() { 		var gf = $gwx( './components/std-toast-6146da8a/index.wxml' ); 		if (window.__wxAppCodeReadyCallback__) { 			window.__wxAppCodeReadyCallback__(gf); 		} else { 			document.dispatchEvent(new CustomEvent( "generateFuncReady", { 				detail: { 					generateFunc: gf 			}})); 		} 	})(); 	</script> 	 