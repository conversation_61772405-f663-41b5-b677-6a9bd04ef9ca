var g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [224], {
    341: function(s, e, t) {
      t.g.currentModuleId = "_0f701595", t.g.currentCtor = Component, t.g.currentCtorType = "component", t.g.currentResourceType = "component", t(343), t.g.currentSrcMode = "wx", t(342)
    },
    342: function(s, e, t) {
      "use strict";
      t.r(e);
      var n = t(279),
        a = t(36);
      const r = s => ({
        enter: "std-".concat(s, "-enter std-").concat(s, "-enter-active"),
        "enter-to": "std-".concat(s, "-enter-to std-").concat(s, "-enter-active"),
        leave: "std-".concat(s, "-leave std-").concat(s, "-leave-active"),
        "leave-to": "std-".concat(s, "-leave-to std-").concat(s, "-leave-active")
      });
      (0, n.a)({
        properties: {
          customStyle: String,
          show: {
            type: Boolean,
            value: !1,
            observer: "observeShow"
          },
          duration: {
            type: null,
            value: 300,
            observer: "observeDuration"
          },
          name: {
            type: String,
            value: "fade"
          },
          overlay: Boolean
        },
        data: {
          type: "",
          inited: !1,
          display: !1
        },
        ready() {
          !0 === this.show && this.observeShow(!0, !1)
        },
        methods: {
          observeShow(s, e) {
            if (s === e) return;
            s ? this.enter() : this.leave()
          },
          enter() {
            const {
              duration: s,
              name: e
            } = this, t = r(e), n = (0, a.sb)(s) ? s.enter : s;
            if ("enter" === this.status) return;
            this.status = "enter", this.triggerEvent("before-enter"), (0, a.Ub)((() => {
              if ("enter" !== this.status) return;
              this.triggerEvent("enter"), this.inited = !0, this.display = !0, this.classes = t.enter, this.currentDuration = n, (0, a.Ub)((() => {
                if ("enter" !== this.status) return;
                this.transitionEnded = !1, this.classes = t["enter-to"]
              }))
            }))
          },
          leave() {
            if (!this.display) return;
            const {
              duration: s,
              name: e
            } = this, t = r(e), n = (0, a.sb)(s) ? s.leave : s;
            this.status = "leave", this.triggerEvent("before-leave"), (0, a.Ub)((() => {
              if ("leave" !== this.status) return;
              this.triggerEvent("leave"), this.classes = t.leave, this.currentDuration = n, (0, a.Ub)((() => {
                if ("leave" !== this.status) return;
                this.transitionEnded = !1, setTimeout((() => this.onTransitionEnd()), n), this.classes = t["leave-to"]
              }))
            }))
          },
          onTransitionEnd() {
            if (this.transitionEnded) return;
            this.transitionEnded = !0, this.triggerEvent("after-".concat(this.status));
            const {
              show: s,
              display: e
            } = this;
            !s && e && (this.display = !1)
          }
        }
      })
    },
    344: function(s, e, t) {
      var n = t(329).style;
      s.exports = {
        rootStyle: function(s) {
          return n([{
            "-webkit-transition-duration": "".concat(s.currentDuration, "ms"),
            "transition-duration": "".concat(s.currentDuration, "ms")
          }, s.display ? null : "display: none", s.customStyle])
        }
      }
    },
    343: function(s, e, t) {
      var n = t(344),
        a = t(308);
      t.g.currentInject = {
        moduleId: "_0f701595"
      }, t.g.currentInject.render = function(s, e, t, r) {
        r("inited") && (r("overlay"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), r("classes"), a.stringifyStyle("", n.rootStyle({
          currentDuration: r("currentDuration"),
          display: r("display"),
          customStyle: r("customStyle")
        }))), t()
      }
    }
  },
  function(s) {
    var e;
    e = 341, s(s.s = e)
  }
]);