var g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [225], {
    300: function(e, t, a) {
      a.g.currentModuleId = "_2645a974", a.g.currentCtor = Component, a.g.currentCtorType = "component", a.g.currentResourceType = "component", a(302), a.g.currentSrcMode = "wx", a(301)
    },
    301: function(e, t, a) {
      "use strict";
      a.r(t);
      var r = a(279),
        n = a(183),
        o = a(119),
        i = a(124),
        l = a(120),
        s = a(184),
        c = a(122),
        h = Object.defineProperty,
        p = Object.defineProperties,
        d = Object.getOwnPropertyDescriptors,
        b = Object.getOwnPropertySymbols,
        m = Object.prototype.hasOwnProperty,
        u = Object.prototype.propertyIsEnumerable,
        g = (e, t, a) => t in e ? h(e, t, {
          enumerable: !0,
          configurable: !0,
          writable: !0,
          value: a
        }) : e[t] = a;
      const T = (0, o.g)(),
        v = T.tabBarExt || {},
        f = {
          navTemplate: v.navTemplate,
          borderStyle: v.borderStyle || "",
          selectedColor: v.selectedColor,
          color: v.color || "",
          backgroundColor: v.backgroundColor,
          list: T.tabBarList
        };
      var y, C;
      (0, r.a)({
        properties: {
          path: {
            type: String,
            value: ""
          }
        },
        data: {
          color: "",
          selectedColor: "",
          backgroundColor: "",
          borderStyle: "",
          navTemplate: 1,
          list: null,
          isIhponX: !1,
          currentIsExtTabBar: !1
        },
        computed: (y = ((e, t) => {
          for (var a in t || (t = {})) m.call(t, a) && g(e, a, t[a]);
          if (b)
            for (var a of b(t)) u.call(t, a) && g(e, a, t[a]);
          return e
        })({}, l.store.mapState(["systemInfo", "modeLarge"])), C = {
          tabBar() {
            return l.designConfigStore.state.tabBar
          },
          currentTabbar() {
            var e;
            return null == (e = f.list) ? void 0 : e.find((e => e.pagePath === this.path))
          },
          isShowTabbar() {
            return l.store.state.isShowTabbar
          },
          isShowGrayFilter() {
            return "gray" === l.store.state.configCenterByKeys["Gray.home.page"] && "pages/index/index" === this.path
          }
        }, p(y, d(C))),
        watch: {
          tabBar: {
            handler(e) {
              e ? this.updateTabBarWithDesign(e) : this.currentIsExtTabBar || this.updateTabBarWithExt(f)
            },
            immediate: !0
          }
        },
        attached() {
          this.list || this.updateTabBarWithExt(f), this.isIhponX = this.isSafeView(), l.store.commit("setIsShowTabbar", "pages/index/index" !== this.path)
        },
        detached() {},
        ready() {},
        methods: {
          updateTabBarWithDesign(e) {
            this.currentIsExtTabBar = !1, this.handleTabBar(e)
          },
          updateTabBarWithExt(e) {
            this.currentIsExtTabBar = !0, this.handleTabBar(f)
          },
          handleTabBar(e) {
            this.color = e.color, this.selectedColor = e.selectedColor, this.backgroundColor = e.backgroundColor || "#ffffff", this.borderStyle = "black" === e.borderStyle ? "#e1e1e1" : e.borderStyle, this.navTemplate = e.navTemplate;
            const t = e.list.map((e => (-1 === e.iconPath.indexOf("https://") && (e.iconPath = "https://images.qmai.cn/resource/20210913185016/2021/10/29/".concat(e.iconPath.replace("image/", ""))), -1 === e.selectedIconPath.indexOf("https://") && (e.selectedIconPath = "https://images.qmai.cn/resource/20210913185016/2021/10/29/".concat(e.selectedIconPath.replace("image/", ""))), e)));
            if (2 != t.length && 4 != t.length || 3 == e.navTemplate || (v.navTemplate = 1), 2 == e.navTemplate && t.length) {
              t[Math.floor(T.tabBarList.length / 2)].isMain = 1
            }
            if ((4 == e.navTemplate || 5 == e.navTemplate) && t.length) {
              t[Math.floor(t.length / 2)].isConcave = 1
            }
            this.list = t
          },
          switchTab(e) {
            var t;
            (0, c._)();
            const {
              dataset: a
            } = e.currentTarget, r = a.path;
            if (r == this.path) return;
            (0, n.d)(r), (0, s.c)("Common_CommonTab_BtnClick", {
              button_name: null == (t = this.currentTabbar) ? void 0 : t.text
            }), (0, i.f)("/".concat(r))
          },
          isSafeView() {
            {
              const {
                systemInfo: e
              } = this, {
                screenHeight: t
              } = e, {
                bottom: a
              } = e.safeArea;
              return t !== a
            }
          }
        }
      })
    },
    302: function(e, t, a) {
      var r = a(303);
      a(203);
      a.g.currentInject = {
        moduleId: "_2645a974"
      }, a.g.currentInject.render = function(e, t, a, n) {
        n("list") && n("list").length && n("isShowTabbar") && (n("navTemplate"), n("isIhponX") || n("navTemplate"), 4 == n("navTemplate") || 5 == n("navTemplate") || 3 == n("navTemplate") || n("backgroundColor"), 2 == n("navTemplate") || 4 == n("navTemplate") || n("navTemplate"), n("isShowGrayFilter"), n("navTemplate"), 1 == n("navTemplate") && n("borderStyle"), 4 != n("navTemplate") && 5 != n("navTemplate") || (n("backgroundColor"), n("navTemplate"), n("navTemplate")), e(n("list"), (function(e, t) {
          r.g(n("currentTabbar"), ["text"]), n("navTemplate"), 1 == n("navTemplate") || 4 == n("navTemplate") || 5 == n("navTemplate") || n("backgroundColor"), e.isMain || e.isConcave, e.isMain ? n("path") === e.pagePath ? e.selectedIconPath : e.iconPath : e.isConcave ? (n("path"), e.pagePath) : (n("path") === e.pagePath ? e.selectedIconPath : e.iconPath, n("modeLarge"), n("path") === e.pagePath ? n("selectedColor") : n("color"))
        }))), a()
      }
    }
  },
  function(e) {
    var t;
    t = 300, e(e.s = t)
  }
]);