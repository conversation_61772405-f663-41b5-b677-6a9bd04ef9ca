var g = {};
g.c = require("../bundle.js"), (g.c = g.c || []).push([
  [226], {
    277: function(e, n, r) {
      r.g.currentModuleId = "_260d9e76", r.g.currentCtor = Component, r.g.currentCtorType = "component", r.g.currentResourceType = "component", r(280), r.g.currentSrcMode = "wx", r(278)
    },
    278: function(e, n, r) {
      "use strict";
      r.r(n), (0, r(279).a)({
        data: {
          path: ""
        }
      })
    },
    280: function(e, n, r) {
      r.g.currentInject = {
        moduleId: "_260d9e76"
      }, r.g.currentInject.render = function(e, n, r, t) {
        t("path"), r()
      }
    }
  },
  function(e) {
    var n;
    n = 277, e(e.s = n)
  }
]);