var __async = (e, t, i) => new Promise(((o, r) => {
    var n = e => {
        try {
          s(i.next(e))
        } catch (e) {
          r(e)
        }
      },
      a = e => {
        try {
          s(i.throw(e))
        } catch (e) {
          r(e)
        }
      },
      s = e => e.done ? o(e.value) : Promise.resolve(e.value).then(n, a);
    s((i = i.apply(e, t)).next())
  })),
  g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [227], {
    296: function(e, t, i) {
      i.g.currentModuleId = "_9df716e6", i.g.currentCtor = Component, i.g.currentCtorType = "component", i.g.currentResourceType = "component", i(299), i.g.currentSrcMode = "wx", i(297)
    },
    297: function(e, t, i) {
      "use strict";
      i.r(t);
      var o = i(279),
        r = i(4),
        n = i(81),
        a = i(36),
        s = i(116),
        c = i(298),
        h = Object.defineProperty,
        l = Object.defineProperties,
        u = Object.getOwnPropertyDescriptors,
        p = Object.getOwnPropertySymbols,
        g = Object.prototype.hasOwnProperty,
        d = Object.prototype.propertyIsEnumerable,
        m = (e, t, i) => t in e ? h(e, t, {
          enumerable: !0,
          configurable: !0,
          writable: !0,
          value: i
        }) : e[t] = i;
      const f = ["wx://component-export"];
      (0, o.a)({
        behaviors: f,
        export () {
          return this
        },
        properties: {
          traceScreenType: {
            type: String,
            value: ""
          },
          force: {
            type: Boolean,
            value: !1
          },
          registerSource: {
            type: Number,
            value: 0
          },
          activityId: {
            type: String,
            value: ""
          },
          channelCode: {
            type: String,
            value: ""
          },
          inviteInfo: {
            type: String,
            value: ""
          },
          loginScene: {
            type: String,
            value: "refresh"
          },
          update: {
            type: Boolean,
            value: !1
          }
        },
        data: {
          isShow: !1,
          theme: "#40ba5a",
          agreeOpen: !1,
          isOld: !0,
          list: [],
          isAgreeTip: !0,
          isAgreeOn: !1,
          authorizedDialogBgImg: "",
          showPanel: "agree",
          isTrial: !1,
          isTiktokDaojia: !1,
          showProtocolPopup: !1
        },
        computed: {
          loginBtnStyle() {
            return "border: 1px solid ".concat(this.colorTheme, "; background: ").concat(this.colorTheme, "; color: #fff;")
          },
          skipBtnStyle() {
            return "border: 1px solid ".concat(this.colorTheme, "; background: #fff; color: ").concat(this.colorTheme, "; height: 88rpx; line-height: 88rpx; border-radius: 88rpx; width: 520rpx; margin: 18rpx auto 0; font-size: 34rpx;box-sizing: border-box;")
          },
          area() {
            const e = n.stdStore.state.locale,
              t = e.split("-")[0];
            if ("zh" === t) return e;
            return t
          },
          loginWithPhone() {
            return this.$t("authorization.loginWithPhone")
          },
          confirmText() {
            return this.$t("authorization.onKeyLoginWithPhone")
          },
          skipTemporarily() {
            return this.$t("authorization.skipTemporarily")
          },
          welcomeJoin() {
            return this.$t("authorization.welcomeJoin")
          },
          joinEnjoy() {
            return this.$t("authorization.joinEnjoy")
          },
          pleaseReadAndAgree() {
            return this.$t("authorization.pleaseReadAndAgree")
          },
          etcContent() {
            return this.$t("authorization.etcContent")
          },
          requestAuthorizationDesc() {
            return this.$t("authorization.requestAuthorizationDesc")
          },
          privacyPolicy() {
            return this.$t("authorization.privacyPolicy")
          },
          membershipAgreement() {
            return this.$t("authorization.membershipAgreement")
          },
          colorTheme() {
            return n.stdStore.state.primaryColor
          },
          storeName() {
            return n.stdStore.state.brandName
          },
          brandLogo() {
            return n.stdStore.state.brandLogo
          },
          isAuth() {
            return n.stdStore.getters.isAuth
          },
          isNeedWechatPublicAuth() {
            const {
              wechatPublicOpenId: e
            } = n.stdStore.state || {};
            return !(!(0, s.E)("getWechatOpenidAppid") || e)
          },
          isUStore() {
            return !1
          }
        },
        lifetimes: {
          attached() {
            this.isTrial = !!r.a.getExtConfigSync().isTrial, this.list = [{
              id: 1,
              name: this.membershipAgreement
            }, {
              id: 2,
              name: this.privacyPolicy
            }]
          }
        },
        methods: {
          togglePanel(e) {
            this.showPanel = e
          },
          getProtocolList() {
            return (0, c.b)({
              position: 1
            }).then((e => {
              if (1 === e.agreementSwitch) {
                if (e.agreementList && e.agreementList.length) {
                  const t = this.membershipAgreement,
                    i = this.privacyPolicy;
                  this.list = e.agreementList.map((e => {
                    return o = ((e, t) => {
                      for (var i in t || (t = {})) g.call(t, i) && m(e, i, t[i]);
                      if (p)
                        for (var i of p(t)) d.call(t, i) && m(e, i, t[i]);
                      return e
                    })({}, e), r = {
                      name: "用户协议" === e.name ? t : "隐私协议" === e.name ? i : e.name
                    }, l(o, u(r));
                    var o, r
                  })), this.isOld = !1
                }
                return e.agreementList || []
              }
            })).catch((() => (this.list = [], [])))
          },
          show(e) {
            if (this.autoTrack("Common_LoginWindow_PopUp", {}), "homeIndex" === e) return void(0, s.v)("/pages/user/info/info");
            this.authorizedDialogBgImg = (0, s.g)("authorizedDialogBgImg"), r.a.checkSession && r.a.checkSession({
              fail() {
                r.a.login()
              }
            }), this.getProtocolList().then((() => {
              this.isShow = !0
            }))
          },
          hide() {
            this.isShow = !1, this.showProtocolPopup = !1
          },
          hideAfter() {
            0
          },
          skipLogin() {
            this.autoTrack("Common_LoginWindow_BtnClick", {
              button_name: "暂时跳过"
            }), this.cancel()
          },
          cancel() {
            this.hide(), this.triggerEvent("fail", !1), this.triggerEvent("complete", !1), getApp().$emit("authorize"), this.authorizeRejected()
          },
          tapLogin() {
            this.autoTrack("Common_LoginWindow_BtnClick", {
              button_name: "手机号一键登录"
            })
          },
          getPhoneNumber(e) {
            this.hide(), this.triggerEvent("success", e.detail), this.triggerEvent("complete", e.detail), this.authorizeSuccess(e.detail), getApp().$emit("authorize")
          },
          checkAgreeOpen() {
            if (this.isAgreeOn) return;
            this.agreeOpen || (this.isUStore ? this.showProtocolPopup = !0 : (this.isAgreeOn = !0, setTimeout((() => {
              this.isAgreeOn = !1
            }), 800)))
          },
          checkboxChange() {
            this.agreeOpen = !this.agreeOpen
          },
          toProtocol(e) {
            const t = e.currentTarget.dataset.id;
            this.isOld ? (0, s.v)("/subpackages/protocol/index?type=".concat(t)) : (0, s.v)("/subpackages/protocol/detail/index?id=".concat(t))
          },
          applyAuthorize({
            success: e,
            fail: t,
            complete: i
          }) {
            "function" == typeof e && (this.successHandler = e), "function" == typeof t && (this.failHandler = t), "function" == typeof i && (this.completeHandler = i), this.isAuth ? this.noNeedAuthorize() : this.show()
          },
          noNeedAuthorize() {
            this.failHandler && this.failHandler({
              message: "authorize:No Need Authorize"
            }), this.autoTrack("Common_LoginOperate_Result", {
              is_success: !1,
              fail_code: "",
              fail_reason: "authorize:No Need Authorize"
            }), this.completeHandler && this.completeHandler(), this.clearAuthorizeCallback()
          },
          authorizeSuccess(e) {
            this.successHandler && this.successHandler({
              message: "authorize:Success"
            }), this.autoTrack("Common_LoginOperate_Result", {
              is_success: !0,
              type: (null == e ? void 0 : e.newGroupRegister) ? "首次注册" : "绑定",
              fail_code: "",
              fail_reason: ""
            }), this.completeHandler && this.completeHandler(), this.clearAuthorizeCallback()
          },
          authorizeRejected() {
            this.failHandler && this.failHandler({
              message: "authorize:Rejected"
            }), this.completeHandler && this.completeHandler(), this.autoTrack("Common_LoginOperate_Result", {
              is_success: !1,
              fail_code: "",
              fail_reason: "authorize:Rejected"
            }), this.clearAuthorizeCallback()
          },
          clearAuthorizeCallback() {
            this.successHandler = null, this.failHandler = null, this.completeHandler = null
          },
          showTrialToast() {
            r.a.showToast({
              title: "试运营期间暂时无法授权手机号"
            })
          },
          navToWXPublicAuth: (0, a.bc)((() => __async(this, null, (function*() {
            const e = (0, s.E)("getWechatOpenidAppid"),
              {
                appid: t
              } = r.a.getExtConfigSync() || {},
              i = yield(0, s.h)(), o = "".concat(i.pth5Url, "/#/getWxOpenId?appId=").concat(e || "", "&minAppId=").concat(t || ""), n = "/pages/webView/index?path=".concat(encodeURIComponent(o));
            (0, s.v)(n)
          }))), 500),
          onCloseProtocolPopup() {
            this.showProtocolPopup = !1
          },
          autoTrack(e, t) {
            getApp().tracker && getApp().tracker.track(e, t)
          }
        }
      })
    },
    299: function(e, t, i) {
      i(203);
      i.g.currentInject = {
        moduleId: "_9df716e6"
      }, i.g.currentInject.render = function(e, t, i, o) {
        o("isShow"), o("authorizedDialogBgImg"), o("authorizedDialogBgImg"), o("authorizedDialogBgImg"), o("authorizedDialogBgImg"), o("authorizedDialogBgImg"), o("authorizedDialogBgImg") || (o("brandLogo"), o("isTiktokDaojia") || (o("welcomeJoin"), o("storeName"), o("joinEnjoy"))), o("isTrial") ? (o("colorTheme"), o("confirmText")) : o("agreeOpen") ? (o("loginBtnStyle"), o("confirmText")) : (o("confirmText"), o("loginBtnStyle")), o("skipBtnStyle"), o("skipTemporarily"), o("isAgreeOn"), o("agreeOpen") && o("colorTheme"), o("requestAuthorizationDesc"), e(o("list"), (function(e, t) {
          o("list").length
        })), o("etcContent"), o("agreeOpen") || (o("isAgreeTip"), o("pleaseReadAndAgree")), o("showProtocolPopup"), e(o("list"), (function(e, t) {})), o("force"), o("update"), o("registerSource"), o("traceScreenType"), o("activityId"), o("inviteInfo"), o("channelCode"), o("loginScene"), i()
      }, i.g.currentInject.injectComputed = {
        _i1() {
          return this.$t("authorization.ihaveRead")
        },
        _i2() {
          return this.$t("authorization.contentAndAgree")
        },
        _i3() {
          return this.$t("authorization.ihaveRead")
        },
        _i4() {
          return this.$t("authorization.contentAndAgree")
        }
      }
    }
  },
  function(e) {
    var t;
    t = 296, e(e.s = t)
  }
]);