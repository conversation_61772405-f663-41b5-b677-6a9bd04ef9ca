var g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [239], {
    231: function(e, r, c) {
      c.g.currentModuleId = "_085ed604", c.g.currentCtor = Page, c.g.currentCtorType = "page", c.g.currentResourceType = "page", c(233), c.g.currentSrcMode = "wx", c(232)
    },
    232: function(e, r, c) {
      "use strict";
      c.r(r), (0, c(194).a)({})
    },
    233: function(e, r, c) {
      c.g.currentInject = {
        moduleId: "_085ed604"
      }
    }
  },
  function(e) {
    var r;
    r = 231, e(e.s = r)
  }
]);