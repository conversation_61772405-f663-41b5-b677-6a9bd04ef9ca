var g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [240], {
    234: function(e, r, t) {
      t.g.currentModuleId = "_1b291ff4", t.g.currentCtor = Page, t.g.currentCtorType = "page", t.g.currentResourceType = "page", t(236), t.g.currentSrcMode = "wx", t(235)
    },
    235: function(e, r, t) {
      "use strict";
      t.r(r);
      var n = t(194),
        c = t(4),
        o = t(127);
      (0, n.a)({
        data: {
          url: ""
        },
        onLoad(e) {
          c.a.setNavigationBarTitle({
            title: e.title || "积分商城"
          });
          const r = (0, o.a)("tiktokServiceUrl"),
            t = e && e.redirect ? decodeURIComponent(e.redirect) : r;
          this.url = t
        },
        onUnload() {
          (0, o.c)("tiktokServiceUrl")
        }
      })
    },
    236: function(e, r, t) {
      t.g.currentInject = {
        moduleId: "_1b291ff4"
      }, t.g.currentInject.render = function(e, r, t, n) {
        n("url"), t()
      }
    }
  },
  function(e) {
    var r;
    r = 234, e(e.s = r)
  }
]);