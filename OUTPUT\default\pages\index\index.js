var __async = (e, n, t) => new Promise(((o, i) => {
    var d = e => {
        try {
          r(t.next(e))
        } catch (e) {
          i(e)
        }
      },
      s = e => {
        try {
          r(t.throw(e))
        } catch (e) {
          i(e)
        }
      },
      r = e => e.done ? o(e.value) : Promise.resolve(e.value).then(d, s);
    r((t = t.apply(e, n)).next())
  })),
  g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [241], {
    192: function(e, n, t) {
      t.g.currentModuleId = "_8bbffdf8", t.g.currentCtor = Page, t.g.currentCtorType = "page", t.g.currentResourceType = "page", t(196), t.g.currentSrcMode = "wx", t(193)
    },
    193: function(e, n, t) {
      "use strict";
      t.r(n);
      var o = t(194),
        i = t(35),
        d = t(134);
      (0, o.a)({
        data: {
          isLoad: !1,
          componentMounted: !1,
          pageOptions: {},
          indexRef: null,
          path: ""
        },
        computed: {},
        onLoad(e) {
          return __async(this, null, (function*() {
            this.isLoad = !0, this.pageOptions = e
          }))
        },
        onShow() {
          this.componentMounted && this.indexRef && this.indexRef.componentOnShow && this.indexRef.componentOnShow()
        },
        onReady() {
          this.indexRef && this.indexRef.componentOnReady && this.indexRef.componentOnReady()
        },
        onHide() {
          this.indexRef && this.indexRef.componentOnHide && this.indexRef.componentOnHide()
        },
        onUnload() {
          d.a.off("setHomeCoverObj"), this.indexRef && this.indexRef.componentOnUnload && this.indexRef.componentOnUnload()
        },
        onShareAppMessage(e) {
          return this.indexRef && this.indexRef.componentOnShareAppMessage && this.indexRef.componentOnShareAppMessage(e)
        },
        methods: {
          asyncComponentMounted() {
            try {
              this.indexRef = this.selectComponent("#index"), this.indexRef.componentOnLoad(this.pageOptions), this.indexRef.componentOnShow(), this.componentMounted = !0
            } catch (e) {
              i.a.reportError("index asyncComponentMounted error", e)
            }
          }
        }
      })
    },
    196: function(e, n, t) {
      t.g.currentInject = {
        moduleId: "_8bbffdf8"
      }, t.g.currentInject.render = function(e, n, t, o) {
        o("componentMounted"), o("isLoad"), t()
      }
    }
  },
  function(e) {
    var n;
    n = 192, e(e.s = n)
  }
]);