var __async = (e, n, t) => new Promise(((o, r) => {
    var i = e => {
        try {
          s(t.next(e))
        } catch (e) {
          r(e)
        }
      },
      c = e => {
        try {
          s(t.throw(e))
        } catch (e) {
          r(e)
        }
      },
      s = e => e.done ? o(e.value) : Promise.resolve(e.value).then(i, c);
    s((t = t.apply(e, n)).next())
  })),
  g = {};
g.c = require("../../../bundle.js"), (g.c = g.c || []).push([
  [255], {
    271: function(e, n, t) {
      t.g.currentModuleId = "_309efbe1", t.g.currentCtor = Page, t.g.currentCtorType = "page", t.g.currentResourceType = "page", t(273), t.g.currentSrcMode = "wx", t(272)
    },
    272: function(e, n, t) {
      "use strict";
      t.r(n);
      var o = t(194),
        r = t(35);
      (0, o.a)({
        data: {
          componentMounted: !1,
          pageOptions: {},
          path: ""
        },
        onLoad(e) {
          return __async(this, null, (function*() {
            this.pageOptions = e;
            try {
              this.indexRef = yield this.$asyncRefs.index, this.indexRef.componentOnLoad(this.pageOptions), this.indexRef.componentOnShow(), this.componentMounted = !0
            } catch (e) {
              r.a.reportError("orderlistAsyncComponentMountedError", e)
            }
          }))
        },
        onShow() {
          this.indexRef && this.indexRef.componentOnShow && this.indexRef.componentOnShow()
        },
        onHide() {
          this.indexRef && this.indexRef.componentOnHide && this.indexRef.componentOnHide()
        },
        onUnload() {
          this.indexRef && this.indexRef.componentOnUnload && this.indexRef.componentOnUnload()
        },
        onShareAppMessage(e) {
          return this.indexRef && this.indexRef.componentOnShareAppMessage && this.indexRef.componentOnShareAppMessage(e)
        }
      })
    },
    273: function(e, n, t) {
      t.g.currentInject = {
        moduleId: "_309efbe1"
      }, t.g.currentInject.render = function(e, n, t, o) {
        o("componentMounted"), t()
      }, t.g.currentInject.getRefsData = function() {
        return [{
          key: "index",
          selector: ".ref_index_1",
          type: "component",
          all: !1
        }]
      }
    }
  },
  function(e) {
    var n;
    n = 271, e(e.s = n)
  }
]);