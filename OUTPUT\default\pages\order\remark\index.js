var g = {};
g.c = require("../../../bundle.js"), (g.c = g.c || []).push([
  [256], {
    274: function(e, r, t) {
      t.g.currentModuleId = "_3fb14203", t.g.currentCtor = Page, t.g.currentCtorType = "page", t.g.currentResourceType = "page", t(276), t.g.currentSrcMode = "wx", t(275)
    },
    275: function(e, r, t) {
      "use strict";
      t.r(r);
      var o, a, c = t(194),
        n = t(124),
        i = t(120),
        s = t(134),
        u = t(122),
        m = Object.defineProperty,
        l = Object.defineProperties,
        p = Object.getOwnPropertyDescriptors,
        k = Object.getOwnPropertySymbols,
        h = Object.prototype.hasOwnProperty,
        d = Object.prototype.propertyIsEnumerable,
        f = (e, r, t) => r in e ? m(e, r, {
          enumerable: !0,
          configurable: !0,
          writable: !0,
          value: t
        }) : e[r] = t;
      (0, c.a)({
        data: {
          remarkSelect: [],
          remark: "",
          placeholder: ""
        },
        computed: (o = ((e, r) => {
          for (var t in r || (r = {})) h.call(r, t) && f(e, t, r[t]);
          if (k)
            for (var t of k(r)) d.call(r, t) && f(e, t, r[t]);
          return e
        })({}, i.store.mapState(["shopConfig"])), a = {
          colorTheme() {
            return i.store.getters.colorTheme
          }
        }, l(o, p(a))),
        onLoad(e) {
          this.remark = e.remark, this.remarkSelect = "3" === e.orderType ? this.shopConfig.takeawayQuickRemarksList : this.shopConfig.eatInQuickRemarksList;
          const r = this.$t("takefood.cannotDeliveryTip"),
            t = this.$t("takefood.inputFavorRequire");
          this.placeholder = "3" === e.orderType ? r : t
        },
        remarkInput(e) {
          this.remark = e.detail.value
        },
        selectRemark(e) {
          const r = (0, u.r)(e, "content"),
            t = this.remark ? "".concat(this.remark, " ").concat(r) : this.remark + r;
          this.remark = t
        },
        submitRemark() {
          s.a.fire("remark", this.remark), (0, n.e)()
        }
      })
    },
    276: function(e, r, t) {
      t.g.currentInject = {
        moduleId: "_3fb14203"
      }, t.g.currentInject.render = function(e, r, t, o) {
        o("placeholder"), o("remark"), o("remarkSelect") && o("remarkSelect").length && (o("_i1"), e(o("remarkSelect"), (function(e, r) {}))), o("colorTheme"), o("_i2"), t()
      }, t.g.currentInject.injectComputed = {
        _i1() {
          return this.$t("takefood.quickInput")
        },
        _i2() {
          return this.$t("takefood.determine")
        }
      }
    }
  },
  function(e) {
    var r;
    r = 274, e(e.s = r)
  }
]);