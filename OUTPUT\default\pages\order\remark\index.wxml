<textarea bindinput="remarkInput" class="remark-textarea _3fb14203 mpx-app-scope" placeholder="{{placeholder}}" value="{{remark}}"></textarea>
<view class="remark-fast _3fb14203 mpx-app-scope" wx:if="{{remarkSelect&&remarkSelect.length}}">
    <view class="remark-fast-title _3fb14203 mpx-app-scope">{{_i1}}</view>
    <view bindtap="selectRemark" class="remark-fast-item _3fb14203 mpx-app-scope" data-content="{{item}}" wx:for="{{remarkSelect}}" wx:key="index">{{item}}</view>
</view>
<view bindtap="submitRemark" class="remark-btn _3fb14203 mpx-app-scope" style="background-color: {{colorTheme||'#F2B940'}};">{{_i2}}</view>
