var __async = (e, n, o) => new Promise(((t, c) => {
    var r = e => {
        try {
          i(o.next(e))
        } catch (e) {
          c(e)
        }
      },
      a = e => {
        try {
          i(o.throw(e))
        } catch (e) {
          c(e)
        }
      },
      i = e => e.done ? t(e.value) : Promise.resolve(e.value).then(r, a);
    i((o = o.apply(e, n)).next())
  })),
  g = {};
g.c = require("../../../bundle.js"), (g.c = g.c || []).push([
  [257], {
    237: function(e, n, o) {
      o.g.currentModuleId = "_ed9ac69a", o.g.currentCtor = Page, o.g.currentCtorType = "page", o.g.currentResourceType = "page", o(240), o.g.currentSrcMode = "wx", o(238)
    },
    238: function(e, n, o) {
      "use strict";
      o.r(n);
      var t = o(194),
        c = o(35),
        r = o(121),
        a = o(239);
      (0, t.a)({
        data: {
          isLoad: !1,
          componentMounted: !1,
          pageOptions: {},
          indexRef: null
        },
        onLoad(e) {
          return __async(this, null, (function*() {
            this.isLoad = !0, this.pageOptions = e
          }))
        },
        onShow() {
          this.componentMounted && this.indexRef && this.indexRef.componentOnShow && this.indexRef.componentOnShow()
        },
        onPageScroll(e) {
          this._debounce || (this._debounce = (0, a.a)((e => {
            this.handleDebounceScroll(e)
          }), 100)), this._debounce(e)
        },
        methods: {
          asyncComponentMounted() {
            try {
              this.indexRef = this.selectComponent("#index"), this.indexRef.componentOnLoad(this.pageOptions), this.indexRef.componentOnShow(), this.componentMounted = !0
            } catch (e) {
              c.a.reportError("p1 asyncComponentMounted error", e)
            }
          },
          handleDebounceScroll(e) {
            try {
              r.a.commit("setTabBarPageOffsetTopMap", {
                "pages/page/p1/index": e.scrollTop
              })
            } catch (e) {
              console.error(e)
            }
          }
        }
      })
    },
    240: function(e, n, o) {
      o.g.currentInject = {
        moduleId: "_ed9ac69a"
      }, o.g.currentInject.render = function(e, n, o, t) {
        t("componentMounted"), t("isLoad"), o()
      }
    }
  },
  function(e) {
    var n;
    n = 237, e(e.s = n)
  }
]);