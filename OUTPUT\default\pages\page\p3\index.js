var __async = (e, n, o) => new Promise(((t, r) => {
    var c = e => {
        try {
          i(o.next(e))
        } catch (e) {
          r(e)
        }
      },
      a = e => {
        try {
          i(o.throw(e))
        } catch (e) {
          r(e)
        }
      },
      i = e => e.done ? t(e.value) : Promise.resolve(e.value).then(c, a);
    i((o = o.apply(e, n)).next())
  })),
  g = {};
g.c = require("../../../bundle.js"), (g.c = g.c || []).push([
  [259], {
    244: function(e, n, o) {
      o.g.currentModuleId = "_9a07fb96", o.g.currentCtor = Page, o.g.currentCtorType = "page", o.g.currentResourceType = "page", o(246), o.g.currentSrcMode = "wx", o(245)
    },
    245: function(e, n, o) {
      "use strict";
      o.r(n);
      var t = o(194),
        r = o(35),
        c = o(121),
        a = o(239);
      (0, t.a)({
        data: {
          isLoad: !1,
          componentMounted: !1,
          pageOptions: {},
          indexRef: null
        },
        onLoad(e) {
          return __async(this, null, (function*() {
            this.isLoad = !0, this.pageOptions = e
          }))
        },
        onShow() {
          this.componentMounted && this.indexRef && this.indexRef.componentOnShow && this.indexRef.componentOnShow()
        },
        onPageScroll(e) {
          this._debounce || (this._debounce = (0, a.a)((e => {
            this.handleDebounceScroll(e)
          }), 100)), this._debounce(e)
        },
        methods: {
          asyncComponentMounted() {
            try {
              this.indexRef = this.selectComponent("#index"), this.indexRef.componentOnLoad(this.pageOptions), this.indexRef.componentOnShow(), this.componentMounted = !0
            } catch (e) {
              r.a.reportError("p3 asyncComponentMounted error", e)
            }
          },
          handleDebounceScroll(e) {
            try {
              c.a.commit("setTabBarPageOffsetTopMap", {
                "pages/page/p3/index": e.scrollTop
              })
            } catch (e) {
              console.error(e)
            }
          }
        }
      })
    },
    246: function(e, n, o) {
      o.g.currentInject = {
        moduleId: "_9a07fb96"
      }, o.g.currentInject.render = function(e, n, o, t) {
        t("componentMounted"), t("isLoad"), o()
      }
    }
  },
  function(e) {
    var n;
    n = 244, e(e.s = n)
  }
]);