var __async = (e, n, o) => new Promise(((t, r) => {
    var c = e => {
        try {
          s(o.next(e))
        } catch (e) {
          r(e)
        }
      },
      i = e => {
        try {
          s(o.throw(e))
        } catch (e) {
          r(e)
        }
      },
      s = e => e.done ? t(e.value) : Promise.resolve(e.value).then(c, i);
    s((o = o.apply(e, n)).next())
  })),
  g = {};
g.c = require("../../../bundle.js"), (g.c = g.c || []).push([
  [261], {
    250: function(e, n, o) {
      o.g.currentModuleId = "_46753092", o.g.currentCtor = Page, o.g.currentCtorType = "page", o.g.currentResourceType = "page", o(252), o.g.currentSrcMode = "wx", o(251)
    },
    251: function(e, n, o) {
      "use strict";
      o.r(n);
      var t = o(194),
        r = o(35),
        c = o(121),
        i = o(239);
      (0, t.a)({
        data: {
          isLoad: !1,
          componentMounted: !1,
          pageOptions: {},
          indexRef: null
        },
        onLoad(e) {
          return __async(this, null, (function*() {
            this.isLoad = !0, this.pageOptions = e
          }))
        },
        onShow() {
          this.componentMounted && this.indexRef && this.indexRef.componentOnShow && this.indexRef.componentOnShow()
        },
        onPageScroll(e) {
          this._debounce || (this._debounce = (0, i.a)((e => {
            this.handleDebounceScroll(e)
          }), 100)), this._debounce(e)
        },
        methods: {
          asyncComponentMounted() {
            try {
              this.indexRef = this.selectComponent("#index"), this.indexRef.componentOnLoad(this.pageOptions), this.indexRef.componentOnShow(), this.componentMounted = !0
            } catch (e) {
              r.a.reportError("p4 asyncComponentMounted error", e)
            }
          },
          handleDebounceScroll(e) {
            try {
              c.a.commit("setTabBarPageOffsetTopMap", {
                "pages/page/p5/index": e.scrollTop
              })
            } catch (e) {
              console.error(e)
            }
          }
        }
      })
    },
    252: function(e, n, o) {
      o.g.currentInject = {
        moduleId: "_46753092"
      }, o.g.currentInject.render = function(e, n, o, t) {
        t("componentMounted"), t("isLoad"), o()
      }
    }
  },
  function(e) {
    var n;
    n = 250, e(e.s = n)
  }
]);