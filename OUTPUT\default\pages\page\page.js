var __async = (e, n, t) => new Promise(((i, o) => {
    var r = e => {
        try {
          a(t.next(e))
        } catch (e) {
          o(e)
        }
      },
      s = e => {
        try {
          a(t.throw(e))
        } catch (e) {
          o(e)
        }
      },
      a = e => e.done ? i(e.value) : Promise.resolve(e.value).then(r, s);
    a((t = t.apply(e, n)).next())
  })),
  g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [262], {
    228: function(e, n, t) {
      t.g.currentModuleId = "_a81f6284", t.g.currentCtor = Page, t.g.currentCtorType = "page", t.g.currentResourceType = "page", t(230), t.g.currentSrcMode = "wx", t(229)
    },
    229: function(e, n, t) {
      "use strict";
      t.r(n);
      var i = t(194),
        o = t(35);
      (0, i.a)({
        data: {
          pageOptions: {}
        },
        onLoad(e) {
          return __async(this, null, (function*() {
            this.pageOptions = e;
            try {
              this.indexRef = yield this.$asyncRefs.index, this.indexRef.onLoad(this.pageOptions), this.indexRef.onShow()
            } catch (e) {
              o.a.reportError("pageAsyncComponentMountedError", e)
            }
          }))
        },
        onShow() {
          this.indexRef && this.indexRef.onShow && this.indexRef.onShow()
        },
        onHide() {
          this.indexRef && this.indexRef.onHide && this.indexRef.onHide()
        },
        onUnload() {
          this.indexRef && this.indexRef.onUnload && this.indexRef.onUnload()
        },
        onShareAppMessage(e) {
          return this.indexRef && this.indexRef.onShareAppMessage && this.indexRef.onShareAppMessage(e)
        }
      })
    },
    230: function(e, n, t) {
      t.g.currentInject = {
        moduleId: "_a81f6284"
      }, t.g.currentInject.getRefsData = function() {
        return [{
          key: "index",
          selector: ".ref_index_1",
          type: "component",
          all: !1
        }]
      }
    }
  },
  function(e) {
    var n;
    n = 228, e(e.s = n)
  }
]);