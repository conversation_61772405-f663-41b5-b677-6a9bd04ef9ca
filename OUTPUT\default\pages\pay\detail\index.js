var g = {};
g.c = require("../../../bundle.js"), (g.c = g.c || []).push([
  [263], {
    268: function(t, e, o) {
      o.g.currentModuleId = "_5dbd3d0c", o.g.currentCtor = Page, o.g.currentCtorType = "page", o.g.currentResourceType = "page", o(270), o.g.currentSrcMode = "wx", o(269)
    },
    269: function(t, e, o) {
      "use strict";
      o.r(e);
      var r = o(194),
        a = o(4),
        d = o(117);
      (0, r.a)({
        data: {
          detail: {},
          card_discount: "0"
        },
        onLoad(t) {
          t.orderNo && this.getOrderPayDetail(t.orderNo)
        },
        getOrderPayDetail(t) {
          (0, d.orderPayDetail)({
            orderNo: t
          }).then((t => {
            if (t.status) {
              const {
                data: e
              } = t;
              e.actualAmount = e.actualAmount.toFixed(2), e.discountLists.length && e.discountLists.map((t => {
                t.discountAmount = t.discountAmount.toFixed(2)
              })), this.detail = e
            } else a.a.showToast({
              title: t.message
            })
          })).catch((t => {
            a.a.showToast({
              title: t.errMsg
            })
          }))
        }
      })
    },
    270: function(t, e, o) {
      o(203);
      o.g.currentInject = {
        moduleId: "_5dbd3d0c"
      }, o.g.currentInject.render = function(t, e, o, r) {
        e("detail.totalAmount"), e("detail.actualAmount"), t(e("detail.discountLists"), (function(t, e) {
          t.discountAmount
        })), e("detail.payTypeText"), e("detail.orderAt"), e("detail.orderNo"), e("detail.billNo"), o()
      }
    }
  },
  function(t) {
    var e;
    e = 268, t(t.s = e)
  }
]);