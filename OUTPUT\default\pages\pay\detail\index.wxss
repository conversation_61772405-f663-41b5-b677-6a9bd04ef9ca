.circle._5dbd3d0c {
    border-radius: 50%;
    overflow: hidden
}

.white._5dbd3d0c {
    background-color: #fff
}

.amount-view._5dbd3d0c {
    background: #fff
}

.head-temp._5dbd3d0c {
    -webkit-align-items: center;
    align-items: center;
    color: #fff;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    font-size: 14px;
    height: 315rpx;
    position: relative;
    width: 100%
}

.pay-account._5dbd3d0c {
    color: #333;
    font-size: 52rpx;
    font-weight: 700;
    height: 78rpx;
    line-height: 78rpx;
    margin-top: 32rpx
}

.partition-line._5dbd3d0c {
    border-top: 1px solid #e5e5e5;
    margin: 0 30rpx
}

.account-img._5dbd3d0c {
    background-color: #fff;
    height: 120rpx;
    margin-top: 59rpx;
    overflow: hidden;
    width: 120rpx
}

.account-img .image._5dbd3d0c {
    height: 100%;
    width: 100%
}

.detaile-info._5dbd3d0c {
    margin-top: 20rpx
}

.detaile-info._5dbd3d0c,.detaile._5dbd3d0c {
    padding: 35rpx 32rpx
}

.section._5dbd3d0c {
    -webkit-align-content: center;
    align-content: center;
    display: flex;
    display: -webkit-flex;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
    height: 70rpx;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    line-height: 70rpx
}

.section._5dbd3d0c:last-child {
    border-bottom: none
}

.section .section-title._5dbd3d0c {
    color: #666;
    font-size: 14px;
    margin-right: 10px;
    min-width: 60px
}

.section .section-box._5dbd3d0c {
    color: #333;
    -webkit-flex: 1;
    flex: 1;
    font-size: 14px;
    height: 70rpx;
    line-height: 70rpx;
    text-align: right
}

.detaile-info .section._5dbd3d0c {
    border-bottom: none
}
