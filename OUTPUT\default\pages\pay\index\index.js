var __async = (n, e, t) => new Promise(((o, r) => {
    var c = n => {
        try {
          s(t.next(n))
        } catch (n) {
          r(n)
        }
      },
      i = n => {
        try {
          s(t.throw(n))
        } catch (n) {
          r(n)
        }
      },
      s = n => n.done ? o(n.value) : Promise.resolve(n.value).then(c, i);
    s((t = t.apply(n, e)).next())
  })),
  g = {};
g.c = require("../../../bundle.js"), (g.c = g.c || []).push([
  [264], {
    262: function(n, e, t) {
      t.g.currentModuleId = "_3da7e99d", t.g.currentCtor = Page, t.g.currentCtorType = "page", t.g.currentResourceType = "page", t(264), t.g.currentSrcMode = "wx", t(263)
    },
    263: function(n, e, t) {
      "use strict";
      t.r(e);
      var o = t(194),
        r = t(35);
      (0, o.a)({
        data: {
          isLoad: !1,
          componentMounted: !1,
          pageOptions: {},
          indexRef: null
        },
        onLoad(n) {
          return __async(this, null, (function*() {
            this.isLoad = !0, this.pageOptions = n
          }))
        },
        onShow() {
          this.componentMounted && this.indexRef && this.indexRef.componentOnShow && this.indexRef.componentOnShow()
        },
        methods: {
          asyncComponentMounted() {
            try {
              this.indexRef = this.selectComponent("#index"), this.indexRef.componentOnLoad(this.pageOptions), this.indexRef.componentOnShow(), this.componentMounted = !0
            } catch (n) {
              r.a.reportError("pay asyncComponentMounted error", n)
            }
          }
        }
      })
    },
    264: function(n, e, t) {
      t.g.currentInject = {
        moduleId: "_3da7e99d"
      }, t.g.currentInject.render = function(n, e, t, o) {
        o("componentMounted"), o("isLoad"), t()
      }
    }
  },
  function(n) {
    var e;
    e = 262, n(n.s = e)
  }
]);