var g = {};
g.c = require("../../../bundle.js"), (g.c = g.c || []).push([
  [265], {
    265: function(e, r, t) {
      t.g.currentModuleId = "_5eb6950c", t.g.currentCtor = Page, t.g.currentCtorType = "page", t.g.currentResourceType = "page", t(267), t.g.currentSrcMode = "wx", t(266)
    },
    266: function(e, r, t) {
      "use strict";
      t.r(r);
      var n = t(194),
        c = t(36),
        o = t(117);
      (0, n.a)({
        data: {
          recordList: [],
          isYiHeTang: (0, c.Gb)()
        },
        onShow() {
          (0, o.orderPayList)({
            orderType: 4,
            pageNo: 1,
            pageSize: 1e3
          }).then((e => {
            this.recordList = e.data.data
          }))
        }
      })
    },
    267: function(e, r, t) {
      t(203);
      t.g.currentInject = {
        moduleId: "_5eb6950c"
      }, t.g.currentInject.render = function(e, r, t, n) {
        n("recordList").length > 0 ? e(n("recordList"), (function(e, r) {
          e.shopName || e.sellerName
        })) : n("isYiHeTang"), t()
      }
    }
  },
  function(e) {
    var r;
    r = 265, e(e.s = r)
  }
]);