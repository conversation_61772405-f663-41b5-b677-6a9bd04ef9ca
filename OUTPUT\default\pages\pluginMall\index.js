var __async = (e, n, t) => new Promise(((o, i) => {
    var r = e => {
        try {
          c(t.next(e))
        } catch (e) {
          i(e)
        }
      },
      s = e => {
        try {
          c(t.throw(e))
        } catch (e) {
          i(e)
        }
      },
      c = e => e.done ? o(e.value) : Promise.resolve(e.value).then(r, s);
    c((t = t.apply(e, n)).next())
  })),
  g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [267], {
    253: function(e, n, t) {
      t.g.currentModuleId = "_792fe709", t.g.currentCtor = Page, t.g.currentCtorType = "page", t.g.currentResourceType = "page", t(255), t.g.currentSrcMode = "wx", t(254)
    },
    254: function(e, n, t) {
      "use strict";
      t.r(n);
      var o = t(194),
        i = t(35);
      (0, o.a)({
        data: {
          isLoad: !1,
          componentMounted: !1,
          pageOptions: {},
          indexRef: null
        },
        onLoad(e) {
          return __async(this, null, (function*() {
            this.isLoad = !0, this.pageOptions = e
          }))
        },
        onShow() {
          this.componentMounted && this.indexRef && this.indexRef.componentOnShow && this.indexRef.componentOnShow()
        },
        onHide() {
          this.indexRef && this.indexRef.componentOnHide && this.indexRef.componentOnHide()
        },
        onUnload() {
          this.indexRef && this.indexRef.componentOnUnload && this.indexRef.componentOnUnload()
        },
        onShareAppMessage(e) {
          return this.indexRef && this.indexRef.componentOnShareAppMessage && this.indexRef.componentOnShareAppMessage(e)
        },
        asyncComponentMounted() {
          try {
            this.indexRef = this.selectComponent("#index"), this.indexRef.componentOnLoad(this.pageOptions), this.indexRef.componentOnShow(), this.componentMounted = !0
          } catch (e) {
            i.a.reportError("mall asyncComponentMounted error", e)
          }
        }
      })
    },
    255: function(e, n, t) {
      t.g.currentInject = {
        moduleId: "_792fe709"
      }, t.g.currentInject.render = function(e, n, t, o) {
        o("componentMounted"), o("isLoad"), t()
      }
    }
  },
  function(e) {
    var n;
    n = 253, e(e.s = n)
  }
]);