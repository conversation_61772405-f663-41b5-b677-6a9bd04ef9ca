var __async = (e, n, t) => new Promise(((o, s) => {
    var i = e => {
        try {
          c(t.next(e))
        } catch (e) {
          s(e)
        }
      },
      r = e => {
        try {
          c(t.throw(e))
        } catch (e) {
          s(e)
        }
      },
      c = e => e.done ? o(e.value) : Promise.resolve(e.value).then(i, r);
    c((t = t.apply(e, n)).next())
  })),
  g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [268], {
    222: function(e, n, t) {
      t.g.currentModuleId = "_04afd5f2", t.g.currentCtor = Page, t.g.currentCtorType = "page", t.g.currentResourceType = "page", t(224), t.g.currentSrcMode = "wx", t(223)
    },
    223: function(e, n, t) {
      "use strict";
      t.r(n);
      var o = t(194),
        s = t(35);
      var i = class {
        static successResult(e) {
          return {
            code: 0,
            success: !0,
            message: "".concat(e.name, ": success "),
            data: e.data
          }
        }
        static errorResult(e) {
          return {
            code: -1,
            success: !1,
            message: "".concat(e.name, ": fail "),
            data: e.data || null
          }
        }
      };
      (0, o.a)({
        data: {
          isLoad: !1,
          componentMounted: !1,
          pageOptions: {},
          path: ""
        },
        onLoad(e) {
          return __async(this, null, (function*() {
            this.isLoad = !0, this.pageOptions = e
          }))
        },
        onShow() {
          this.componentMounted && this.indexRef && this.indexRef.componentOnShow && this.indexRef.componentOnShow()
        },
        onHide() {
          this.indexRef && this.indexRef.componentOnHide && this.indexRef.componentOnHide()
        },
        onUnload() {
          this.indexRef && this.indexRef.componentOnUnload && this.indexRef.componentOnUnload()
        },
        onShareAppMessage(e) {
          return this.indexRef && this.indexRef.componentOnShareAppMessage && this.indexRef.componentOnShareAppMessage(e)
        },
        methods: {
          asyncComponentMounted() {
            try {
              this.indexRef = this.selectComponent("#index"), this.indexRef.componentOnLoad(this.pageOptions), this.indexRef.componentOnShow(), this.componentMounted = !0
            } catch (e) {
              s.a.reportError("takefood asyncComponentMounted error", e)
            }
          },
          openApiSwitchShop(e) {
            return __async(this, null, (function*() {
              try {
                yield this.indexRef.openApiSwitchShop(e)
              } catch (n) {
                s.a.reportError("takefood openApiSwitchShop error", n), e.onResult && e.onResult(i.errorResult({
                  name: "openApiAuthLogin",
                  data: n
                }))
              }
            }))
          }
        }
      })
    },
    224: function(e, n, t) {
      t.g.currentInject = {
        moduleId: "_04afd5f2"
      }, t.g.currentInject.render = function(e, n, t, o) {
        o("componentMounted"), o("isLoad"), t()
      }
    }
  },
  function(e) {
    var n;
    n = 222, e(e.s = n)
  }
]);