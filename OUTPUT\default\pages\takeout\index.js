var __async = (t, e, o) => new Promise(((a, n) => {
    var i = t => {
        try {
          d(o.next(t))
        } catch (t) {
          n(t)
        }
      },
      c = t => {
        try {
          d(o.throw(t))
        } catch (t) {
          n(t)
        }
      },
      d = t => t.done ? a(t.value) : Promise.resolve(t.value).then(i, c);
    d((o = o.apply(t, e)).next())
  })),
  g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [269], {
    225: function(t, e, o) {
      o.g.currentModuleId = "_1b473d4e", o.g.currentCtor = Page, o.g.currentCtorType = "page", o.g.currentResourceType = "page", o(227), o.g.currentSrcMode = "wx", o(226)
    },
    226: function(t, e, o) {
      "use strict";
      o.r(e);
      var a = o(194),
        n = o(35),
        i = o(117),
        c = o(124),
        d = o(120),
        r = o(172);
      (0, a.a)({
        onLoad(t) {
          return __async(this, null, (function*() {
            if (t.q) return void(0, i.getQrCodeInfo)(t.q).then((t => __async(this, null, (function*() {
              t.data.shop_id ? (n.a.notify("addlogswitchShop", "page-takeout1__".concat(t.data.shop_id)), yield d.store.dispatch("switchShop", t.data.shop_id, 1), (0, c.g)((0, r.a)())) : (0, c.i)((0, r.a)("multi", "mode=auto"))
            }))));
            if (t.scene) return void(0, i.getSceneMiniCodeInfo)(t.scene).then((t => __async(this, null, (function*() {
              t.data.mul_id ? (n.a.notify("addlogswitchShop", "page-takeout2__".concat(t.data.mul_id)), yield d.store.dispatch("switchShop", t.data.mul_id, 1), (0, c.g)((0, r.a)())) : (0, c.i)((0, r.a)("multi", "mode=auto"))
            }))));
            const e = t.multi_id;
            if (e) return n.a.notify("addlogswitchShop", "page-takeout3__".concat(e)), yield d.store.dispatch("switchShop", e, 1), void(0, c.g)((0, r.a)());
            (0, c.i)((0, r.a)("multi", "mode=auto"))
          }))
        }
      })
    },
    227: function(t, e, o) {
      o.g.currentInject = {
        moduleId: "_1b473d4e"
      }
    }
  },
  function(t) {
    var e;
    e = 225, t(t.s = e)
  }
]);