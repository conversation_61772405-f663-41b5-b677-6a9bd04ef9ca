var __async = (e, n, t) => new Promise(((o, i) => {
    var r = e => {
        try {
          c(t.next(e))
        } catch (e) {
          i(e)
        }
      },
      s = e => {
        try {
          c(t.throw(e))
        } catch (e) {
          i(e)
        }
      },
      c = e => e.done ? o(e.value) : Promise.resolve(e.value).then(r, s);
    c((t = t.apply(e, n)).next())
  })),
  g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [278], {
    219: function(e, n, t) {
      t.g.currentModuleId = "_1eb25fed", t.g.currentCtor = Page, t.g.currentCtorType = "page", t.g.currentResourceType = "page", t(221), t.g.currentSrcMode = "wx", t(220)
    },
    220: function(e, n, t) {
      "use strict";
      t.r(n);
      var o = t(194),
        i = t(35),
        r = t(117);
      (0, o.a)({
        data: {
          componentMounted: !1,
          pageOptions: {},
          path: ""
        },
        onLoad(e) {
          return __async(this, null, (function*() {
            this.pageOptions = e, this.memberAfterTips();
            try {
              this.indexRef = yield this.$asyncRefs.index, this.indexRef.componentOnLoad(this.pageOptions), this.indexRef.componentOnShow(), this.componentMounted = !0
            } catch (e) {
              i.a.reportError("userAsyncComponentMountedError", e)
            }
          }))
        },
        onShow() {
          this.indexRef && this.indexRef.componentOnShow && this.indexRef.componentOnShow()
        },
        onHide() {
          this.indexRef && this.indexRef.componentOnHide && this.indexRef.componentOnHide()
        },
        onUnload() {
          this.indexRef && this.indexRef.componentOnUnload && this.indexRef.componentOnUnload()
        },
        onShareAppMessage(e) {
          return this.indexRef && this.indexRef.componentOnShareAppMessage && this.indexRef.componentOnShareAppMessage(e)
        },
        methods: {
          memberAfterTips() {
            (0, r.customerMemberDisablePower)().then((e => {
              if (e.status && e.data) {
                const {
                  riskLevel: n,
                  isForbidBehavior: t,
                  afterTips: o,
                  isPublish: i
                } = e.data;
                n && 1 !== t && 1 === i && this.selectComponent("#showToast").showToast(o)
              }
            }))
          }
        }
      })
    },
    221: function(e, n, t) {
      t.g.currentInject = {
        moduleId: "_1eb25fed"
      }, t.g.currentInject.render = function(e, n, t, o) {
        o("componentMounted"), t()
      }, t.g.currentInject.getRefsData = function() {
        return [{
          key: "index",
          selector: ".ref_index_1",
          type: "component",
          all: !1
        }]
      }
    }
  },
  function(e) {
    var n;
    n = 219, e(e.s = n)
  }
]);