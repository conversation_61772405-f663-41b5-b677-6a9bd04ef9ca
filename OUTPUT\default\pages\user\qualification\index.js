var __async = (e, t, r) => new Promise(((c, n) => {
    var i = e => {
        try {
          u(r.next(e))
        } catch (e) {
          n(e)
        }
      },
      a = e => {
        try {
          u(r.throw(e))
        } catch (e) {
          n(e)
        }
      },
      u = e => e.done ? c(e.value) : Promise.resolve(e.value).then(i, a);
    u((r = r.apply(e, t)).next())
  })),
  g = {};
g.c = require("../../../bundle.js"), (g.c = g.c || []).push([
  [280], {
    259: function(e, t, r) {
      r.g.currentModuleId = "_ccddbe66", r.g.currentCtor = Page, r.g.currentCtorType = "page", r.g.currentResourceType = "page", r(261), r.g.currentSrcMode = "wx", r(260)
    },
    260: function(e, t, r) {
      "use strict";
      r.r(t);
      var c = r(194),
        n = r(4),
        i = r(117);
      r(119);
      (0, c.a)({
        data: {
          list: []
        },
        onLoad() {
          this.getQualify()
        },
        getFetchApi() {
          return i.getServiceQualify
        },
        getQualify() {
          return __async(this, null, (function*() {
            try {
              const e = this.getFetchApi(),
                {
                  data: t
                } = yield e();
              this.list = t.qualifications
            } catch (e) {
              console.log(e), n.a.showToast({
                title: "获取服务资质失败"
              })
            }
          }))
        },
        previewImg(e) {
          const {
            list: t
          } = this, {
            index: r
          } = e.currentTarget.dataset;
          n.a.previewImage({
            urls: t,
            current: t[r]
          })
        }
      })
    },
    261: function(e, t, r) {
      r(203);
      r.g.currentInject = {
        moduleId: "_ccddbe66"
      }, r.g.currentInject.render = function(e, t, r, c) {
        e(c("list"), (function(e, t) {})), r()
      }
    }
  },
  function(e) {
    var t;
    t = 259, e(e.s = t)
  }
]);