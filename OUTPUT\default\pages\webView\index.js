var g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [281], {
    256: function(e, n, o) {
      o.g.currentModuleId = "_9b24fcea", o.g.currentCtor = Page, o.g.currentCtorType = "page", o.g.currentResourceType = "page", o(258), o.g.currentSrcMode = "wx", o(257)
    },
    257: function(e, n, o) {
      "use strict";
      o.r(n);
      var t = o(194),
        r = o(35),
        c = o(115),
        a = o(117),
        i = o(124),
        s = o(133);
      (0, t.a)({
        data: {
          webViewSrc: "",
          beforePage: null
        },
        onLoad(e) {
          const {
            path: n
          } = e;
          if (n) this.webViewSrc = decodeURIComponent(n);
          else {
            try {
              const e = getCurrentPages();
              r.a.reportInfo("进入bot滑块验证页面，上一个页面为: ", JSON.stringify(e.map((e => e.route))))
            } catch (e) {
              console.log(e)
            }
            c.a.captcha.PageOptions.onLoad.bind(this)(e)
          }
        },
        methods: {
          onMessage: c.a.captcha.buildOnMessage({
            callback(e) {
              r.a.reportInfo("bot滑块验证通过")
            },
            bindThis: !0,
            trigerType: "after"
          }),
          handleRefresh() {
            const e = this.beforePage;
            if (!e) return (0, i.g)("/pages/index/index");
            (0, i.d)(e.route) ? "pages/index/index" === e.route ? (console.log("触发重新登录"), (0, a.fetchLogin)({
              scene: "init",
              forceLogin: !0,
              source: "botCaptcha"
            }), (0, s.b)({
              init: !0
            }).then((() => {
              (0, i.g)("/pages/index/index")
            }))) : (0, i.g)("/".concat(e.route, "?").concat(e.sensors_mp_url_query || "")): e.onLoad(e.options)
          }
        },
        onUnload() {
          c.a.captcha.PageOptions.onUnload.bind(this)()
        }
      })
    },
    258: function(e, n, o) {
      o.g.currentInject = {
        moduleId: "_9b24fcea"
      }, o.g.currentInject.render = function(e, n, o, t) {
        t("webViewSrc"), o()
      }
    }
  },
  function(e) {
    var n;
    n = 256, e(e.s = n)
  }
]);