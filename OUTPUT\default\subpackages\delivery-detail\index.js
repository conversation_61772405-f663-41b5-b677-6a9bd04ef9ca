var g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [814], {
    216: function(e, t, i) {
      i.g.currentModuleId = "_7ef18c65", i.g.currentCtor = Page, i.g.currentCtorType = "page", i.g.currentResourceType = "page", i(218), i.g.currentSrcMode = "wx", i(217)
    },
    217: function(e, t, i) {
      "use strict";
      i.r(t);
      var a = i(194),
        r = i(117),
        o = i(120),
        n = i(122);
      (0, a.a)({
        data: {
          markers: [],
          isShowMap: !1,
          horseManLocationSetInterval: null,
          deliveryDetail: null,
          marketTitle: "",
          marketCharacter: "",
          marketDistance: "",
          longitude: "",
          latitude: "",
          controls: [{
            id: 1,
            iconPath: "https://images.qmai.cn/s16/images/2019/06/04/e77c12f47a792b77.png",
            position: {
              left: 10,
              top: 270,
              width: 20,
              height: 20
            },
            clickable: !0
          }]
        },
        computed: {
          colorTheme() {
            return o.store.getters.colorTheme
          }
        },
        onLoad() {
          getApp().$once("deliveryDetail", (e => {
            this.deliveryDetail = e, this.getSpiltDeliveryDetail()
          }))
        },
        onHide() {
          this.cleanHorseManLocationSetInterval()
        },
        onUnload() {
          this.cleanHorseManLocationSetInterval()
        },
        getSpiltDeliveryDetail() {
          this.getHorseManLocation(), this.horseManLocationSetInterval = setInterval((() => {
            this.getHorseManLocation()
          }), 5e3)
        },
        getHorseManLocation() {
          const {
            orderNo: e,
            shopInfo: t,
            addr: i
          } = this.deliveryDetail;
          (0, r.getDeliveryInfo)({
            orderNo: e
          }).then((e => {
            if (!e.status) return this.isShowMap = !1, void this.cleanHorseManLocationSetInterval();
            const a = e.data;
            if (!a.deliveryPlatformInfo) return this.isShowMap = !1, void this.cleanHorseManLocationSetInterval();
            if (8 == a.deliveryPlatformInfo.deliveryStatus) return void this.cleanHorseManLocationSetInterval();
            if (!a.deliveryPlatformInfo.latitude || !a.deliveryPlatformInfo.longitude) return this.isShowMap = !1, void this.cleanHorseManLocationSetInterval();
            let r = "距离您";
            const o = (0, n.U)(a.deliveryPlatformInfo.distance),
              {
                deliveryStatus: l
              } = a.deliveryPlatformInfo;
            l < 4 ? r = "距离商家" : 4 == l && (r = "");
            const s = [{
              iconPath: "https://images.qmai.cn/s16/images/2020/09/14/55c89d7df6fef67b.png",
              id: 1,
              latitude: Number(Number(t.lat).toFixed(6)),
              longitude: Number(Number(t.lng).toFixed(6)),
              width: 40,
              height: 40,
              title: "商家"
            }, {
              iconPath: "https://images.qmai.cn/s16/images/2020/09/14/a0c30c62926c377c.png",
              id: 2,
              latitude: Number(Number(i.lat).toFixed(6)),
              longitude: Number(Number(i.lng).toFixed(6)),
              width: 40,
              height: 40,
              title: "收货人"
            }, {
              iconPath: "https://images.qmai.cn/s16/images/2020/09/14/2ea5b1cdf5a18033.png",
              id: 0,
              latitude: Number(Number(a.deliveryPlatformInfo.latitude).toFixed(6)),
              longitude: Number(Number(a.deliveryPlatformInfo.longitude).toFixed(6)),
              width: 40,
              height: 40,
              title: "骑手",
              customCallout: {
                display: "ALWAYS",
                anchorY: -3
              }
            }];
            this.latitude = Number(Number(a.deliveryPlatformInfo.latitude).toFixed(6)), this.longitude = Number(Number(a.deliveryPlatformInfo.longitude).toFixed(6)), this.markers = s, this.marketTitle = a.deliveryPlatformInfo.deliveryStatusText, this.marketCharacter = r, this.marketDistance = o, this.isShowMap = !0
          })).catch((() => {
            this.cleanHorseManLocationSetInterval()
          }))
        },
        cleanHorseManLocationSetInterval() {
          clearInterval(this.horseManLocationSetInterval), this.horseManLocationSetInterval = null
        }
      })
    },
    218: function(e, t, i) {
      i.g.currentInject = {
        moduleId: "_7ef18c65"
      }, i.g.currentInject.render = function(e, t, i, a) {
        a("longitude"), a("latitude"), a("controls"), a("markers"), a("marketTitle"), a("marketCharacter") && (a("colorTheme"), a("marketDistance")), i()
      }
    }
  },
  function(e) {
    var t;
    t = 216, e(e.s = t)
  }
]);