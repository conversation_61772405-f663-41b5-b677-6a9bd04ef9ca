<view>
    <map showLocation controls="{{controls}}" id="map" latitude="{{latitude}}" longitude="{{longitude}}" markers="{{markers}}" scale="15" style="width: 100%; height: 100vh">
        <cover-view slot="callout">
            <cover-view class="flex flex-col items-center" markerId="0">
                <cover-view class="p-20rpx bg-_bl__h_fff_br_ border-rd-10rpx shadow-_bl_0_5rpx_25rpx_0_rgba_pl_202_2c_202_2c_202_2c_0_d_25_pr__br_">
                    <cover-view class="text-_bl__h_333_br_ font-bold text-30rpx">{{marketTitle}}</cover-view>
                    <cover-view class="flex items-center mt-10rpx text-26rpx" wx:if="{{marketCharacter}}">{{marketCharacter}}<cover-view class="ml-5rpx" style="color: {{colorTheme}}">{{marketDistance}}</cover-view>
                    </cover-view>
                </cover-view>
            </cover-view>
        </cover-view>
    </map>
</view>
