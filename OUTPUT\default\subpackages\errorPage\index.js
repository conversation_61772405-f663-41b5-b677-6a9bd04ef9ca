var g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [825], {
    210: function(e, r, t) {
      t.g.currentModuleId = "_11929d5c", t.g.currentCtor = Page, t.g.currentCtorType = "page", t.g.currentResourceType = "page", t(212), t.g.currentSrcMode = "wx", t(211)
    },
    211: function(e, r, t) {
      "use strict";
      t.r(r);
      var o = t(194),
        n = t(4),
        i = t(117),
        s = t(124),
        a = t(133),
        c = t(122);
      (0, o.a)({
        data: {
          loadingUrl: "",
          error1: "https://images.qmai.cn/resource/20210824210816/2023/10/16/yongdu.png",
          errorStyle1: "width: 300rpx; height: 300rpx",
          error2: "https://images.qmai.cn/resource/20210824210816/2023/10/16/xiaohuojian.gif",
          errorStyle2: "width: 200rpx; height: 200rpx",
          tryLoad: !1,
          reason: "",
          reasonText: "",
          initCommonInfo: !1,
          loadErrorInfo: {},
          loadStatusErrorDesc: "",
          isLimited: !1,
          refresh: !0
        },
        computed: {
          status() {
            return this.tryLoad ? "retry" : "error"
          }
        },
        onLoad(e) {
          this.reason = e.reason, this.reasonText = e.reasonText || "", this.refresh = !e.noRefresh, this.initCommonInfo = !!e.initCommonInfo, this.isLimited = !!e.isLimited, n.a.hideHomeButton && n.a.hideHomeButton(), this.init()
        },
        methods: {
          init() {
            this.loadStatusErrorDesc = "errorPage: ".concat(this.reason), this.loadErrorInfo = {
              limited: this.isLimited
            }
          },
          tapRetryBtn() {
            this.tryLoad = !0;
            const e = (0, c.v)(3e3, 5e3, 0);
            setTimeout((() => {
              "loginFailed" === this.reason ? (0, i.fetchLogin)({
                scene: "init",
                forceLogin: !0,
                source: "errorPage"
              }).then((() => {
                this.reloadPage()
              })) : "getCommonInfoFailed" === this.reason && (0, a.b)({
                init: this.initCommonInfo
              }).then((() => {
                this.reloadPage()
              }))
            }), e - 3e3), setTimeout((() => {
              this.tryLoad = !1
            }), e)
          },
          reloadPage() {
            const e = getCurrentPages(),
              r = e[e.length - 1];
            r.currentPath ? (0, s.i)("/".concat(r.currentPath, "?").concat(r.sensors_mp_url_query || "")) : (0, s.g)("/pages/index/index")
          }
        }
      })
    },
    212: function(e, r, t) {
      t(203);
      t.g.currentInject = {
        moduleId: "_11929d5c"
      }, t.g.currentInject.render = function(e, r, t, o) {
        "appIdNotExist" === o("reason") || (o("status"), o("loadErrorInfo"), o("loadStatusErrorDesc"), o("refresh"), o("reasonText")), t()
      }
    }
  },
  function(e) {
    var r;
    r = 210, e(e.s = r)
  }
]);