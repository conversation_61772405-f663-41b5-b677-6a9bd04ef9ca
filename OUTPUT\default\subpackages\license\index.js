var g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [916], {
    200: function(e, r, t) {
      t.g.currentModuleId = "_68c946c8", t.g.currentCtor = Page, t.g.currentCtorType = "page", t.g.currentResourceType = "page", t(202), t.g.currentSrcMode = "wx", t(201)
    },
    201: function(e, r, t) {
      "use strict";
      t.r(r);
      var c = t(194),
        n = t(4),
        s = t(120);
      (0, c.a)({
        data: {
          license: []
        },
        computed: {
          shop() {
            return s.shopStore.state
          }
        },
        onLoad() {
          let e = [];
          this.shop.current && this.shop.current.foodLicenseList && this.shop.current.foodLicenseList.length && (e = this.shop.current.foodLicenseList), this.license = e
        },
        previewImage(e) {
          const {
            src: r
          } = e.currentTarget.dataset;
          n.a.previewImage({
            urls: this.license,
            current: r
          })
        }
      })
    },
    202: function(e, r, t) {
      t(203);
      t.g.currentInject = {
        moduleId: "_68c946c8"
      }, t.g.currentInject.render = function(e, r, t, c) {
        c("license") && c("license").length && e(c("license"), (function(e, r) {})), t()
      }
    }
  },
  function(e) {
    var r;
    r = 200, e(e.s = r)
  }
]);