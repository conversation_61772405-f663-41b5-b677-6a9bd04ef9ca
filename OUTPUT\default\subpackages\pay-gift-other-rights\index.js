var __async = (e, r, t) => new Promise(((o, n) => {
    var c = e => {
        try {
          s(t.next(e))
        } catch (e) {
          n(e)
        }
      },
      a = e => {
        try {
          s(t.throw(e))
        } catch (e) {
          n(e)
        }
      },
      s = e => e.done ? o(e.value) : Promise.resolve(e.value).then(c, a);
    s((t = t.apply(e, r)).next())
  })),
  g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [942], {
    213: function(e, r, t) {
      t.g.currentModuleId = "_2fc93fa7", t.g.currentCtor = Page, t.g.currentCtorType = "page", t.g.currentResourceType = "page", t(215), t.g.currentSrcMode = "wx", t(214)
    },
    214: function(e, r, t) {
      "use strict";
      t.r(r);
      var o = t(194),
        n = t(4),
        c = t(117),
        a = t(124),
        s = t(120),
        i = t(122);
      (0, o.a)({
        data: {
          qq: "",
          orderNo: "",
          disabled: !1
        },
        computed: {
          colorTheme() {
            return s.store.getters.colorTheme
          }
        },
        onLoad(e) {
          e.orderNo && (this.orderNo = e.orderNo)
        },
        qqChange(e) {
          this.qq = e.detail.value.trim()
        },
        exchange() {
          const {
            qq: e
          } = this;
          if (!e) return void n.a.showToast({
            title: "请输入QQ号码"
          });
          n.a.showModal({
            content: "请确认当前QQ号是否正确？",
            confirmText: "确定",
            cancelText: "取消",
            confirmColor: this.colorTheme
          }).then((() => {
            this.handleExchange()
          }))
        },
        handleExchange() {
          return __async(this, null, (function*() {
            const {
              qq: e,
              orderNo: r
            } = this;
            this.disabled = !0;
            try {
              yield(0, c.sendRightsInterests)({
                account: e,
                orderNo: r
              }), n.a.showToast({
                title: "兑换成功"
              }), yield(0, i.j)(1500), (0, a.e)()
            } catch (e) {
              e.errMsg && n.a.showToast({
                title: e.errMsg
              })
            }
            this.disabled = !1
          }))
        }
      })
    },
    215: function(e, r, t) {
      t.g.currentInject = {
        moduleId: "_2fc93fa7"
      }, t.g.currentInject.render = function(e, r, t, o) {
        o("qq"), o("disabled"), o("colorTheme"), t()
      }
    }
  },
  function(e) {
    var r;
    r = 213, e(e.s = r)
  }
]);