var __async = (e, n, t) => new Promise(((o, i) => {
    var r = e => {
        try {
          s(t.next(e))
        } catch (e) {
          i(e)
        }
      },
      c = e => {
        try {
          s(t.throw(e))
        } catch (e) {
          i(e)
        }
      },
      s = e => e.done ? o(e.value) : Promise.resolve(e.value).then(r, c);
    s((t = t.apply(e, n)).next())
  })),
  g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [943], {
    197: function(e, n, t) {
      t.g.currentModuleId = "_50c046ed", t.g.currentCtor = Page, t.g.currentCtorType = "page", t.g.currentResourceType = "page", t(199), t.g.currentSrcMode = "wx", t(198)
    },
    198: function(e, n, t) {
      "use strict";
      t.r(n);
      var o = t(194),
        i = t(35);
      (0, o.a)({
        data: {
          isLoad: !1,
          componentMounted: !1,
          pageOptions: {},
          indexRef: null
        },
        onLoad(e) {
          return __async(this, null, (function*() {
            this.isLoad = !0, this.pageOptions = e
          }))
        },
        onShow() {
          this.componentMounted && this.indexRef && this.indexRef.componentOnShow && this.indexRef.componentOnShow()
        },
        onHide() {
          this.indexRef && this.indexRef.componentOnHide && this.indexRef.componentOnHide()
        },
        onUnload() {
          this.indexRef && this.indexRef.componentOnUnload && this.indexRef.componentOnUnload()
        },
        onPageScroll(e) {
          this.indexRef && this.indexRef.componentOnPageScroll(e)
        },
        onShareAppMessage(e) {
          return this.indexRef && this.indexRef.componentOnShareAppMessage && this.indexRef.componentOnShareAppMessage(e)
        },
        onUserCaptureScreen(e) {
          return this.indexRef && this.indexRef.componentOnUserCaptureScreen && this.indexRef.componentOnUserCaptureScreen(e)
        },
        methods: {
          asyncComponentMounted() {
            try {
              this.indexRef = this.selectComponent("#index"), this.indexRef.componentOnLoad(this.pageOptions), this.indexRef.componentOnShow(), this.componentMounted = !0
            } catch (e) {
              i.a.reportError("payment-code asyncComponentMounted error", e)
            }
          }
        }
      })
    },
    199: function(e, n, t) {
      t.g.currentInject = {
        moduleId: "_50c046ed"
      }, t.g.currentInject.render = function(e, n, t, o) {
        o("componentMounted"), o("isLoad"), t()
      }
    }
  },
  function(e) {
    var n;
    n = 197, e(e.s = n)
  }
]);