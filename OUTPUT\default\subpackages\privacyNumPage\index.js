var g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [967], {
    207: function(r, e, c) {
      c.g.currentModuleId = "_a20635b4", c.g.currentCtor = Page, c.g.currentCtorType = "page", c.g.currentResourceType = "page", c(209), c.g.currentSrcMode = "wx", c(208)
    },
    208: function(r, e, c) {
      "use strict";
      c.r(e), (0, c(194).a)({})
    },
    209: function(r, e, c) {
      c.g.currentInject = {
        moduleId: "_a20635b4"
      }
    }
  },
  function(r) {
    var e;
    e = 207, r(r.s = e)
  }
]);