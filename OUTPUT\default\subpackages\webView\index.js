var __async = (e, t, i) => new Promise(((a, r) => {
    var n = e => {
        try {
          s(i.next(e))
        } catch (e) {
          r(e)
        }
      },
      c = e => {
        try {
          s(i.throw(e))
        } catch (e) {
          r(e)
        }
      },
      s = e => e.done ? a(e.value) : Promise.resolve(e.value).then(n, c);
    s((i = i.apply(e, t)).next())
  })),
  g = {};
g.c = require("../../bundle.js"), (g.c = g.c || []).push([
  [1189], {
    204: function(e, t, i) {
      i.g.currentModuleId = "_ead6cb18", i.g.currentCtor = Page, i.g.currentCtorType = "page", i.g.currentResourceType = "page", i(206), i.g.currentSrcMode = "wx", i(205)
    },
    205: function(e, t, i) {
      "use strict";
      i.r(t);
      var a = i(194),
        r = i(4),
        n = i(35),
        c = i(36),
        s = i(117),
        o = i(120);
      const h = new(i(126).a);
      (0, a.a)({
        data: {
          webViewSrc: "",
          activityId: "",
          activityName: "",
          gamesSharePic: "",
          isDuiBa: "",
          ShareInfo: null
        },
        computed: {
          mobile() {
            return o.userInfoStore.getters.mobile
          }
        },
        onLoad(e) {
          return __async(this, null, (function*() {
            (0, c.V)() && n.a.reportError("webview", e);
            let {
              path: t = "",
              activityId: i,
              activityName: a,
              gamesSharePic: s,
              isDuiBa: o,
              isDuiBaLink: l,
              alReadyDuiba: d
            } = e;
            if (t = decodeURIComponent(t), d) try {
              const e = yield this.handleDuibaUrl(t);
              if (e.status && e.data) return this.webViewSrc = e.data, void(this.isDuiBa = "1")
            } catch (e) {
              r.a.showToast({
                title: e.errMsg
              }), n.a.reportError("兑吧底部导航加载失败", {
                error: e
              })
            }
            if (l && (t = h.get("duibaUrl")), (0, c.ib)()) {
              const {
                mobile: e
              } = this;
              e && (t = t.includes("?") ? "".concat(t, "&phone=").concat(e) : "".concat(t, "?phone=").concat(e))
            }
            this.webViewSrc = t, this.activityId = i, this.activityName = a, this.gamesSharePic = s, this.isDuiBa = o || ""
          }))
        },
        onShareAppMessage() {
          {
            if (this.ShareInfo) {
              if (this.isDuiBa && this.ShareInfo.link) return {
                title: this.ShareInfo.title,
                imageUrl: this.ShareInfo.imgUrl,
                desc: this.ShareInfo.desc,
                path: "/pages/index/index?isduiba=1&duibaPath=".concat(encodeURIComponent(this.ShareInfo.link))
              };
              return {
                title: this.ShareInfo.title,
                desc: this.ShareInfo.desc,
                imageUrl: this.ShareInfo.imgUrl,
                path: encodeURIComponent(this.ShareInfo.link)
              }
            }
            const {
              activityId: e,
              activityName: t,
              gamesSharePic: i
            } = this, a = i || "https://images.qmai.cn/resource/20210824210816/2022/01/27/图片.jpg?x-oss-process=image";
            (0, s.memberShare)({
              activityId: e
            });
            return {
              title: t ? "邀你参加【".concat(t, "】") : "点击👇 获取更多福利",
              imageUrl: a,
              path: "/pages/index/index?gameId=".concat(this.activityId, "&isFromGame=1&gameShareName=").concat(t, "&gameShareImage=").concat(a)
            }
          }
        },
        eventHandler(e) {
          {
            (0, c.V)() && n.a.reportError("webview eventHandler", e.detail);
            const t = e.detail.data;
            this.ShareInfo = {
              title: "",
              link: "",
              desc: "",
              imgUrl: ""
            }, this.ShareInfo.title = t[t.length - 1].title, this.ShareInfo.desc = t[t.length - 1].desc, this.ShareInfo.link = t[t.length - 1].link, this.ShareInfo.imgUrl = t[t.length - 1].imgUrl
          }
        },
        methods: {
          handleDuibaUrl(e) {
            return __async(this, null, (function*() {
              return (0, s.getDuibaUrl)({
                redirectUrl: e
              })
            }))
          }
        }
      })
    },
    206: function(e, t, i) {
      i.g.currentInject = {
        moduleId: "_ead6cb18"
      }, i.g.currentInject.render = function(e, t, i, a) {
        a("webViewSrc"), i()
      }
    }
  },
  function(e) {
    var t;
    t = 204, e(e.s = t)
  }
]);