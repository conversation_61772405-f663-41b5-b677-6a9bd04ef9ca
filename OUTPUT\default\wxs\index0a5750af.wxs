module.exports = ((function() {
  var __webpack_modules__ = ([((function(module, __unused_webpack_exports, __webpack_require__) {
    var style = (__webpack_require__(1).style);

    function rootStyle(data) {
      if (!data.color) {
        return (data.customStyle)
      };
      var properties = ({
        color: data.plain ? data.color : "#fff",
        background: data.plain ? null : data.color,
      });
      if (data.color.indexOf("gradient") !== -1) {
        properties.border = 0
      } else {
        properties[("" + "border-color")] = data.color
      };
      return (style([properties, data.customStyle]))
    };

    function loadingColor(data) {
      if (data.plain) {
        return (data.color ? data.color : "#c9c9c9")
      };
      if (data.type === "default") {
        return ("#c9c9c9")
      };
      return ("#fff")
    };
    module.exports = ({
      rootStyle: rootStyle,
      loadingColor: loadingColor,
    })
  })), ((function(module) {
    var kebabCase = (function(word) {
      return (word.replace(getRegExp("[A-Z]", "g"), (function(i) {
        return ("-".concat(i))
      })).toLowerCase())
    });
    var isArray = (function(array) {
      return (array && array.constructor === "Array")
    });
    var REGEXP = getRegExp('{|}|\x22', "g");

    function keys(obj) {
      return (JSON.stringify(obj).replace(REGEXP, "").split(",").map((function(item) {
        return (item.split(":")[(0)])
      })))
    };
    var style = (function(styles) {
      if (isArray(styles)) {
        return (styles.filter((function(item) {
          return (item != null && item !== "")
        })).map((function(item) {
          return (style(item))
        })).join(";"))
      };
      if (styles.constructor === "Object") {
        return (keys(styles).filter((function(key) {
          return (styles[((nt_2 = (key), null == nt_2 ? undefined : 'number' === typeof nt_2 ? nt_2 : "" + nt_2))] != null && styles[((nt_3 = (key), null == nt_3 ? undefined : 'number' === typeof nt_3 ? nt_3 : "" + nt_3))] !== "")
        })).map((function(key) {
          return ([kebabCase(key), [styles[((nt_4 = (key), null == nt_4 ? undefined : 'number' === typeof nt_4 ? nt_4 : "" + nt_4))]]].join(":"))
        })).join(";"))
      };
      return (styles)
    });
    var formatSales = (function(value) {
      return (value > 100 ? "".concat(Math.floor(value / 100), "00+") : "".concat(value))
    });

    function addUnit(value) {
      if (value == null) return (undefined);;
      return (typeof value === "number" ? "".concat(value, "px") : value)
    };
    var hexToRgba = (function(hex, opacity) {
      if (!hex) return ("");;
      return ("rgba(".concat(parseInt("0x".concat(hex.slice(1, 3)), 16), ",").concat(parseInt("0x".concat(hex.slice(3, 5)), 16), ",").concat(parseInt("0x".concat(hex.slice(5, 7)), 16), ",").concat(opacity, ")"))
    });
    module.exports = ({
      isArray: isArray,
      keys: keys,
      style: style,
      formatSales: formatSales,
      addUnit: addUnit,
      kebabCase: kebabCase,
      hexToRgba: hexToRgba,
    })
  }))]);
  var __webpack_module_cache__ = ({});

  function __webpack_require__(moduleId) {
    var cachedModule = __webpack_module_cache__[((nt_5 = (moduleId), null == nt_5 ? undefined : 'number' === typeof nt_5 ? nt_5 : "" + nt_5))];
    if (cachedModule !== undefined) {
      return (cachedModule.exports)
    };
    var module = __webpack_module_cache__[((nt_6 = (moduleId), null == nt_6 ? undefined : 'number' === typeof nt_6 ? nt_6 : "" + nt_6))] = ({
      exports: ({}),
    });
    __webpack_modules__[((nt_7 = (moduleId), null == nt_7 ? undefined : 'number' === typeof nt_7 ? nt_7 : "" + nt_7))](module, module.exports, __webpack_require__);
    return (module.exports)
  };
  var __webpack_exports__ = __webpack_require__(0);
  return (__webpack_exports__ && __webpack_exports__.__esModule ? __webpack_exports__[("" + "default")] : __webpack_exports__)
}))();