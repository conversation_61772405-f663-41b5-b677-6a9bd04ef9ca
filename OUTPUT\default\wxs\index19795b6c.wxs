module.exports = ((function() {
  var __webpack_modules__ = ([((function(module) {
    function imageStyle(data) {
      var width = data.width + "";
      var height = data.height + "";
      var _style = ({});
      if (width) {
        if (width.indexOf("%") === -1 && width.indexOf("px") === -1) {
          var _width = parseInt(width);
          if (!isNaN(_width)) width += "rpx";
        };
        _style.width = width
      };
      if (height) {
        if (height.indexOf("%") === -1 && height.indexOf("px") === -1) {
          var _height = parseInt(height);
          if (!isNaN(_height)) height += "rpx";
        };
        _style.height = height
      };
      return (_style)
    };
    module.exports = ({
      imageStyle: imageStyle,
    })
  }))]);
  var __webpack_module_cache__ = ({});

  function __webpack_require__(moduleId) {
    var cachedModule = __webpack_module_cache__[((nt_0 = (moduleId), null == nt_0 ? undefined : 'number' === typeof nt_0 ? nt_0 : "" + nt_0))];
    if (cachedModule !== undefined) {
      return (cachedModule.exports)
    };
    var module = __webpack_module_cache__[((nt_1 = (moduleId), null == nt_1 ? undefined : 'number' === typeof nt_1 ? nt_1 : "" + nt_1))] = ({
      exports: ({}),
    });
    __webpack_modules__[((nt_2 = (moduleId), null == nt_2 ? undefined : 'number' === typeof nt_2 ? nt_2 : "" + nt_2))](module, module.exports, __webpack_require__);
    return (module.exports)
  };
  var __webpack_exports__ = __webpack_require__(0);
  return (__webpack_exports__ && __webpack_exports__.__esModule ? __webpack_exports__[("" + "default")] : __webpack_exports__)
}))();