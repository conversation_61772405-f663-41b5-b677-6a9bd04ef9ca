module.exports = ((function() {
  var __webpack_modules__ = ([((function(module, __unused_webpack_exports, __webpack_require__) {
    var style = (__webpack_require__(1).style);

    function popupStyle(data) {
      return (style([({
        "z-index": data.zIndex,
        "-webkit-transition-duration": "".concat(data.currentDuration, "ms"),
        "transition-duration": "".concat(data.currentDuration, "ms"),
      }), data.display ? null : "display: none", data.customStyle]))
    };
    module.exports = ({
      popupStyle: popupStyle,
    })
  })), ((function(module) {
    var kebabCase = (function(word) {
      return (word.replace(getRegExp("[A-Z]", "g"), (function(i) {
        return ("-".concat(i))
      })).toLowerCase())
    });
    var isArray = (function(array) {
      return (array && array.constructor === "Array")
    });
    var REGEXP = getRegExp('{|}|\x22', "g");

    function keys(obj) {
      return (JSON.stringify(obj).replace(REGEXP, "").split(",").map((function(item) {
        return (item.split(":")[(0)])
      })))
    };
    var style = (function(styles) {
      if (isArray(styles)) {
        return (styles.filter((function(item) {
          return (item != null && item !== "")
        })).map((function(item) {
          return (style(item))
        })).join(";"))
      };
      if (styles.constructor === "Object") {
        return (keys(styles).filter((function(key) {
          return (styles[((nt_1 = (key), null == nt_1 ? undefined : 'number' === typeof nt_1 ? nt_1 : "" + nt_1))] != null && styles[((nt_2 = (key), null == nt_2 ? undefined : 'number' === typeof nt_2 ? nt_2 : "" + nt_2))] !== "")
        })).map((function(key) {
          return ([kebabCase(key), [styles[((nt_3 = (key), null == nt_3 ? undefined : 'number' === typeof nt_3 ? nt_3 : "" + nt_3))]]].join(":"))
        })).join(";"))
      };
      return (styles)
    });
    var formatSales = (function(value) {
      return (value > 100 ? "".concat(Math.floor(value / 100), "00+") : "".concat(value))
    });

    function addUnit(value) {
      if (value == null) return (undefined);;
      return (typeof value === "number" ? "".concat(value, "px") : value)
    };
    var hexToRgba = (function(hex, opacity) {
      if (!hex) return ("");;
      return ("rgba(".concat(parseInt("0x".concat(hex.slice(1, 3)), 16), ",").concat(parseInt("0x".concat(hex.slice(3, 5)), 16), ",").concat(parseInt("0x".concat(hex.slice(5, 7)), 16), ",").concat(opacity, ")"))
    });
    module.exports = ({
      isArray: isArray,
      keys: keys,
      style: style,
      formatSales: formatSales,
      addUnit: addUnit,
      kebabCase: kebabCase,
      hexToRgba: hexToRgba,
    })
  }))]);
  var __webpack_module_cache__ = ({});

  function __webpack_require__(moduleId) {
    var cachedModule = __webpack_module_cache__[((nt_4 = (moduleId), null == nt_4 ? undefined : 'number' === typeof nt_4 ? nt_4 : "" + nt_4))];
    if (cachedModule !== undefined) {
      return (cachedModule.exports)
    };
    var module = __webpack_module_cache__[((nt_5 = (moduleId), null == nt_5 ? undefined : 'number' === typeof nt_5 ? nt_5 : "" + nt_5))] = ({
      exports: ({}),
    });
    __webpack_modules__[((nt_6 = (moduleId), null == nt_6 ? undefined : 'number' === typeof nt_6 ? nt_6 : "" + nt_6))](module, module.exports, __webpack_require__);
    return (module.exports)
  };
  var __webpack_exports__ = __webpack_require__(0);
  return (__webpack_exports__ && __webpack_exports__.__esModule ? __webpack_exports__[("" + "default")] : __webpack_exports__)
}))();