module.exports = ((function() {
  var __webpack_modules__ = ([((function(module) {
    function s(mpxExt, url, width) {
      mpxExt = mpxExt || ({});
      var imageFormat = mpxExt.imageFormat;
      var pixelRatio = mpxExt.pixelRatio;
      var screenWidth = mpxExt.screenWidth;
      var maxWidth = parseInt(screenWidth * pixelRatio, 10);
      width = width ? Math.min(parseInt(width * (screenWidth / 750) * pixelRatio, 10), maxWidth) : maxWidth;
      if (!url) return;;
      if (url.indexOf("images.qmai.cn") === -1) return (url);;
      if (url.indexOf(".gif") > -1) return (url);;
      if (url.indexOf(".webp") > -1) return (url);;
      if (url.indexOf(".avif") > -1) return (url);;
      if (url.indexOf("?x-oss-process\x3d") > -1 || url.indexOf("\x26x-oss-process\x3d") > -1) return (url);;
      var hasQuery = url.indexOf("?") > -1;
      var baseRule = hasQuery ? "\x26x-oss-process\x3dimage" : "?x-oss-process\x3dimage";
      if (width && !isNaN(width)) {
        baseRule += "/resize,w_".concat(width, ",type_6");
        baseRule += "/sharpen,1"
      };
      if (imageFormat === "webp" || imageFormat === "avif") {
        baseRule += "/format,webp"
      };
      return (url + baseRule)
    };
    module.exports = ({
      s: s,
    })
  }))]);
  var __webpack_module_cache__ = ({});

  function __webpack_require__(moduleId) {
    var cachedModule = __webpack_module_cache__[((nt_0 = (moduleId), null == nt_0 ? undefined : 'number' === typeof nt_0 ? nt_0 : "" + nt_0))];
    if (cachedModule !== undefined) {
      return (cachedModule.exports)
    };
    var module = __webpack_module_cache__[((nt_1 = (moduleId), null == nt_1 ? undefined : 'number' === typeof nt_1 ? nt_1 : "" + nt_1))] = ({
      exports: ({}),
    });
    __webpack_modules__[((nt_2 = (moduleId), null == nt_2 ? undefined : 'number' === typeof nt_2 ? nt_2 : "" + nt_2))](module, module.exports, __webpack_require__);
    return (module.exports)
  };
  var __webpack_exports__ = __webpack_require__(0);
  return (__webpack_exports__ && __webpack_exports__.__esModule ? __webpack_exports__[("" + "default")] : __webpack_exports__)
}))();