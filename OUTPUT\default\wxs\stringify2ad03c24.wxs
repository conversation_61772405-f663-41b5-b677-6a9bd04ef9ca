module.exports = ((function() {
  var __webpack_modules__ = ([((function(module) {
    function objectKeys(obj) {
      if (false)({});
      else {
        var keys = [];
        var stackMap = ({
          "{": "}",
          "[": "]",
          "(": ")",
        });
        if (typeof obj === "object") {
          var objStr = JSON.stringify(obj);
          if (objStr[(0)] === "{" && objStr[((nt_1 = (objStr.length - 1), null == nt_1 ? undefined : 'number' === typeof nt_1 ? nt_1 : "" + nt_1))] === "}") {
            var inKey = true;
            var stack = [];
            var keyStart = 0;
            var keyEnd = 0;
            for (var i = 1; i < objStr.length - 1; i++) {
              var item = objStr[((nt_2 = (i), null == nt_2 ? undefined : 'number' === typeof nt_2 ? nt_2 : "" + nt_2))];
              var lastItem = objStr[((nt_3 = (i - 1), null == nt_3 ? undefined : 'number' === typeof nt_3 ? nt_3 : "" + nt_3))];
              if (inKey) {
                if (item === ":" && keyEnd === i - 1) {
                  keys.push(objStr.slice(keyStart + 1, keyEnd));
                  inKey = false
                } else {
                  if (item === '\x22' && lastItem !== "\\x5c") {
                    keyStart = keyEnd;
                    keyEnd = i
                  }
                }
              } else {
                if (stackMap[((nt_4 = (item), null == nt_4 ? undefined : 'number' === typeof nt_4 ? nt_4 : "" + nt_4))]) {
                  stack.push(item)
                } else if (stackMap[((nt_5 = (stack[((nt_6 = (stack.length - 1), null == nt_6 ? undefined : 'number' === typeof nt_6 ? nt_6 : "" + nt_6))]), null == nt_5 ? undefined : 'number' === typeof nt_5 ? nt_5 : "" + nt_5))] === item) {
                  stack.pop()
                } else if (stack.length === 0 && item === ",") {
                  inKey = true
                }
              }
            }
          }
        };
        return (keys)
      }
    };

    function genRegExp(str, flags) {
      if (false)({});
      else {
        return (getRegExp(str, flags))
      }
    };

    function extend(target, from) {
      var fromKeys = objectKeys(from);
      for (var i = 0; i < fromKeys.length; i++) {
        var key = fromKeys[((nt_7 = (i), null == nt_7 ? undefined : 'number' === typeof nt_7 ? nt_7 : "" + nt_7))];
        target[((nt_8 = (key), null == nt_8 ? undefined : 'number' === typeof nt_8 ? nt_8 : "" + nt_8))] = from[((nt_9 = (key), null == nt_9 ? undefined : 'number' === typeof nt_9 ? nt_9 : "" + nt_9))]
      };
      return (target)
    };

    function concat(a, b) {
      return (a ? b ? a + " " + b : a : b || "")
    };

    function isObject(obj) {
      return (obj !== null && typeof obj === "object")
    };

    function isArray(arr) {
      if (false)({});
      else {
        return (arr && arr.constructor === "Array")
      }
    };
    var escapeMap = ({
      "(": "_pl_",
      ")": "_pr_",
      "[": "_bl_",
      "]": "_br_",
      "{": "_cl_",
      "#": "_h_",
      "!": "_i_",
      "/": "_s_",
      ".": "_d_",
      ":": "_c_",
      ",": "_2c_",
      "%": "_p_",
      "\x27": "_q_",
      '\x22': "_dq_",
      "+": "_a_",
      "$": "_si_",
    });
    var escapeReg = genRegExp("[()[]{}#!/.:,%\x27\\x22+$]", "g");

    function mpEscape(str) {
      return (str.replace(escapeReg, (function(match) {
        if (escapeMap[((nt_10 = (match), null == nt_10 ? undefined : 'number' === typeof nt_10 ? nt_10 : "" + nt_10))]) return (escapeMap[((nt_11 = (match), null == nt_11 ? undefined : 'number' === typeof nt_11 ? nt_11 : "" + nt_11))]);;
        if (match === "}") return ("_cr_");;
        return ("_u_")
      })))
    };

    function stringifyDynamicClass(value) {
      if (isArray(value)) {
        value = stringifyArray(value)
      } else if (isObject(value)) {
        value = stringifyObject(value)
      };
      if (typeof value === "string") {
        return (value)
      } else {
        return ("")
      }
    };

    function stringifyArray(value) {
      var res = "";
      var classString;
      for (var i = 0; i < value.length; i++) {
        if (classString = stringifyDynamicClass(value[((nt_12 = (i), null == nt_12 ? undefined : 'number' === typeof nt_12 ? nt_12 : "" + nt_12))])) {
          if (res) res += " ";;
          res += classString
        }
      };
      return (res)
    };
    var mpxEscapeReg = genRegExp("(.+)MpxEscape$");
    var dashEscapeReg = genRegExp("_da_", "g");
    var spaceEscapeReg = genRegExp("_sp_", "g");

    function stringifyObject(value) {
      var res = "";
      var objKeys = objectKeys(value);
      for (var i = 0; i < objKeys.length; i++) {
        var key = objKeys[((nt_13 = (i), null == nt_13 ? undefined : 'number' === typeof nt_13 ? nt_13 : "" + nt_13))];
        if (value[((nt_14 = (key), null == nt_14 ? undefined : 'number' === typeof nt_14 ? nt_14 : "" + nt_14))]) {
          if (res) res += " ";;
          if (mpxEscapeReg.test(key)) {
            key = mpxEscapeReg.exec(key)[(1)].replace(dashEscapeReg, "-").replace(spaceEscapeReg, " ")
          };
          res += key
        }
      };
      return (res)
    };

    function hump2dash(value) {
      var reg = genRegExp("[A-Z]", "g");
      return (value.replace(reg, (function(match) {
        return ("-" + match.toLowerCase())
      })))
    };

    function dash2hump(value) {
      var reg = genRegExp("-([a-z])", "g");
      return (value.replace(reg, (function(match, p1) {
        return (p1.toUpperCase())
      })))
    };

    function parseStyleText(cssText) {
      var res = ({});
      var listDelimiter = genRegExp(";(?![^(]*[)])", "g");
      var propertyDelimiter = genRegExp(":(.+)");
      var arr = cssText.split(listDelimiter);
      for (var i = 0; i < arr.length; i++) {
        var item = arr[((nt_16 = (i), null == nt_16 ? undefined : 'number' === typeof nt_16 ? nt_16 : "" + nt_16))];
        if (item) {
          var tmp = item.split(propertyDelimiter);
          if (tmp.length > 1) {
            var k = dash2hump(tmp[(0)].trim());
            res[((nt_18 = (k), null == nt_18 ? undefined : 'number' === typeof nt_18 ? nt_18 : "" + nt_18))] = tmp[(1)].trim()
          }
        }
      };
      return (res)
    };

    function genStyleText(styleObj) {
      var res = "";
      var objKeys = objectKeys(styleObj);
      for (var i = 0; i < objKeys.length; i++) {
        var key = objKeys[((nt_20 = (i), null == nt_20 ? undefined : 'number' === typeof nt_20 ? nt_20 : "" + nt_20))];
        var item = styleObj[((nt_21 = (key), null == nt_21 ? undefined : 'number' === typeof nt_21 ? nt_21 : "" + nt_21))];
        res += hump2dash(key) + ":" + item + ";"
      };
      return (res)
    };

    function mergeObjectArray(arr) {
      var res = ({});
      for (var i = 0; i < arr.length; i++) {
        if (arr[((nt_22 = (i), null == nt_22 ? undefined : 'number' === typeof nt_22 ? nt_22 : "" + nt_22))]) {
          extend(res, arr[((nt_23 = (i), null == nt_23 ? undefined : 'number' === typeof nt_23 ? nt_23 : "" + nt_23))])
        }
      };
      return (res)
    };

    function normalizeDynamicStyle(value) {
      if (!value) return (({}));;
      if (isArray(value)) {
        return (mergeObjectArray(value))
      };
      if (typeof value === "string") {
        return (parseStyleText(value))
      };
      return (value)
    };
    module.exports = ({
      stringifyClass: function stringifyClass(staticClass, dynamicClass) {
        if (typeof staticClass !== "string") {
          return (console.log("Template attr class must be a string!"))
        };
        return (concat(staticClass, mpEscape(stringifyDynamicClass(dynamicClass))))
      },
      stringifyStyle: function stringifyStyle(staticStyle, dynamicStyle) {
        var normalizedDynamicStyle = normalizeDynamicStyle(dynamicStyle);
        var parsedStaticStyle = typeof staticStyle === "string" ? parseStyleText(staticStyle) : ({});
        return (genStyleText(extend(parsedStaticStyle, normalizedDynamicStyle)))
      },
    })
  }))]);
  var __webpack_module_cache__ = ({});

  function __webpack_require__(moduleId) {
    var cachedModule = __webpack_module_cache__[((nt_24 = (moduleId), null == nt_24 ? undefined : 'number' === typeof nt_24 ? nt_24 : "" + nt_24))];
    if (cachedModule !== undefined) {
      return (cachedModule.exports)
    };
    var module = __webpack_module_cache__[((nt_25 = (moduleId), null == nt_25 ? undefined : 'number' === typeof nt_25 ? nt_25 : "" + nt_25))] = ({
      exports: ({}),
    });
    __webpack_modules__[((nt_26 = (moduleId), null == nt_26 ? undefined : 'number' === typeof nt_26 ? nt_26 : "" + nt_26))](module, module.exports, __webpack_require__);
    return (module.exports)
  };
  var __webpack_exports__ = __webpack_require__(0);
  return (__webpack_exports__ && __webpack_exports__.__esModule ? __webpack_exports__[("" + "default")] : __webpack_exports__)
}))();