# 爷爷不泡茶小程序自动签到脚本

## 📋 功能说明

本脚本实现了爷爷不泡茶小程序的自动登录和签到功能，基于对小程序源码的逆向分析，完全破解了签名算法。

### 🎯 主要功能

1. **自动登录** - 通过微信授权码和手机号授权自动登录
2. **自动签到** - 每日自动执行签到任务
3. **多账号支持** - 支持批量处理多个微信账号
4. **智能缓存** - 登录状态缓存，避免重复登录
5. **签名算法** - 完全破解的签名生成算法
6. **通知推送** - 支持多种通知方式

## 📁 文件说明

- `爷爷不泡茶_签到版.js` - 主脚本文件，包含完整的登录和签到逻辑
- `wxcode.js` - 微信授权模块，处理微信相关的授权操作
- `test_signin.js` - 测试脚本，用于验证签名算法和接口调用
- `signature_generator_final.js` - 独立的签名生成器
- `1.txt` - 抓包数据，包含真实的HTTP请求示例

## 🔧 环境要求

- Node.js 12.0+
- npm 包：`request`

## 📦 安装依赖

```bash
npm install request
```

## ⚙️ 配置说明

### 1. 基础配置

在脚本中修改以下配置：

```javascript
const APPID = 'wx3423ef0c7b7f19af'; // 爷爷不泡茶小程序appid
const STORE_ID = '216652'; // 店铺ID
const ACTIVITY_ID = '1146457634812837889'; // 签到活动ID
```

### 2. 微信授权服务配置

设置环境变量：
```bash
export PHONECODE_SERVER="http://your-server:8800"
```

或在 `wxcode.js` 中修改：
```javascript
let xieyi = process.env.PHONECODE_SERVER || 'http://820121.xyz:8800';
```

### 3. 账号配置

设置环境变量：
```bash
export TXX_WXID="wxid1
wxid2
wxid3"
```

或使用命令行参数：
```bash
node 爷爷不泡茶_签到版.js --wxid your_wxid
```

## 🚀 使用方法

### 1. 单账号运行

```bash
node 爷爷不泡茶_签到版.js --wxid your_wxid
```

### 2. 多账号运行

```bash
# 设置环境变量后运行
export TXX_WXID="wxid1
wxid2
wxid3"
node 爷爷不泡茶_签到版.js
```

### 3. 调试模式

```bash
node 爷爷不泡茶_签到版.js --wxid your_wxid --debug
```

### 4. 测试签到功能

```bash
# 需要先在test_signin.js中配置真实的token和userId
node test_signin.js
```

## 🔐 签名算法详解

### 算法原理

1. **密钥生成**：将 `activityId` 字符串反转作为密钥
2. **参数构建**：构建包含 `activityId`、`sellerId`、`timestamp`、`userId` 的参数对象
3. **字典序排序**：按参数名进行字典序排序
4. **字符串拼接**：按 `key=value&key=value` 格式拼接
5. **添加密钥**：在末尾添加 `&key=反转的activityId`
6. **MD5加密**：对整个字符串进行MD5加密并转大写

### 示例

```javascript
// 输入参数
activityId: "1146457634812837889"
sellerId: "216652"
timestamp: "1753847631850"
userId: "1157283994981785601"

// 密钥（反转的activityId）
key: "9887382184367546411"

// 签名字符串
signString: "activityId=1146457634812837889&sellerId=216652&timestamp=1753847631850&userId=1157283994981785601&key=9887382184367546411"

// 最终签名
signature: "50B59044AA7561A0CD79C109567E072E"
```

## 📊 运行流程

1. **初始化** - 读取配置和账号列表
2. **缓存检查** - 检查是否有有效的登录缓存
3. **微信授权** - 获取微信授权码
4. **手机授权** - 获取手机号授权码
5. **用户登录** - 使用授权码登录获取token
6. **签到检查** - 检查签到活动状态
7. **执行签到** - 生成签名并执行签到
8. **结果通知** - 发送签到结果通知

## 🔍 调试信息

开启调试模式后，脚本会输出详细的调试信息：

- 微信授权码获取过程
- 手机号授权码获取过程
- 登录请求和响应
- 签名生成过程
- 签到请求和响应
- 缓存操作详情

## ⚠️ 注意事项

1. **合法使用** - 请确保在合法合规的前提下使用本脚本
2. **频率控制** - 避免过于频繁的请求，建议每日运行一次
3. **账号安全** - 妥善保管微信账号信息，避免泄露
4. **服务依赖** - 脚本依赖第三方微信授权服务，请确保服务可用
5. **活动有效性** - 签到活动可能会变更，需要及时更新活动ID

## 🐛 常见问题

### 1. 获取授权码失败
- 检查微信授权服务是否正常
- 确认wxid格式是否正确
- 检查网络连接

### 2. 登录失败
- 确认手机号授权码是否有效
- 检查店铺ID是否正确
- 验证小程序appid

### 3. 签到失败
- 确认活动ID是否正确
- 检查签名算法是否正确
- 验证用户token是否有效

### 4. 签名验证失败
- 检查参数顺序是否正确
- 确认MD5算法实现
- 验证密钥生成逻辑

## 📝 更新日志

### v2.0 (2025-07-30)
- 完全破解签名算法
- 集成登录和签到功能
- 支持多账号批量处理
- 添加智能缓存机制
- 完善错误处理和调试信息

### v1.0 (2025-07-21)
- 基础框架搭建
- 微信授权功能
- 缓存机制实现

## 📞 技术支持

如有问题或建议，请通过以下方式联系：

- 提交Issue到项目仓库
- 发送邮件到技术支持邮箱

## 📄 免责声明

本脚本仅供学习和研究使用，使用者需自行承担使用风险。作者不对因使用本脚本而产生的任何问题负责。
