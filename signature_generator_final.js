const crypto = require('crypto');

/**
 * 签到接口签名生成器 - 完整实现
 * 基于对霸王茶姬小程序源码的逆向分析
 */

// MD5加密函数
function MD5(str) {
  return crypto.createHash('md5').update(str).digest('hex');
}

/**
 * 生成签到接口签名
 * @param {string} activityId - 活动ID
 * @param {string} storeId - 店铺ID (sellerId)
 * @param {string} timestamp - 时间戳 (13位毫秒)
 * @param {string} userId - 用户ID
 * @returns {string} 32位大写MD5签名
 */
function generateSignature(activityId, storeId, timestamp, userId) {
  // 1. 将activityId反转作为密钥
  const key = activityId.split("").reverse().join("");
  
  // 2. 构建参数对象
  const params = {
    activityId: activityId,
    sellerId: storeId.toString(),
    timestamp: timestamp,
    userId: userId
  };
  
  // 3. 按字典序排序参数
  const sortedParams = Object.keys(params)
    .sort()
    .reduce((result, key) => {
      result[key] = params[key];
      return result;
    }, {});
  
  // 4. 拼接参数字符串
  const paramString = Object.entries(sortedParams)
    .map(([key, value]) => `${key}=${value}`)
    .join("&");
  
  // 5. 添加密钥
  const signString = `${paramString}&key=${key}`;
  
  // 6. MD5加密并转大写
  return MD5(signString).toUpperCase();
}

/**
 * 生成完整的签到请求数据
 * @param {string} activityId - 活动ID
 * @param {string} storeId - 店铺ID
 * @param {string} userId - 用户ID
 * @param {string} appid - 微信小程序ID
 * @returns {object} 完整的请求数据
 */
function generateSignInRequest(activityId, storeId, userId, appid = "wx3423ef0c7b7f19af") {
  const timestamp = String(Date.now());
  const signature = generateSignature(activityId, storeId, timestamp, userId);
  
  return {
    activityId: activityId,
    storeId: storeId,
    appid: appid,
    timestamp: timestamp,
    signature: signature,
    v: 1,
    data: "加密数据需要单独处理", // 这部分需要AES加密
    version: 1
  };
}

// 验证示例
console.log("=== 签名生成器验证 ===");

const testData = {
  activityId: "1146457634812837889",
  storeId: "216652",
  timestamp: "1753847631850",
  userId: "1157283994981785601"
};

const signature = generateSignature(
  testData.activityId,
  testData.storeId,
  testData.timestamp,
  testData.userId
);

console.log("生成的签名:", signature);
console.log("目标签名:", "50B59044AA7561A0CD79C109567E072E");
console.log("验证结果:", signature === "50B59044AA7561A0CD79C109567E072E" ? "✅ 成功" : "❌ 失败");

// 生成新的签到请求示例
console.log("\n=== 生成新的签到请求 ===");
const newRequest = generateSignInRequest(
  testData.activityId,
  testData.storeId,
  testData.userId
);

console.log("新请求数据:", JSON.stringify(newRequest, null, 2));

// 导出函数
module.exports = {
  generateSignature,
  generateSignInRequest
};

/**
 * 算法总结：
 * 
 * 1. 密钥生成：将activityId字符串反转
 * 2. 参数构建：{ activityId, sellerId, timestamp, userId }
 * 3. 字典序排序：按参数名排序
 * 4. 字符串拼接：key=value&key=value格式
 * 5. 添加密钥：&key=反转的activityId
 * 6. MD5加密：转大写输出
 * 
 * 关键发现：
 * - 参数名是sellerId而不是storeId
 * - userId是必需参数，值为"1157283994981785601"
 * - 密钥是activityId的反转字符串
 * - 最终输出为32位大写MD5值
 */
