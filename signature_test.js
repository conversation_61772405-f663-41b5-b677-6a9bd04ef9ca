const crypto = require('crypto');

// MD5加密函数
function MD5(str) {
  return crypto.createHash('md5').update(str).digest('hex');
}

// 签名生成函数
function generateSignature(activityId, storeId, timestamp, userId) {
  // 1. 反转activityId作为密钥
  const key = activityId.split("").reverse().join("");
  console.log("密钥 (反转的activityId):", key);
  
  // 2. 构建参数对象
  const params = {
    activityId: activityId,
    sellerId: storeId.toString(),
    timestamp: timestamp,
    userId: userId
  };
  console.log("原始参数:", params);
  
  // 3. 按字典序排序
  const sortedParams = Object.keys(params)
    .sort()
    .reduce((result, key) => {
      result[key] = params[key];
      return result;
    }, {});
  console.log("排序后参数:", sortedParams);
  
  // 4. 拼接参数字符串
  const paramString = Object.entries(sortedParams)
    .map(([key, value]) => `${key}=${value}`)
    .join("&");
  console.log("参数字符串:", paramString);
  
  // 5. 添加密钥
  const signString = `${paramString}&key=${key}`;
  console.log("签名字符串:", signString);
  
  // 6. MD5加密并转大写
  const signature = MD5(signString).toUpperCase();
  console.log("生成的签名:", signature);
  
  return signature;
}

// 测试数据（从请求中提取的实际数据）
const testData = {
  activityId: "1146457634812837889",
  storeId: "216652",
  timestamp: "1753847631850",
  // 我们需要找到实际的userId
  userId: "假设的用户ID" // 这个需要从实际请求中获取
};

console.log("=== 签名生成验证测试 ===");
console.log("目标签名:", "50B59044AA7561A0CD79C109567E072E");
console.log("");

// 测试不同的参数组合
const testCases = [
  // 测试不同的userId值
  { userId: "", desc: "空userId" },
  { userId: "216652", desc: "使用storeId作为userId" },
  { userId: "1146457634812837889", desc: "使用activityId作为userId" },

  // 测试不包含userId的情况
  { excludeUserId: true, desc: "不包含userId参数" },

  // 测试参数名变化
  { useStoreId: true, userId: "", desc: "使用storeId而不是sellerId" },
];

// 修改签名生成函数以支持不同参数组合
function generateSignatureFlexible(activityId, storeId, timestamp, options = {}) {
  const key = activityId.split("").reverse().join("");
  console.log("密钥 (反转的activityId):", key);

  const params = {
    activityId: activityId,
    timestamp: timestamp,
  };

  // 根据选项添加不同参数
  if (options.useStoreId) {
    params.storeId = storeId.toString();
  } else {
    params.sellerId = storeId.toString();
  }

  if (!options.excludeUserId) {
    params.userId = options.userId || "";
  }

  console.log("原始参数:", params);

  const sortedParams = Object.keys(params)
    .sort()
    .reduce((result, key) => {
      result[key] = params[key];
      return result;
    }, {});
  console.log("排序后参数:", sortedParams);

  const paramString = Object.entries(sortedParams)
    .map(([key, value]) => `${key}=${value}`)
    .join("&");
  console.log("参数字符串:", paramString);

  const signString = `${paramString}&key=${key}`;
  console.log("签名字符串:", signString);

  const signature = MD5(signString).toUpperCase();
  console.log("生成的签名:", signature);

  return signature;
}

testCases.forEach((testCase, index) => {
  console.log(`\n--- 测试 ${index + 1}: ${testCase.desc} ---`);
  const result = generateSignatureFlexible(
    testData.activityId,
    testData.storeId,
    testData.timestamp,
    testCase
  );

  const isMatch = result === "50B59044AA7561A0CD79C109567E072E";
  console.log("匹配结果:", isMatch ? "✅ 匹配成功!" : "❌ 不匹配");

  if (isMatch) {
    console.log("🎉 找到正确的参数组合:", testCase.desc);
  }
});
