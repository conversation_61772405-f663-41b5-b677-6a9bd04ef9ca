/**
 * 爷爷不泡茶签到功能测试脚本
 * 用于验证签名算法和接口调用
 */

const crypto = require('crypto');
const request = require('request');

// 配置信息
const CONFIG = {
    APPID: 'wx3423ef0c7b7f19af',
    STORE_ID: '216652',
    ACTIVITY_ID: '1146457634812837889',
    // 测试用的token和userId（需要替换为真实值）
    USER_TOKEN: 'iIJXUQt0YY0BRu-TulJR5Odyzt21XHbLd049Y3D4ZTolwvTsu7RjDHRAaTMdtxwQb_Gs6zZX5q_YN-SLjeRnwg',
    USER_ID: '1157283994981785601'
};

// MD5加密函数
function MD5(str) {
    return crypto.createHash('md5').update(str).digest('hex');
}

// 签名生成函数
function generateSignature(activityId, storeId, timestamp, userId) {
    // 1. 将activityId反转作为密钥
    const key = activityId.split("").reverse().join("");
    
    // 2. 构建参数对象
    const params = {
        activityId: activityId,
        sellerId: storeId.toString(),
        timestamp: timestamp,
        userId: userId
    };
    
    // 3. 按字典序排序参数
    const sortedParams = Object.keys(params)
        .sort()
        .reduce((result, key) => {
            result[key] = params[key];
            return result;
        }, {});
    
    // 4. 拼接参数字符串
    const paramString = Object.entries(sortedParams)
        .map(([key, value]) => `${key}=${value}`)
        .join("&");
    
    // 5. 添加密钥
    const signString = `${paramString}&key=${key}`;
    
    // 6. MD5加密并转大写
    return MD5(signString).toUpperCase();
}

// HTTP请求函数
function httpRequest(options) {
    return new Promise((resolve, reject) => {
        request(options, (error, response, body) => {
            if (error) {
                reject(error);
            } else {
                try {
                    const data = typeof body === 'string' ? JSON.parse(body) : body;
                    resolve({ response, data });
                } catch (e) {
                    resolve({ response, data: body });
                }
            }
        });
    });
}

// 检查签到活动信息
async function checkActivityInfo() {
    console.log('🔍 检查签到活动信息...');
    
    const options = {
        url: 'https://webapi.qmai.cn/web/cmk-center/sign/activityInfo',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'qm-user-token': CONFIG.USER_TOKEN,
            'store-id': CONFIG.STORE_ID,
            'qm-from': 'wechat',
            'qm-from-type': 'catering',
            'accept': 'v=1.0'
        },
        body: JSON.stringify({
            activityId: CONFIG.ACTIVITY_ID,
            appid: CONFIG.APPID
        })
    };

    try {
        const { response, data } = await httpRequest(options);
        console.log('📋 活动信息响应:', JSON.stringify(data, null, 2));
        return data.code === 0;
    } catch (error) {
        console.log('❌ 检查活动信息失败:', error.message);
        return false;
    }
}

// 测试签到功能
async function testSignIn() {
    console.log('🎯 开始测试签到功能...');
    
    const timestamp = String(Date.now());
    const signature = generateSignature(CONFIG.ACTIVITY_ID, CONFIG.STORE_ID, timestamp, CONFIG.USER_ID);
    
    console.log('🔐 签名信息:');
    console.log(`  时间戳: ${timestamp}`);
    console.log(`  签名: ${signature}`);
    
    // 测试不同的签到数据格式
    const testCases = [
        {
            name: '不带data字段',
            data: {
                activityId: CONFIG.ACTIVITY_ID,
                storeId: CONFIG.STORE_ID,
                appid: CONFIG.APPID,
                timestamp: timestamp,
                signature: signature,
                v: 1,
                version: 1
            }
        },
        {
            name: '带空data字段',
            data: {
                activityId: CONFIG.ACTIVITY_ID,
                storeId: CONFIG.STORE_ID,
                appid: CONFIG.APPID,
                timestamp: timestamp,
                signature: signature,
                v: 1,
                data: "",
                version: 1
            }
        },
        {
            name: '带示例data字段',
            data: {
                activityId: CONFIG.ACTIVITY_ID,
                storeId: CONFIG.STORE_ID,
                appid: CONFIG.APPID,
                timestamp: timestamp,
                signature: signature,
                v: 1,
                data: "RbvsFVlYyI+JVj8uHHzZL895S4OEcpoFXW0UiaMpncg1sa7inrpFKDA9sJH7WT8impsHWzvGAlHiWUF3Khv4usTlpMJPVJ1B8/GilrwxYAo20FS4teRaLXCeJSAEvEoWqp5QEe69yeNMLsw+S0Ihkmm5rIWuQ+HabmhC64XXkonqBq12PogkjV5Fec/InbM+KoOWX6N3rFRFFUND7IGyv0dehtzNcYGo77pGTbb9DqQ=",
                version: 1
            }
        }
    ];

    for (let i = 0; i < testCases.length; i++) {
        const testCase = testCases[i];
        console.log(`\n📝 测试案例 ${i + 1}: ${testCase.name}`);
        
        const options = {
            url: 'https://webapi.qmai.cn/web/cmk-center/sign/takePartInSign',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'qm-user-token': CONFIG.USER_TOKEN,
                'store-id': CONFIG.STORE_ID,
                'qm-from': 'wechat',
                'qm-from-type': 'catering',
                'accept': 'v=1.0',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185'
            },
            body: JSON.stringify(testCase.data)
        };

        try {
            console.log('📤 请求数据:', JSON.stringify(testCase.data, null, 2));
            const { response, data } = await httpRequest(options);
            console.log('📥 响应数据:', JSON.stringify(data, null, 2));
            
            if (data.code === 0) {
                console.log('✅ 签到成功！');
                return true;
            } else if (data.message && data.message.includes('已签到')) {
                console.log('ℹ️ 今日已签到');
                return true;
            } else {
                console.log(`❌ 签到失败: ${data.message || '未知错误'}`);
            }
        } catch (error) {
            console.log(`❌ 请求失败: ${error.message}`);
        }

        // 测试案例间延迟
        if (i < testCases.length - 1) {
            console.log('⏳ 等待2秒后继续下一个测试...');
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }

    return false;
}

// 验证已知签名
function verifyKnownSignature() {
    console.log('🔍 验证已知签名...');
    
    const knownData = {
        activityId: "1146457634812837889",
        storeId: "216652",
        timestamp: "1753847631850",
        userId: "1157283994981785601",
        expectedSignature: "50B59044AA7561A0CD79C109567E072E"
    };
    
    const generatedSignature = generateSignature(
        knownData.activityId,
        knownData.storeId,
        knownData.timestamp,
        knownData.userId
    );
    
    console.log(`期望签名: ${knownData.expectedSignature}`);
    console.log(`生成签名: ${generatedSignature}`);
    console.log(`验证结果: ${generatedSignature === knownData.expectedSignature ? '✅ 通过' : '❌ 失败'}`);
    
    return generatedSignature === knownData.expectedSignature;
}

// 主函数
async function main() {
    console.log('🚀 爷爷不泡茶签到功能测试开始\n');
    
    // 1. 验证签名算法
    console.log('='.repeat(50));
    const signatureValid = verifyKnownSignature();
    if (!signatureValid) {
        console.log('❌ 签名验证失败，停止测试');
        return;
    }
    
    // 2. 检查活动信息
    console.log('\n' + '='.repeat(50));
    const activityValid = await checkActivityInfo();
    if (!activityValid) {
        console.log('⚠️ 活动信息检查失败，但继续测试签到');
    }
    
    // 3. 测试签到
    console.log('\n' + '='.repeat(50));
    const signInSuccess = await testSignIn();
    
    console.log('\n' + '='.repeat(50));
    console.log(`🎉 测试完成，签到${signInSuccess ? '成功' : '失败'}`);
}

// 执行测试
main().catch(console.error);
