GET /web/catering/design/configProfileKeyValueByCode?subTypeId=4NTPZGO8_3&type=catering.pageShareData&app=1&appid=wx3423ef0c7b7f19af h2
host: webapi.qmai.cn
promotion-code: 
work-wechat-userid: 
store-id: 216652
accept-language: zh-CN
work-staff-id: 
scene: 1145
qm-from-type: catering
multi-store-id: 
qm-user-token: iIJXUQt0YY0BRu-TulJR5Odyzt21XHbLd049Y3D4ZTolwvTsu7RjDHRAaTMdtxwQb_Gs6zZX5q_YN-SLjeRnwg
work-staff-name: 
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
qz-gtd: 
qm-from: wechat
content-type: application/json
accept: v=1.0
channelcode: 
xweb_xhr: 1
gdt-vid: 
sec-fetch-site: cross-site
sec-fetch-mode: cors
sec-fetch-dest: empty
referer: https://servicewechat.com/wx3423ef0c7b7f19af/77/page-frame.html
accept-encoding: gzip, deflate, br
priority: u=1, i



h2 200
date: Wed, 30 Jul 2025 03:53:47 GMT
content-type: application/json;charset=UTF-8
content-length: 446
set-cookie: acw_tc=ac11000117538476272652557e005fdc079caf882f8f83c2288787afaaea06;path=/;HttpOnly;Max-Age=1800
vary: Origin
vary: Access-Control-Request-Method
vary: Access-Control-Request-Headers
strict-transport-security: max-age=31536000

{"status":true,"code":"0","message":"ok","data":{"createdAt":"2023-06-12 11:09:59","id":"569948","key":"page_share_data","storeId":"216652","value":"{\"pageShareData\":{\"4NTPZGO8_3\":{\"pageShare\":true,\"pageShareEnable\":true,\"pageShareTitle\":\"\",\"customShareImgSrc\":\"https://images.qmai.cn/s216652/2024/10/17/b4f5964e2f523ef34b.jpg\",\"pageShareType\":\"square\",\"pageShareImgSrc\":\"\"}}}"},"trace_id":"pdW07302zUjFO5cab013c569e4e6b"}