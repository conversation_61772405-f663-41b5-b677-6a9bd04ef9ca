POST /web/cmk-center/sign/userSignStatistics h2
host: webapi.qmai.cn
content-length: 65
qm-user-token: iIJXUQt0YY0BRu-TulJR5Odyzt21XHbLd049Y3D4ZTolwvTsu7RjDHRAaTMdtxwQb_Gs6zZX5q_YN-SLjeRnwg
store-id: 216652
accept-language: zh-CN
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
qm-from: wechat
content-type: application/json
accept: v=1.0
xweb_xhr: 1
qm-from-type: catering
sec-fetch-site: cross-site
sec-fetch-mode: cors
sec-fetch-dest: empty
referer: https://servicewechat.com/wx3423ef0c7b7f19af/77/page-frame.html
accept-encoding: gzip, deflate, br
priority: u=1, i

{"activityId":"1146457634812837889","appid":"wx3423ef0c7b7f19af"}

h2 200
date: Wed, 30 Jul 2025 03:53:52 GMT
content-type: application/json;charset=UTF-8
set-cookie: acw_tc=ac11000117538476326593163e0055d7a10c633c968ad6540ad401e30ac2a7;path=/;HttpOnly;Max-Age=1800
vary: Accept-Encoding
vary: Origin
vary: Access-Control-Request-Method
vary: Access-Control-Request-Headers
vary: Origin
vary: Access-Control-Request-Method
vary: Access-Control-Request-Headers
content-encoding: br
strict-transport-security: max-age=31536000

{"code":0,"data":{"basicRewardSwitch":2,"nextRewardList":[{"attain":2,"imageUrl":"","rewardList":[{"entityId":"1135507431420715008","forwardUrl":"coupon","guidingClerk":"","imageUrl":"","rewardName":"整单88折券（签到有礼）","rewardShowExtra":{"expiredDateStr":"获得券当日开始 7个自然日内有效","type":2},"rewardType":1,"sendNum":1}],"signCalculateType":1,"signNum":3}],"nextSignDays":2,"rewardList":[{"attain":2,"imageUrl":"","rewardList":[{"entityId":"1135507431420715008","forwardUrl":"coupon","guidingClerk":"","imageUrl":"","rewardName":"整单88折券（签到有礼）","rewardShowExtra":{"expiredDateStr":"获得券当日开始 7个自然日内有效","type":2},"rewardType":1,"sendNum":1}],"signCalculateType":1,"signNum":3},{"attain":2,"imageUrl":"","rewardList":[{"entityId":"1135508134880993281","forwardUrl":"coupon","guidingClerk":"","imageUrl":"","rewardName":"整单85折券（签到有礼）","rewardShowExtra":{"expiredDateStr":"获得券当日开始 7个自然日内有效","type":2},"rewardType":1,"sendNum":1}],"signCalculateType":1,"signNum":7},{"attain":2,"imageUrl":"","rewardList":[{"entityId":"1135508411900833793","forwardUrl":"coupon","guidingClerk":"","imageUrl":"","rewardName":"整单8折券（签到有礼）","rewardShowExtra":{"expiredDateStr":"获得券当日开始 7个自然日内有效","type":2},"rewardType":1,"sendNum":1}],"signCalculateType":1,"signNum":15},{"attain":2,"imageUrl":"","rewardList":[{"entityId":"1135509320174592000","forwardUrl":"coupon","guidingClerk":"","imageUrl":"","rewardName":"双杯77折（签到有礼）","rewardShowExtra":{"expiredDateStr":"获得券当日开始 7个自然日内有效","type":2},"rewardType":1,"sendNum":1}],"signCalculateType":1,"signNum":21},{"attain":2,"imageUrl":"","rewardList":[{"entityId":"1123296319913017344","forwardUrl":"coupon","guidingClerk":"","imageUrl":"","rewardName":"全场饮品第2杯免单券（签到有礼）","rewardShowExtra":{"expiredDateStr":"获得券当日开始 7个自然日内有效","type":4},"rewardType":1,"sendNum":1}],"signCalculateType":1,"signNum":30}],"signDays":1,"signStatus":1},"message":"ok","status":true,"trace_id":"pdW07302zWB2n5a87f6cffe2c4384"}