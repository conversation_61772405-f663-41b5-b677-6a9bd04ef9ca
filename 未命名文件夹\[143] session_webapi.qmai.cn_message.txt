POST /web/cmk-center/sign/userSignRecordCalendar h2
host: webapi.qmai.cn
content-length: 113
qm-user-token: iIJXUQt0YY0BRu-TulJR5Odyzt21XHbLd049Y3D4ZTolwvTsu7RjDHRAaTMdtxwQb_Gs6zZX5q_YN-SLjeRnwg
store-id: 216652
accept-language: zh-CN
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
qm-from: wechat
content-type: application/json
accept: v=1.0
xweb_xhr: 1
qm-from-type: catering
sec-fetch-site: cross-site
sec-fetch-mode: cors
sec-fetch-dest: empty
referer: https://servicewechat.com/wx3423ef0c7b7f19af/77/page-frame.html
accept-encoding: gzip, deflate, br
priority: u=1, i

{"activityId":"1146457634812837889","startDate":"2025-01-01","endDate":"2025-08-31","appid":"wx3423ef0c7b7f19af"}

h2 200
date: Wed, 30 Jul 2025 03:53:52 GMT
content-type: application/json;charset=UTF-8
content-length: 153
set-cookie: acw_tc=ac11000117538476326593164e0055c4dcbdc1d4f1a7fa5033c7b93972abef;path=/;HttpOnly;Max-Age=1800
vary: Origin
vary: Access-Control-Request-Method
vary: Access-Control-Request-Headers
vary: Origin
vary: Access-Control-Request-Method
vary: Access-Control-Request-Headers
strict-transport-security: max-age=31536000

{"code":0,"data":{"signDateList":[{"signDate":"2025-07-30","signRewardMode":1}]},"message":"ok","status":true,"trace_id":"pdW07302zWB4s3f3638ebccfc4363"}