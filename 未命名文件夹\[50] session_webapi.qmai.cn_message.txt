POST /web/account-center/design/config-profile h2
host: webapi.qmai.cn
content-length: 74
qm-user-token: AE4xSe1ViDngjcdR6bK8zoisE15u7m-kDKiyBFWm8b8fh9p_305tSa8uInKgu4cqereEtN1G4BipB31QbpYLYA
store-id: 216652
accept-language: zh-CN
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
qm-from: wechat
content-type: application/json
accept: v=1.0
xweb_xhr: 1
qm-from-type: catering
sec-fetch-site: cross-site
sec-fetch-mode: cors
sec-fetch-dest: empty
referer: https://servicewechat.com/wx3423ef0c7b7f19af/77/page-frame.html
accept-encoding: gzip, deflate, br
priority: u=1, i

{"app":1,"type":"catering.personalserviceV2","appid":"wx3423ef0c7b7f19af"}

h2 200
date: Wed, 30 Jul 2025 03:53:29 GMT
content-type: application/json;charset=UTF-8
content-length: 94
set-cookie: acw_tc=ac11000117538476098207321e0054297cb99c5ef35942008610fa0cc3afb1;path=/;HttpOnly;Max-Age=1800
vary: Origin
vary: Access-Control-Request-Method
vary: Access-Control-Request-Headers
strict-transport-security: max-age=31536000

{"code":0,"data":null,"message":"ok","status":true,"trace_id":"pdW07302zQ3Wm2d14fca326e946c9"}