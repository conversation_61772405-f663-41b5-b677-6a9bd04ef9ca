/**
 * 爷爷不泡茶小程序自动签到脚本
 * 作者：Tianxx
 * 版本：2.0 - 集成登录和签到功能
 * 日期：2025-07-30
 */
const NOTICE_SWITCH = 1; // 通知开关：1=开启，0=关闭
// 常量配置
const APPID = 'wx3423ef0c7b7f19af'; // 爷爷不泡茶小程序appid
const STORE_ID = '216652'; // 店铺ID
const ACTIVITY_ID = '1146457634812837889'; // 签到活动ID

// 解析命令行参数
const args = process.argv.slice(2);
const getArg = (name) => {
    const index = args.indexOf(`--${name}`);
    return index !== -1 && args[index + 1] ? args[index + 1] : null;
};

// 环境变量和命令行参数
const cmdWxid = getArg('wxid');
const isDebug = args.includes('--debug');
const wxidList = cmdWxid || process.env.TXX_WXID || '';

// 解析wxid列表的函数
function parseWxidList(wxidString) {
    if (!wxidString) return [];
    return wxidString
        .split('\n')                    
        .map(wxid => wxid.trim())       
        .filter(wxid => wxid.length > 0) 
        .filter(wxid => !wxid.startsWith('#')); 
}

// 引入模块
const wxcode = require('./wxcode');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const request = require('request');

// 获取脚本名称（不含扩展名）
const scriptName = path.basename(__filename, '.js');
// Token缓存文件路径
const TOKEN_CACHE_FILE = path.join(__dirname, `${scriptName}_tokens.json`);

// MD5加密函数
function MD5(str) {
    return crypto.createHash('md5').update(str).digest('hex');
}

// 签名生成函数
function generateSignature(activityId, storeId, timestamp, userId) {
    // 1. 将activityId反转作为密钥
    const key = activityId.split("").reverse().join("");
    
    // 2. 构建参数对象
    const params = {
        activityId: activityId,
        sellerId: storeId.toString(),
        timestamp: timestamp,
        userId: userId
    };
    
    // 3. 按字典序排序参数
    const sortedParams = Object.keys(params)
        .sort()
        .reduce((result, key) => {
            result[key] = params[key];
            return result;
        }, {});
    
    // 4. 拼接参数字符串
    const paramString = Object.entries(sortedParams)
        .map(([key, value]) => `${key}=${value}`)
        .join("&");
    
    // 5. 添加密钥
    const signString = `${paramString}&key=${key}`;
    
    // 6. MD5加密并转大写
    return MD5(signString).toUpperCase();
}

// HTTP请求函数
function httpRequest(options) {
    return new Promise((resolve, reject) => {
        request(options, (error, response, body) => {
            if (error) {
                reject(error);
            } else {
                try {
                    const data = typeof body === 'string' ? JSON.parse(body) : body;
                    resolve({ response, data });
                } catch (e) {
                    resolve({ response, data: body });
                }
            }
        });
    });
}

class YeYeBuPaoChaScript {
    constructor(wxid) {
        this.wxid = wxid;
        this.appid = APPID;
        this.storeId = STORE_ID;
        this.activityId = ACTIVITY_ID;
        this.isLogin = false;
        this.wxCode = null;
        this.phoneCode = null;
        this.userToken = null;
        this.userId = null;
        this.mobile = null;
        this.cacheExpireTime = null;
    }

    // 读取token缓存
    loadTokenCache() {
        try {
            if (fs.existsSync(TOKEN_CACHE_FILE)) {
                const cacheData = JSON.parse(fs.readFileSync(TOKEN_CACHE_FILE, 'utf8'));
                const userCache = cacheData[this.wxid];

                if (userCache && userCache.cacheExpireTime > Date.now()) {
                    this.userToken = userCache.userToken;
                    this.userId = userCache.userId;
                    this.mobile = userCache.mobile;
                    this.cacheExpireTime = userCache.cacheExpireTime;
                    this.isLogin = true;

                    if (isDebug) {
                        console.log(`[DEBUG] 从缓存加载数据成功`);
                        console.log(`[DEBUG] UserToken: ${this.userToken}`);
                        console.log(`[DEBUG] UserId: ${this.userId}`);
                        console.log(`[DEBUG] 缓存过期时间: ${new Date(this.cacheExpireTime).toLocaleString()}`);
                    }
                    return true;
                } else if (userCache) {
                    if (isDebug) console.log(`[DEBUG] 缓存数据已过期`);
                }
            }
        } catch (error) {
            if (isDebug) console.log(`[DEBUG] 读取缓存失败: ${error.message}`);
        }
        return false;
    }

    // 保存数据到缓存
    saveTokenCache() {
        try {
            let cacheData = {};

            // 读取现有缓存
            if (fs.existsSync(TOKEN_CACHE_FILE)) {
                try {
                    cacheData = JSON.parse(fs.readFileSync(TOKEN_CACHE_FILE, 'utf8'));
                } catch (e) {
                    if (isDebug) console.log(`[DEBUG] 现有缓存文件格式错误，将重新创建`);
                }
            }

            // 设置缓存过期时间（默认2小时）
            const expireTime = Date.now() + (2 * 60 * 60 * 1000);

            // 更新当前用户的缓存信息
            cacheData[this.wxid] = {
                userToken: this.userToken,
                userId: this.userId,
                mobile: this.mobile,
                cacheExpireTime: expireTime,
                updateTime: Date.now()
            };

            this.cacheExpireTime = expireTime;

            // 写入文件
            fs.writeFileSync(TOKEN_CACHE_FILE, JSON.stringify(cacheData, null, 2), 'utf8');

            if (isDebug) {
                console.log(`[DEBUG] 缓存保存成功`);
                console.log(`[DEBUG] 缓存文件: ${TOKEN_CACHE_FILE}`);
                console.log(`[DEBUG] 过期时间: ${new Date(expireTime).toLocaleString()}`);
            }
        } catch (error) {
            console.log(`❌ 保存缓存失败: ${error.message}`);
        }
    }

    // 获取微信授权码
    async getWxCode() {
        if (isDebug) console.log(`[DEBUG] 开始获取微信授权码...`);

        const codeResult = await wxcode.getWxCode(this.wxid, this.appid);
        if (!codeResult.success) {
            console.log(`获取授权码失败：${codeResult.error}`);
            return false;
        }

        this.wxCode = codeResult.code;
        if (isDebug) console.log(`[DEBUG] 获取授权码成功：${this.wxCode}`);
        return true;
    }

    // 获取手机号授权码
    async getPhoneCode() {
        if (isDebug) console.log(`[DEBUG] 开始获取手机号授权码...`);

        const result = await wxcode.getmobile(this.wxid, this.appid);
        if (!result.success) {
            console.log(`获取手机号授权码失败：${result.error}`);
            return false;
        }

        this.phoneCode = result.code;
        if (isDebug) console.log(`[DEBUG] 获取手机号授权码成功：${this.phoneCode}`);
        return true;
    }

    // 登录获取token
    async login() {
        if (isDebug) console.log(`[DEBUG] 开始登录...`);

        if (!this.phoneCode) {
            console.log(`❌ 手机号授权码为空，无法登录`);
            return false;
        }

        const loginData = {
            code: this.phoneCode,
            reg_activity_source: 0,
            is_update_mobile: 0,
            channel_code: "",
            flowScene: 1145,
            eVersion: "1.0",
            appid: this.appid
        };

        const options = {
            url: 'https://webapi.qmai.cn/web/account-center/oauth/bind-mobile',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'store-id': this.storeId,
                'qm-from': 'wechat',
                'qm-from-type': 'catering',
                'accept': 'v=1.0',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185'
            },
            body: JSON.stringify(loginData)
        };

        try {
            const { response, data } = await httpRequest(options);
            
            if (data.code === 0 && data.data && data.data.loginToken) {
                this.userToken = data.data.loginToken.token;
                this.userId = data.data.loginToken.user.id;
                this.mobile = data.data.mobile;
                this.isLogin = true;

                if (isDebug) {
                    console.log(`[DEBUG] 登录成功`);
                    console.log(`[DEBUG] UserToken: ${this.userToken}`);
                    console.log(`[DEBUG] UserId: ${this.userId}`);
                    console.log(`[DEBUG] Mobile: ${this.mobile}`);
                }

                print(`✅ [${this.wxid}] 登录成功，手机号：${this.mobile}`, true);
                return true;
            } else {
                console.log(`❌ 登录失败：${data.message || '未知错误'}`);
                return false;
            }
        } catch (error) {
            console.log(`❌ 登录请求失败：${error.message}`);
            return false;
        }
    }

    // 检查签到状态
    async checkSignStatus() {
        if (!this.isLogin || !this.userToken) {
            return { canSign: false, message: "未登录" };
        }

        const options = {
            url: 'https://webapi.qmai.cn/web/cmk-center/sign/activityInfo',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'qm-user-token': this.userToken,
                'store-id': this.storeId,
                'qm-from': 'wechat',
                'qm-from-type': 'catering',
                'accept': 'v=1.0'
            },
            body: JSON.stringify({
                activityId: this.activityId,
                appid: this.appid
            })
        };

        try {
            const { response, data } = await httpRequest(options);
            if (data.code === 0) {
                return {
                    canSign: true,
                    activityInfo: data.data,
                    message: "活动正常"
                };
            } else {
                return {
                    canSign: false,
                    message: data.message || "获取活动信息失败"
                };
            }
        } catch (error) {
            return {
                canSign: false,
                message: `检查签到状态失败：${error.message}`
            };
        }
    }

    // 执行签到
    async signIn() {
        if (!this.isLogin || !this.userToken || !this.userId) {
            console.log(`❌ 未登录，无法签到`);
            return false;
        }

        if (isDebug) console.log(`[DEBUG] 开始签到...`);

        // 先检查签到状态
        const statusCheck = await this.checkSignStatus();
        if (!statusCheck.canSign) {
            console.log(`❌ 签到检查失败：${statusCheck.message}`);
            return false;
        }

        const timestamp = String(Date.now());
        const signature = generateSignature(this.activityId, this.storeId, timestamp, this.userId);

        // 构建基础签到数据（不包含data字段，因为需要特殊处理）
        const signData = {
            activityId: this.activityId,
            storeId: this.storeId,
            appid: this.appid,
            timestamp: timestamp,
            signature: signature,
            v: 1,
            version: 1
        };

        // 尝试不带data字段的签到
        if (isDebug) {
            console.log(`[DEBUG] 签到数据:`, signData);
            console.log(`[DEBUG] 生成的签名: ${signature}`);
        }

        // 尝试多种签到方式
        const signMethods = [
            // 方法1：不带data字段
            { ...signData },
            // 方法2：带空data字段
            { ...signData, data: "" },
            // 方法3：带占位符data字段
            { ...signData, data: "RbvsFVlYyI+JVj8uHHzZL895S4OEcpoFXW0UiaMpncg1sa7inrpFKDA9sJH7WT8impsHWzvGAlHiWUF3Khv4usTlpMJPVJ1B8/GilrwxYAo20FS4teRaLXCeJSAEvEoWqp5QEe69yeNMLsw+S0Ihkmm5rIWuQ+HabmhC64XXkonqBq12PogkjV5Fec/InbM+KoOWX6N3rFRFFUND7IGyv0dehtzNcYGo77pGTbb9DqQ=" }
        ];

        for (let i = 0; i < signMethods.length; i++) {
            const currentSignData = signMethods[i];

            if (isDebug) {
                console.log(`[DEBUG] 尝试签到方法 ${i + 1}/${signMethods.length}`);
            }

            const options = {
                url: 'https://webapi.qmai.cn/web/cmk-center/sign/takePartInSign',
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'qm-user-token': this.userToken,
                    'store-id': this.storeId,
                    'qm-from': 'wechat',
                    'qm-from-type': 'catering',
                    'accept': 'v=1.0',
                    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185'
                },
                body: JSON.stringify(currentSignData)
            };

            try {
                const { response, data } = await httpRequest(options);

                if (data.code === 0) {
                    print(`🎉 [${this.wxid}] 签到成功！手机号：${this.mobile}`, true);
                    if (isDebug) {
                        console.log(`[DEBUG] 签到成功，使用方法 ${i + 1}`);
                        console.log(`[DEBUG] 签到响应:`, data);
                    }
                    return true;
                } else if (data.message && data.message.includes('已签到')) {
                    print(`ℹ️ [${this.wxid}] 今日已签到，手机号：${this.mobile}`, true);
                    return true;
                } else {
                    if (isDebug) {
                        console.log(`[DEBUG] 方法 ${i + 1} 失败：${data.message || '未知错误'}`);
                    }
                    // 如果不是最后一种方法，继续尝试下一种
                    if (i === signMethods.length - 1) {
                        print(`❌ [${this.wxid}] 签到失败：${data.message || '未知错误'}`, true);
                        return false;
                    }
                }
            } catch (error) {
                if (isDebug) {
                    console.log(`[DEBUG] 方法 ${i + 1} 请求失败：${error.message}`);
                }
                // 如果不是最后一种方法，继续尝试下一种
                if (i === signMethods.length - 1) {
                    print(`❌ [${this.wxid}] 签到请求失败：${error.message}`, true);
                    return false;
                }
            }

            // 方法间稍微延迟
            if (i < signMethods.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        return false;
    }

    // 主要业务逻辑
    async run() {
        try {
            // 1. 尝试从缓存加载数据
            const cacheLoaded = this.loadTokenCache();

            if (cacheLoaded) {
                console.log(`📦 使用缓存的登录数据`);
            } else {
                // 2. 缓存无效或不存在，进行完整登录
                console.log(`🔐 开始登录流程...`);
                
                // 获取微信授权码
                const wxCodeSuccess = await this.getWxCode();
                if (!wxCodeSuccess) {
                    print(`[${this.wxid}] 获取微信授权码失败，跳过`, true);
                    return;
                }

                // 获取手机号授权码
                const phoneCodeSuccess = await this.getPhoneCode();
                if (!phoneCodeSuccess) {
                    print(`[${this.wxid}] 获取手机号授权码失败，跳过`, true);
                    return;
                }

                // 登录
                const loginSuccess = await this.login();
                if (!loginSuccess) {
                    print(`[${this.wxid}] 登录失败，跳过`, true);
                    return;
                }

                // 保存到缓存
                this.saveTokenCache();
            }

            // 3. 执行签到
            await this.signIn();

        } catch (error) {
            print(`[${this.wxid}] 脚本执行出错：${error.message}`, true);
            if (isDebug) {
                console.error(error);
            }
        }
    }
}

// 主函数
async function main() {
    console.log(`🔔 爷爷不泡茶自动签到脚本开始执行`);
    
    if (isDebug) {
        console.log(`[DEBUG] 调试模式已开启`);
        console.log(`[DEBUG] APPID: ${APPID}`);
        console.log(`[DEBUG] STORE_ID: ${STORE_ID}`);
        console.log(`[DEBUG] ACTIVITY_ID: ${ACTIVITY_ID}`);
    }
    
    if (!wxidList) {
        console.log(`❌ 未设置环境变量 TXX_WXID 或命令行参数 --wxid`);
        return;
    }

    // 处理单个wxid或多个wxid
    const wxids = cmdWxid ? [cmdWxid] : parseWxidList(wxidList);

    if (wxids.length === 0) {
        console.log(`❌ 没有找到有效的wxid`);
        return;
    }

    console.log(`📋 共找到 ${wxids.length} 个有效账号`);

    if (isDebug) {
        console.log(`[DEBUG] 账号列表: ${wxids.join(', ')}`);
    }

    // 逐个处理账号
    for (let i = 0; i < wxids.length; i++) {
        const wxid = wxids[i];
        console.log(`\n🚀 [${i + 1}/${wxids.length}] 开始处理账号: ${wxid}`);

        try {
            const script = new YeYeBuPaoChaScript(wxid);
            await script.run();
            console.log(`✅ [${i + 1}/${wxids.length}] 账号 ${wxid} 处理完成`);
        } catch (error) {
            console.log(`❌ [${i + 1}/${wxids.length}] 账号 ${wxid} 处理失败: ${error.message}`);
            if (isDebug) {
                console.error(error);
            }
        }

        console.log('─'.repeat(60));

        // 如果不是最后一个账号，稍微延迟一下
        if (i < wxids.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }

    console.log(`\n🎉 所有账号处理完成！`);

    // 发送通知
    if (NOTICE_SWITCH && notice) {
        await sendMsg(notice);
    }
}

// 通知相关变量和函数
let notice = '';

function print(msg, is_notice = false) {
    let str = `${msg}`;
    console.log(str);
    if (NOTICE_SWITCH && is_notice) {
        notice += `${str}\n`;
    }
}

async function sendMsg(message) {
    try {
        let notify = '';
        try {
            notify = require('./sendNotify');
        } catch (e) {
            try {
                notify = require("../sendNotify");
            } catch (e2) {
                console.log('❌ 未找到sendNotify模块，无法发送通知');
                return;
            }
        }
        await notify.sendNotify(scriptName, message);
        console.log('📢 通知发送成功');
    } catch (error) {
        console.log(`❌ 通知发送失败: ${error.message}`);
    }
}

// 执行脚本
main().catch(console.error);
